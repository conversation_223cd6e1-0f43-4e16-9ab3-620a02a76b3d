'use strict';
(function () {
    angular
        .module('AkPadManager')
        .factory('AkPadApi', AkPadApi);

    AkPadApi.$inject = ['LMBAjaxService'];
    function AkPadApi(LMBAjaxService) {
        const apiBase = "page.php/AkPad/Standard/Standard/";
        let post = function (action, data = {}, useApiBase = true) {
            return LMBAjaxService.post((useApiBase ? apiBase : '') + action, data, function (response) {
                return response.data;
            });
        };
        return {
            getList: (data) => post('getList', data),
            create: (data) => post('create', data),
            update: (data) => post('update', data),
            desync: (data) => post('desync', data),
            delete: (data) => post('delete', data),
            config: (data) => post('config', data)
        }
    }
})();

'use strict';

(function() {

    var module = angular.module('ArticleSimple',['ui.router','ArticleSimpleFilters', 'article.SimpleServices', 'article.VisualisationServices','ArticleVisualisationStandard','ui.sortable','GenericServices', 'angularModalService','standardDirectives',
       'internationalisation.Directives','angularFileUpload','GenericServices','Upload', 'Pagination','ngJsTree', 'lmb']);

    module.controller('ArticleVisualCtrl', ['$scope', '$rootScope', 'ArticleService','ArticleVisualisationService', '$state', '$sce', 'LMBModalService', '$window', function($scope, $rootScope, ArticleService, ArticleVisualisationService, $state, $sce, LMBModalService, $window) {

        var $ = jQuery;

        $scope.init = function(id, page) {
            $scope.page = page;
            ArticleService.getDatas(id,['info_generales','tarifs_achats', 'tarifs_ventes', 'images', 'liaisons']).then(function(datas) {

                console.log(datas);
                $scope.config = datas.config;
                $scope.pieces_jointes = datas.pieces_jointes;
                $scope.article = datas.article;
                $scope.abonnements = datas.abonnements;
                $scope.credits = datas.credits;
                $scope.id_article = datas.article.info_generales.ref_article;
                $scope.is_parent = datas.article.info_generales.is_parent;
                $scope.article.tarifs_achats.current_tab_fournisseur = 1;
                $scope.article.tarifs_ventes.current_tab_grille = $scope.article.tarifs_ventes.grilles[0].id_tarif || 1;
                initReferencement();

                $scope.article.gestionAchat = $scope.article.tarifs_achats.tarifs.length > 0;

                $scope.article.info_generales.lib_article_complet = $scope.article.info_generales.lib_article;
                if($scope.article.info_generales.lib_marque) {
                    $scope.article.info_generales.lib_article_complet = $scope.article.info_generales.lib_marque + " - " + $scope.article.info_generales.lib_article_complet;
                }

                if($scope.article.info_generales.type === 'produit') {
                    $scope.article.info_generales.modele = 'service';
                }

                if($scope.article.info_generales.desc_courte_html) {
                    $scope.article.info_generales.desc_courte_html = $sce.trustAsHtml($scope.article.info_generales.desc_courte_html);
                }

                if($scope.article.info_generales.desc_longue_html) {
                    $scope.article.info_generales.desc_longue_html = $sce.trustAsHtml($scope.article.info_generales.desc_longue_html);
                }

                $scope.select_id_liaison_type = '1-has';

                $scope.checkFDV();

                $scope.statedata = {};

                $scope.user_list = datas.historique.user_list;
                $scope.histo_types = datas.historique.histo_types;

                $scope.text_modif_desc_longue=__(450273,"Modifier la description longue");
                $scope.text_modif_liaisons=__(450276,"Modifier les liaisons");

                if($state.current.name != $scope.page) {
                    $state.go($scope.page,{'idArticle': $scope.id_article});
                } else {
                    $scope.refresh($scope.page);
                }

                if (LMBConfig.onDevMode()) {
                    console.log("ID ARTICLE : " + datas.article.info_generales.id_article);
                }

		$scope.$watch(function() {
		    return $scope.article.images.list.length;
		}, function(newVal, oldVal) {
		    if(newVal > oldVal) {
			angular.forEach($scope.article.ecommerces, function(ecommerce) {
			    angular.forEach(ecommerce.seos, function(seo) {
				    updateSEOImageList(seo);
			    });
			});
		    }
		});

            });

        };

        function initReferencement() {
            $scope.article.ecommerces = $scope.config.ecommerces;
            angular.forEach($scope.article.seo_articles, function(seo) {
                seo.langage = getLangageById(seo.id_langage);
                updateSEOImageList(seo);

                var ecomerce = $scope.config.ecommerces[seo.id_ecommerce];
                if(ecomerce.seos === undefined) {
                    ecomerce.seos = [];
                }
                ecomerce.seos.push(seo);
            });
	    angular.forEach($scope.article.ecommerces, function(ecommerce){
		if(ecommerce.seos === undefined) {
		    ecommerce.seos = [];
		    var new_seo = {};
		    new_seo.id_article = $scope.article.info_generales.id_article;
		    new_seo.id_ecommerce = ecommerce.id_ecommerce;
		    new_seo.id_langage = $scope.config.langage_courant.id;
		    new_seo.langage = getLangageById(new_seo.id_langage);
		    new_seo.images = angular.copy($scope.article.images.list);
		    ecommerce.seos.push(new_seo);
		}
	    });
        }

        function getLangageById(id_langage) {
            var langages = $scope.config.all_langages;
            for(var i = 0; i < langages.length; i++) {
                if(langages[i].id_langage == id_langage) {
                    return langages[i];
                }
            }
        }

	function updateSEOImageList(seo) {
	    if(seo.images === undefined) {
		seo.images = angular.copy($scope.article.images.list);
	    }
	    else if(seo.images.length < $scope.article.images.list.length) {
		angular.forEach($scope.article.images.list, function(art_image) {
		    var hasImage = false;
		    for(var i=0; i<seo.images.length; i++) {
			if(art_image.id_image == seo.images[i].id_image) {
			    hasImage = true;
			    break;
			}
		    }
		    if(!hasImage) {
			seo.images.push(angular.copy(art_image));
		    }
		});
	    }
	}

        $rootScope.$on('$stateChangeSuccess',
        function(event, toState, toParams, fromState, fromParams){

            var callbackAfterStateData = function(toState) {
              if(toState.name.split(".").length > 1) {
                $scope.mySubData = $scope.statedata[toState.name];

                var rootState = toState.name.split(".")[0];
                ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article,rootState).then(function(datas){
                    $scope.statedata[rootState] = datas;
                    $scope.myData = $scope.statedata[rootState];
                });
              } else {
                $scope.myData = $scope.statedata[toState.name];
              }
            }
            $scope.$watch("article.info_generales.ref_article", function() {
                if (angular.isUndefined($scope.statedata[toState.name]) && angular.isDefined($scope.article)) {
                    ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article,toState.name).then(function(datas){
                        $scope.statedata[toState.name] = datas;

                        callbackAfterStateData(toState);
                    });
                } else {
                    callbackAfterStateData(toState);
                }
            })
        });

        $scope.refresh = function (statedata,data){
            ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article,statedata,data).then(function(datas){
                $scope.statedata[statedata] = datas;
            });
        }

        $scope.checkFDV = function() {
          if($scope.article) {
            var dateFin = new Date($scope.article.info_generales.date_fin_dispo_vente);
            var dateNow = new Date();
            $scope.article.info_generales.is_fdv = ($scope.article.info_generales.is_dispo && dateFin < dateNow);
          } else {
            $scope.article.info_generales.is_fdv = false;
          }
        }

         $scope.getTvaTauxAchat = function(id_tva_ha){
             if ($scope.config)
                return $scope.config.tvas_achat[id_tva_ha].taux || 0
         }

         $scope.getTvaTauxVente = function(id_tva){
             if ($scope.config)
                return $scope.config.tvas_vente[id_tva].taux || 0
         }

         $scope.getPrixTTC = function(p_ht,taux) {
             return p_ht * (1+taux/100);
         }

       $scope.articleTarifsQteList = function(){

           if (!$scope.article)
               return;
           var qte_list = ["1"];
           for(var i=0,l=$scope.article.tarifs_ventes.tarifs.length;i<l;i++){
               if (qte_list.indexOf($scope.article.tarifs_ventes.tarifs[i].indice_qte) === -1)
                qte_list.push($scope.article.tarifs_ventes.tarifs[i].indice_qte);
            }
        return qte_list;
       };

        $scope.ifObjectIsEmpty = function (object) {
            for(var key in object) {
                if(object.hasOwnProperty(key)){
                    return false;
                }
            }
            return true;
        };

        $scope.getStatutStock = function(){
            if(!$scope.article.info_generales.is_stockable){
                return false;
            }

            var statut = "";
            var id_stock = $scope.article.id_stock_magasin;

            var stock = $scope.statedata.stocks.InfosStocks.physique[id_stock] ? $scope.statedata.stocks.InfosStocks.physique[id_stock].qte : 0;
            var stock_rsv = $scope.statedata.stocks.InfosStocks.reserve[id_stock] ? ($scope.statedata.stocks.InfosStocks.reserve[id_stock].qte - $scope.statedata.stocks.InfosStocks.reserve[id_stock].qte_livree) : 0;
            var stock_dispo = stock-stock_rsv;

            if(stock_dispo > 0 && stock_dispo <= $scope.statedata.stocks.InfosStocks.stocks_liste[id_stock].mini) {
                statut = "insuffisant"
            }
            else if(stock_dispo <= 0) {
                statut = "indisponible";
            }
            else {
                statut = "en_stock";
            }

            return statut;
        };

        $scope.alertes = {};

        $scope.alertBientotDispo = function(){
            LMBTools.alert(__(100194,"Bientôt disponnible"));
        };
        /*
         * Convertit une valeur vers l’unité « de base » en utilisant un codec de conversion.
         * @param val La valeur à convertir
         * @param unit_codec Le codec de conversion (ex. : [VALUE]/1000)
         * @param pow (facultatif) puissance à utiliser : dans le cas d’un volume, il faut mettre le facteur à la puissance 3
         * @param reverse (facultatif) true = convertir dans l’autre sens (de l’unité « de base » vers l’unité passée en paramètre)
         *
         *   poids -> kg
         *   longueur -> cm
         */
        function conversionUnite(val, unit_codec, pow, reverse) {
            if(unit_codec.trim() != "[VALUE]") {
                var facteur = parseFloat(unit_codec.replace(/\[VALUE\][\/*]?/,"")); // efface [VALUE], [VALUE]/ ou [VALUE]*
                if(typeof pow === "number") {
                    facteur = Math.pow(facteur,pow);
                }
                if(unit_codec.indexOf("*") > 0) {
                    facteur = 1 / facteur;
                }
                if(reverse === true) {
                    facteur = 1 / facteur;
                }
                return val * facteur;
            } else {
                return parseFloat(val);
            }
        }

        $scope.recalc_colisage = function() {
            $scope.article.colisage.poids_total_colis = 0;
            $scope.article.colisage.volume_total_colis_cm3 = 0;
            $scope.article.colisage.volume_total_colis = 0;
            $scope.article.colisage.nb_colis = 0;
            if($scope.article.colisage.colis.length > 0) {
                $.each($scope.article.colisage.colis, function(k, colis){
                    if(colis.dimensions && colis.dimensions.hauteur && colis.dimensions.largeur && colis.dimensions.profondeur) {
                        colis.volume = colis.dimensions.hauteur * colis.dimensions.largeur * colis.dimensions.profondeur;
                        colis.dim = colis.dimensions.hauteur + "x" + colis.dimensions.largeur + "x" + colis.dimensions.profondeur;

                        $scope.article.colisage.volume_total_colis_cm3 += conversionUnite(
                            colis.volume, $scope.config.units.longueur[colis.id_dim].codec_conversion, 3);
                    }
                    colis.idDim = colis.id_dim;
                    colis.idPoids = colis.id_poids;

                    if(colis.poids) {
                        $scope.article.colisage.poids_total_colis += conversionUnite(colis.poids, $scope.config.units.poids[colis.id_poids].codec_conversion);
                    } else {
                        colis.poids = 0;
                    }
                    if(colis.dimensions || colis.poids) {
                        $scope.article.colisage.nb_colis += 1;
                    }
                });
                // Conversion du volume total dans l’unité du premier colis
                var id_dim_totale = $scope.article.colisage.colis[0].id_dim;
                $scope.article.colisage.volume_total_colis = conversionUnite($scope.article.colisage.volume_total_colis_cm3,
                    $scope.config.units.longueur[id_dim_totale].codec_conversion, 3, true);
                $scope.article.colisage.unit_volume_total = $scope.config.units.longueur[id_dim_totale].abrev;
            }
        };

        /**
         * Retourne le lib de la liaison tel qu'il est défini dans $scope.config.liaison_types
         * Par rapport à ceux dans $scope.article.liaisons, l'avantage est qu'on n'a pas les variables
         * comme %LIB_ARTICLE%
         *
         * @param id_liaison_type (int) - L'id du type de liaison
         * @param sens ('is'|'has') - Le sens de la liaison
         */
        $scope.getLibLiaison = function(id_liaison_type, sens) {
            let id_liaison = id_liaison_type + "-" + sens;
            let liaison = $scope.config.liaisons_types[id_liaison];
            if(typeof liaison != "undefined" && liaison) {
                return liaison.lib;
            } else {
                return "";
            }
        }

        /**
         * Retourne, parmi la liste de liaisons, le nombre de liaisons dans le sens choisi
         *
         * @param liste_liaisons (array) - La liste de liaisons à vérifier
         * @param liste_liaisons ('is'|'has') - Le sens des liaisons
         */
        $scope.countLiaisonsSens = function(liste_liaisons, sens) {
            let count = 0;
            liste_liaisons.forEach((liais) => {
                if(liais.sens == sens) {
                    count++;
                }
            });
            return count;
        }

        $scope.editEffets = function(liaison) {
            let params = JSON.parse(liaison.params);
            let table = `<table class="style-2">
							<tr>
								<th>Effet</th>
								<th>Quantité</th>
							</tr>`;
            table += liaison.effets.map(function(effet) {
                let param = params[effet.ref_art_liaison_effet_type] || {};
				if(effet.ref_art_liaison_effet_type == "DocVenteOptionRemplacement") {
					return `<tr qa_id="172447">
								<td class="text-left" qa_id="172437">` + effet.lib + `</td>
								<td class="text-left">
									<div class="input-group"qa_id="172441">
										<input type="text" class="input-xxsmall" name="` + effet.ref_art_liaison_effet_type + `_to" value="` + (param.ratio.to || 1) + `" lmb-number="type:floatPos" qa_id="172438"/>
										<span class="highlight">pour chaque</span>
										<input type="text" class="input-xxsmall" name="` + effet.ref_art_liaison_effet_type + `_from" value="` + (param.ratio.from || 1) + `" lmb-number="type:floatPos" qa_id="172450"/>
										<span class="highlight">présents</span>
									</div>
								</td>
							</tr>`;
				} else {
					let qte = param.qte;
					let prop = '';
					let prop_fix = '';
					let ratioRegex = /^([\d\.]*?)(x?)$/;
					let qteInfos = ratioRegex.exec(param.qte);

					if(qteInfos) {
						qte = parseFloat(qteInfos[1]);
						if(qteInfos[2]) {
							prop = 'checked="checked"';
						}
					}
                    if (prop == '') {
                        prop_fix = 'checked="checked"';
                    }
					return `<tr qa_id="172447">
								<td class="text-left" qa_id="172437">` + effet.lib + `</td>
								<td class="text-left">
									<input type="text" class="input-xsmall" name="` + effet.ref_art_liaison_effet_type + `" value="` + (qte || 1) + `"  lmb-number="type:floatPos" qa_id="172442"/>
                                    <qaid qa_id="172443">
                                        <div>
                                            <input type="radio" name="` + effet.ref_art_liaison_effet_type + `_prop" value="1" ` + prop + `/>
                                                <label for="Effet<?= $effet->ref_art_liaison_effet_type ?>_prop" class="qte_prop">
                                                    Quantité pour chaque article
                                                </label>
                                        </div>
                                        <div>
                                            <input type="radio" name="` + effet.ref_art_liaison_effet_type + `_prop" value="0" ` + prop_fix + `/>
                                            <label for="Effet<?= $effet->ref_art_liaison_effet_type ?>_prop" class="qte_prop">
                                                Quantité fixe
                                            </label>
                                        </div>         
                                    </qaid>
								</td>
							</tr>`;
				}
            }).join('');
            table += '</table>';
            LMBTools.confirm({
                title: 'Modifier les effets de la liaison',
                content: table,
                confirm: 'Enregistrer',
                onValid: function(confirm, args) {
                    if (confirm) {
						let ratio = {};
						// Si les cases 'quantité fixe' ne sont pas cochées on rajoute les 'x'
						if(typeof args['DocVenteOptionAjout_prop'] != "undefined" && args['DocVenteOptionAjout_prop'] != "0") {
							args['DocVenteOptionAjout'] = args['DocVenteOptionAjout'] + 'x';
							delete args['DocVenteOptionAjout_prop'];
						}
						if(typeof args['DocVenteAjoutAutomatique_prop'] != "undefined" && args['DocVenteAjoutAutomatique_prop'] != "0") {
							args['DocVenteAjoutAutomatique'] = args['DocVenteAjoutAutomatique'] + 'x';
							delete args['DocVenteAjoutAutomatique_prop'];
						}
                        Object.keys(args).forEach(function (ref) {
							if(ref == "DocVenteOptionRemplacement_from") {
								ratio.from = args[ref];
								delete args['DocVenteOptionRemplacement_from'];
							} else if(ref == "DocVenteOptionRemplacement_to") {
								ratio.to = args[ref];
								delete args['DocVenteOptionRemplacement_to'];
							} else {
								args[ref] = {qte: args[ref]};
							}
                        });
						if(Object.keys(ratio).length) {
							args['DocVenteOptionRemplacement'] = {ratio: ratio};
						}
                        ArticleVisualisationService.saveLiaisonParams(liaison.id, args).then(function(data) {
                            liaison.params = data;

                            // On met à jour la valeur des params dans $scope.article.liaisons
                            let id_liaison_type = liaison.id_article_liaison_type + "-" + liaison.sens;
                            let liaison_type = $scope.article.liaisons.find(function(liais) {
                                return liais.id_liaison_type == id_liaison_type;
                            });
                            if(liaison_type) {
                                let article_liaison = liaison_type.articles.find(function(art) {
                                    return art.ref_article == liaison.ref_article;
                                });
                                if(article_liaison) {
                                    article_liaison.params = data;
                                }
                            }
                        });
                    }
                }
            });

        };

		/*
		 * Permet de corriger un bug sur les formulaires de recherche ( on utilisait jQuery pour bind avec un .submit()
		 * mais ça ne marchait pas pour la recherche de déclinaisons car les forms n'étaient pas encore chargées au moment
		 * où le Js s'éxécutait donc le binding était foireux ). Cette fonction est appelée dans un ng-submit
		 * @param evt - L'évènement du submit ( $event )
		 * @param selector - Le sélecteur CSS pour récup le formulaire ( par ex. '#form_recherche_declinaisons' )
		 */
		$scope.formSubmit = function(evt, selector) {
			evt.preventDefault();
			$(selector).request();
		};


    }]);

    module.controller('GalleryCtrl', ['$rootScope', '$scope', function($rootScope, $scope) {
        var vm = this,
            imageList = $scope.$parent.article.images.list; //Bind to variable containing image list

        //Variables
        vm.currentIndex = 0; //Index of image displayed when opened
        vm.galleryCoverImage = null; //Main cover image displayed when not opened
        vm.opened = false;
        //Functions
        vm.closeGallery = closeGallery;
        vm.nextImage = nextImage;
        vm.openGallery = openGallery;
        vm.prevImage = prevImage;
        vm.setGalleryCoverImage = setGalleryCoverImage;
        vm.showImage = showImage;

        _init();

        //PUBLIC--------------------------------------------------------------------------------------------------------

        function closeGallery() {
            vm.opened = false;
        }

        function nextImage() {
            vm.currentIndex += 1;
            if (vm.currentIndex === imageList.length) {
                vm.currentIndex = 0;
            }
        }

        function openGallery() {
            if(imageList.length) {
                var index = 0;

                //We got a cover image
                if(vm.galleryCoverImage) {
                    //Try to get her index (can return nothing if image has been deleted, then first image in imageList will be shown)
                    _.find(imageList, function(image, i) {
                        if(image.id_image == vm.galleryCoverImage.id_image) {
                            index = i;
                            return true;
                        }
                    });
                }

                vm.currentIndex = index;
                vm.opened = true;
            }
        }

        function prevImage() {
            vm.currentIndex -= 1;
            if (vm.currentIndex < 0) {
                vm.currentIndex = imageList.length - 1;
            }
        }

        function setGalleryCoverImage(image) {
            vm.galleryCoverImage = image;
        }

        function showImage(i) {
            vm.currentIndex = i;
        }

        //PRIVATE-------------------------------------------------------------------------------------------------------

        function _init() {
            //Attach keydown events on body
            jQuery('body').on('keydown.galleryArticle', function (event) {
                if(!vm.opened) {
                    return;
                }

                var keys_codes = {
                    enter: 13,
                    esc: 27,
                    left: 37,
                    right: 39
                };
                var which = event.which;

                if (which === keys_codes.esc) {
                    vm.closeGallery();
                } else if (which === keys_codes.right || which === keys_codes.enter) {
                    vm.nextImage();
                } else if (which === keys_codes.left) {
                    vm.prevImage();
                }

                $rootScope.safeApply();
            });
        }

        function _updateGalleryCoverImage() {
            //If we get a cover image set
            if(vm.galleryCoverImage) {
                //Verify she is not deleted
                var image = _.findWhere(imageList, { id_image: vm.galleryCoverImage.id_image });
                if(!image) {
                    //If deleted, show the first image in list if exist
                    vm.setGalleryCoverImage(imageList.length && imageList[0] || null);
                }
            } else if(imageList.length) {
                vm.setGalleryCoverImage(imageList[0]);
            }
        }

        //Watch changes on image list
        $scope.$watch(function() {
            return imageList.length;
        }, function(newVal) {
            _updateGalleryCoverImage();
        });

        //Destroy event
        $scope.$on('$destroy', function() {
            jQuery('body').off('keydown.galleryArticle');
        });
    }]);

    module.controller('ArticleHomeCtrl', ['$scope', 'ArticleVisualisationService','LMBModalService', '$timeout', '$sce', '$sanitize', function($scope, ArticleVisualisationService, LMBModalService, $timeout, $sce, $sanitize) {

        // La page actuelle
        $scope.page = 'infos';
        // Permet d'initialiser l'accordéon pour la première fois sans passer par actualPage()
        $scope.loaded = false;

        function caseInsensitiveEquals(value1, value2) {
            if (angular.isString(value1) && angular.isString(value2)) {
                return value1.toLowerCase() === value2.toLowerCase();
            } else {
                return value1 == value2;
            }
        }

        $scope.$watch('statedata.home',function(oldval){
           if (angular.isDefined(oldval)){
                $scope.myData = $scope.statedata['home'];

                //Chargement de la section avis client en + de @home
                ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article,'avis_client').then(function(datas){
                    $scope.statedata['avis_client'] = datas;
                });

                //Chargement de la section stocks en + de @home
                ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article,'stocks').then(function(datas){
                    $scope.statedata['stocks'] = datas;
                });

               //Chargement de la section declinaisons en + de @home
               ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, 'declinaisons').then(function (datas) {
                   $scope.statedata['declinaisons'] = datas;
               });

               ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, 'declinaisons_Stocks').then(function (datas) {
                   $scope.statedata['declinaisons_Stocks'] = datas;
               });
               ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, 'declinaisons_prix_vente').then(function (datas) {
                   $scope.statedata['declinaisons_prix_vente'] = datas;
               });
               ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, 'declinaisons_references').then(function (datas) {
                   $scope.statedata['declinaisons_references'] = datas;
               });
               ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, 'vente').then(function (datas) {
                   $scope.statedata['vente'] = datas;
               });
           }
        });

        $scope.$watch('statedata.vente', function (oldval) {
            if (angular.isDefined(oldval)) {
                $scope.myData.InfosTarifsVentes = $scope.statedata['vente'].InfosTarifsVentes;
            }
        });

        $scope.removePromotion = function(id_article_tarif_promo) {
            LMBTools.confirm({
                content: __(150066,"Confirmez-vous la suppression de ce tarif ?"),
                confirm: __(150067,"Supprimer"),
                onValid: function(confirm){
                    if (confirm){
                        ArticleVisualisationService.supprimerPromotion(id_article_tarif_promo).then(function(response) {
                            if(!response.statut) {
                                LMBTools.alert({
                                    content: __(113024,"Une erreur s’est produite pendant la suppression du tarif")
                                });
                            } else {
                                LMBNavigation.refresh();
                            }
                        });
                    }
                }
            });
        };

        $scope.$watch('statedata.achats',function(oldval) {
           if (angular.isDefined(oldval)) {
               $scope.myData = $scope.statedata['achats'];
               if ($scope.myData.InfosAchats) {
                   var infosAchats = $scope.myData.InfosAchats;
                   $scope.article.tarifs_achats.id_tva_ha = infosAchats.id_tva_ha;
                   $scope.article.tarifs_achats.paa = infosAchats.paa;
                   $scope.article.tarifs_achats.vas = infosAchats.vas;
                   $scope.article.tarifs_achats.cout_moyen_pondere = infosAchats.cout_moyen_pondere;
                   $scope.article.tarifs_achats.dernier_cout_achat = infosAchats.dernier_cout_achat;
                   $scope.article.tarifs_achats.nom_fournisseur_dca = infosAchats.nom_fournisseur_dca;
                   $scope.article.tarifs_achats.caf_moyen = infosAchats.caf_moyen;
                   $scope.article.tarifs_achats.config_valeur_achat = infosAchats.config_valeur_achat;
                   $scope.article.tarifs_achats.config_valeur_stock = infosAchats.config_valeur_stock;
               }
           }
        });

        $scope.$watch('statedata.stocks', function (oldval) {
            if (angular.isDefined(oldval)) {
                $scope.myData.InfosStocks = $scope.statedata['stocks'].InfosStocks;
                $timeout($scope.stockGroupsAccordion);
                $scope.loaded = true;
            }
        });

        $scope.$watch('statedata.declinaisons', function (oldval) {
            if (angular.isDefined(oldval)) {
                $scope.myData_declinaisons = $scope.statedata['declinaisons'];
            }
        });

        $scope.$watch('statedata.declinaisons_Stocks', function (oldval) {
            if (angular.isDefined(oldval)) {
                $scope.myData_declinaisons_Stocks = $scope.statedata['declinaisons_Stocks'];
            }
        });

        $scope.$watch('statedata.declinaisons_prix_vente', function (oldval) {
            if (angular.isDefined(oldval)) {
                $scope.myData_declinaisons_prix_vente = $scope.statedata['declinaisons_prix_vente'];
            }
        });

        $scope.$watch('statedata.declinaisons_references', function (oldval) {
            if (angular.isDefined(oldval)) {
                $scope.myData_declinaisons_references = $scope.statedata['declinaisons_references'];
            }
        });

        $scope.getQteStock = function (carac1, carac2) {
            if (!angular.isDefined($scope.myData_declinaisons_Stocks))
                return "";

            var carac1_field = 'carac_' + $scope.myData_declinaisons.caracs_declinaisons[0].id_carac;
            var carac2_field = angular.isDefined($scope.myData_declinaisons.caracs_declinaisons[1]) ? 'carac_' + $scope.myData_declinaisons.caracs_declinaisons[1].id_carac : false;
            var qte_field = 'qte_' + $scope.myData_declinaisons_Stocks.etat;

            var art = {};
            angular.forEach($scope.myData_declinaisons_Stocks.resultats, function (article_stock) {
                if (angular.isDefined(article_stock[carac1_field]) && caseInsensitiveEquals(article_stock[carac1_field], carac1.lib_value)) {
                    if (angular.isDefined(carac2)) {
                        if (angular.isDefined(article_stock[carac2_field]) && caseInsensitiveEquals(article_stock[carac2_field], carac2.lib_value)) {
                            art = article_stock;
                            art['qte'] = article_stock[qte_field];
                        }
                    } else {
                        art = article_stock;
                        art['qte'] = article_stock[qte_field];
                    }
                }
            });
            return art;
        };

        $scope.changeStock = function () {
            ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, "declinaisons_Stocks", {
                id_stock: $scope.myData_declinaisons_Stocks.id_stock,
                etat: $scope.myData_declinaisons_Stocks.etat
            }).then(function (datas) {
                $scope.myData_declinaisons_Stocks.resultats = datas.resultats;
                let qte_field = "qte_" + $scope.myData_declinaisons_Stocks.etat;
                angular.forEach($scope.myData_declinaisons_Stocks.resultats, (res) => {
                    res['qte'] = res[qte_field];
                });
            });
        };

        $scope.changeTarif = function () {
            ArticleVisualisationService.getStateDatas($scope.article.info_generales.ref_article, 'declinaisons_prix_vente', {id_tarif: $scope.myData_declinaisons_prix_vente.id_tarif}).then(function (datas) {
                $scope.statedata['declinaisons.declinaisons_prix_vente'] = datas;
                $scope.myData_declinaisons_prix_vente.resultats = datas.resultats;
            });
        };

        $scope.getPrixVente = function (carac1, carac2) {
            if (!angular.isDefined($scope.myData_declinaisons))
                return "";

            var carac1_field = 'carac_' + $scope.myData_declinaisons.caracs_declinaisons[0].id_carac;
            var carac2_field = angular.isDefined($scope.myData_declinaisons.caracs_declinaisons[1]) ? 'carac_' + $scope.myData_declinaisons.caracs_declinaisons[1].id_carac : false;

            var art = {};
            angular.forEach($scope.myData_declinaisons_prix_vente.resultats, function (article) {
                if (angular.isDefined(article[carac1_field]) && article[carac1_field] == carac1.lib_value) {
                    if (angular.isDefined(carac2)) {
                        if (angular.isDefined(article[carac2_field]) && article[carac2_field] == carac2.lib_value) {
                            art = {pu: article.prix_unitaire, ref_article: article.ref_article};
                        }
                    } else {
                        art = {pu: article.prix_unitaire, ref_article: article.ref_article};
                    }
                }
            });
            return art;
        };

        $scope.getReference = function (carac1, carac2) {
            if (!angular.isDefined($scope.myData_declinaisons))
                return "";

            var carac1_field = 'carac_' + $scope.myData_declinaisons.caracs_declinaisons[0].id_carac;
            var carac2_field = angular.isDefined($scope.myData_declinaisons.caracs_declinaisons[1]) ? 'carac_' + $scope.myData_declinaisons.caracs_declinaisons[1].id_carac : false;
            var value_field = 'ref_' + $scope.myData_declinaisons_references.ref_type;

            var art = {};
            angular.forEach($scope.myData_declinaisons_references.resultats, function (article) {
                if (angular.isDefined(article[carac1_field]) && article[carac1_field] == carac1.lib_value) {
                    if (angular.isDefined(carac2)) {
                        if (angular.isDefined(article[carac2_field]) && article[carac2_field] == carac2.lib_value) {
                            art = {ref: article[value_field], ref_article: article.ref_article};
                        }
                    } else {
                        art = {ref: article[value_field], ref_article: article.ref_article};
                    }
                }
            });
            return art;
        };

        $scope.AffichageTableau = function (type) {
            if (type === 'stock') {
                $scope.myData_declinaisons_Stocks.affichage = 'tableau';
            } else if (type === 'prix_vente') {
                $scope.myData_declinaisons_prix_vente.affichage = 'tableau';
            } else {
                $scope.myData_declinaisons_references.affichage = 'tableau';
            }
        };

        $scope.AffichageListe = function (type) {
            if (type === 'stock') {
                $scope.myData_declinaisons_Stocks.affichage = 'liste';
            } else if (type === 'prix_vente') {
                $scope.myData_declinaisons_prix_vente.affichage = 'liste';
            } else {
                $scope.myData_declinaisons_references.affichage = 'liste';
            }
        };

        // Appelée lorsqu'on navigue sur l'onglet "Stocks" ou "Logistique"
        // Sert à réinitialiser le menu accordéon des groupes de stocks à chaque fois que l'on revient sur l'onglet ( sinon ça bug et il freeze quand on change d'onglet )
        $scope.actualPage = function(val) {
            $scope.page = val;
            if($scope.loaded) {
                $timeout($scope.stockGroupsAccordion);
            }
        }

        // Permet d'activer les accordéons pour les groupes de stocks
        $scope.stockGroupsAccordion = function() {

            // On vérifie sur quel onglet on est, et on ne l'active pas si on n'est pas sur 'Stocks' ou 'Logistique'
            var accordion_group;
            switch($scope.page) {
                case 'infos':
                    accordion_group = '.stocks-infos';
                    break;
                case 'logistique':
                    accordion_group = '.stocks-logistique';
                    break;
                default:
                    return;
            }

            $j = jQuery.noConflict();

            if($j('.stocks-list-group'+accordion_group+' .group:not(.ui-accordion)').length < 1) {
                return false;
            }

            // On n'active que le premier groupe
            $j(".stocks-list-group"+accordion_group+" .group:first-child").accordion({
                animate: 200,
                active: 0,
                header: "> h3",
                heightStyle: "content",
                collapsible: true,
                icons: false
            });
            $j(".stocks-list-group"+accordion_group+" .group:not(:first-child)").accordion({
                animate: 200,
                active: false,
                header: "> h3",
                heightStyle: "content",
                collapsible: true,
                icons: false
            });

            $j(document).off('click', '.stocks-list-group'+accordion_group+' .group .ui-accordion-header').on('click', '.stocks-list-group'+accordion_group+' .group .ui-accordion-header', function () {
              $j(this).parents('.group').find('.fa-rotate-180').removeClass('fa-rotate-180');
              if ($j(this).attr('aria-selected') === "true") {
                  $j(this).find('.fa-angle-down').addClass('fa-rotate-180');
              }
            });
        }

        // Permet de renvoyer la liste des groupes de stocks en tant qu'array
        // car le orderBy de ng-repeat ne marche pas sur les objets
        $scope.stocksGroupesToArray = function(obj) {
          var stocksGroupes = [];
          angular.forEach(obj, function (val, key) {
            stocksGroupes.push(val);
          });
          return stocksGroupes;
        }

        $scope.currentDate = Date.now();
        $scope.maxEvaluation = 5;
        $scope.evaluationRange = new Array($scope.maxEvaluation);

        $scope.editing_valeurs_achat = false;
        $scope.toggleEditValeursAchat = function () {
            $scope.editing_valeurs_achat = !$scope.editing_valeurs_achat;
        };

        $scope.stockReserve = function(id_stock){
            if (!$scope.myData)
                return;
            if ($scope.myData.InfosStocks && $scope.myData.InfosStocks.reserve[id_stock]) {
                return $scope.myData.InfosStocks.reserve[id_stock].qte - $scope.myData.InfosStocks.reserve[id_stock].qte_livree;
            } else {
                return 0;
            }
        };

        $scope.stockDispo = function(id_stock){
            if (!$scope.myData)
                return;
            var stock = 0;
            if ($scope.myData.InfosStocks && $scope.myData.InfosStocks.physique[id_stock]) {
                stock = $scope.myData.InfosStocks.physique[id_stock].qte;
            }
            var stock_rsv = $scope.stockReserve(id_stock);
            return stock-stock_rsv;
        };

        $scope.stockViewSn = function(id_stock){

            var params = {ref_article:$scope.article.info_generales.ref_article,
                          id_stock:id_stock};

            var pp_stock_view_sn = new LMBModal("pp_stock_view_sn", {titre: __(113028,"Numéros de série liés à l’article en stock"), drag:true, resizable:true, overlay:true, type: 'get', style: {width:"1000px", height: "650px"}});
            pp_stock_view_sn.request('page.php/Article/Templates/ArticleSimple/Partial:A-000000-00001/stock_view_sn/', params, {type: "post"});
            pp_stock_view_sn.open();

        }

        $scope.stockAdjust = function(id_stock) {

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(113030,"Ajuster le stock"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: {width: "490px", height: "230px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/templates/ArticleSimple/phtml/Visualisation/partials/stock_adjust.phtml",
                        controller: "stockAdjustCtrl",
                        inputs: {
                            ref_article: $scope.article.info_generales.ref_article,
                            id_stock : id_stock,
                            qte_nb_decimales : $scope.article.info_generales.qte_nb_decimales
                        }
                    },
                    then: function (modal) {},
                    onClose: function (result) {
                        if (result.return){
                            $scope.refresh('stocks');
                        }
                    }
                }
            });

        };

        $scope.articleSortieStock = function(id_stock) {

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(113031,"Sortie de stock"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: {width: "415px", height: "240px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/stock_out.phtml",
                        controller: "SortieStockCtrl",
                        inputs: {
                            ref_article: $scope.article.info_generales.ref_article,
                            id_stock : id_stock,
                            raisons : $scope.config.sorties_raisons
                        }
                    },
                    then: function (modal) {},
                    onClose: function (result) {
                        if (result.return) {
                            $scope.refresh('stocks');
                        }
                    }
                }
            });

        };

        $scope.articleTarifDetail = function(id_tarif,indice_qte){

            var pp_voir_calcul = new LMBModal("pp_voir_calcul", {titre: __(113032,"Voir le calcul du prix de vente"), drag:true, resizable:true, overlay:true, type: 'get', style: {width:"500px", height: "410px"}});
            pp_voir_calcul.request('page.php/Article/Standard/Visualisation/Partial:A-000000-00001/voir_calcul', {ref_article:$scope.article.info_generales.ref_article,id_tarif:id_tarif,indice_qte:indice_qte}, {type: "post"});
            pp_voir_calcul.open();

        };

        $scope.callMajPrixVente = function() {
            ArticleVisualisationService.majPrixVente($scope.article.info_generales.id_article).then(function(response) {
                $scope.refresh("vente");
            })
        };

        $scope.articleTarifFournisseurDetail = function(tarif) {

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(113033,"Eléments pris en compte dans le calcul"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: {width: "500px", height: "400px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/paf_view.phtml",
                        controller: "pafViewCtrl",
                        inputs: {
                            ref_article: $scope.article.info_generales.ref_article,
                            article: $scope.article,
                            devise_defaut: $scope.config.devise.default,
                            tarif : tarif,
                            devises : $scope.config.devises
                        }
                    },
                    then: function (modal) {},
                    onClose: function (result) {
                        if (result.return) {
                            $scope.refresh('stocks');
                        }
                    }
                }
            });

        };

        $scope.editTarifFournisseur = function(tarif) {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(120849,"Eléments pris en compte dans le calcul"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: {width: "1000px", height: "400px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/paf_edit.phtml",
                        controller: "pafEditCtrl",
                        inputs: {
                            ref_article: $scope.article.info_generales.ref_article,
                            tarif : angular.copy(tarif),
                            article : $scope.article,
                            config : $scope.config
                        }
                    },
                    then: function (modal) {},
                    onClose: function (result) {
                        if (result.return) {
                            $scope.refresh('stocks');
                        }
                    }
                }
            });

        };

        $scope.hasFournisseurFavoriActif = function () {
            var id_fournisseur_favori = $scope.article.info_generales.id_fournisseur_favori;
            var tarifs_founisseurs = $scope.article.tarifs_achats.tarifs;
            if (id_fournisseur_favori) {
                for (var i = 0; i < tarifs_founisseurs.length; i++) {
                    if (tarifs_founisseurs[i].id_fournisseur == id_fournisseur_favori && !tarifs_founisseurs[i].inactif) {
                        return true;
                    }
                }
            }
            else {
                return true;
            }
            return false;
        };

        $scope.updateFournisseurFavori = function (id_fournisseur) {
            ArticleVisualisationService.updateFournisseurFavori($scope.article.info_generales.id_article, id_fournisseur).then(function () {
                $scope.refresh("achats");
            });
        };

        $scope.saveValeursAchat = function () {
            $scope.editing_valeurs_achat = false;
            var infos_achats = $scope.article.tarifs_achats;
            ArticleVisualisationService.saveValeursAchat($scope.article.info_generales.id_article, infos_achats).then(function () {
                $scope.refresh('achats');
            });
        };

        $scope.cancelEditValeursAchat = function () {
            $scope.editing_valeurs_achat = false;
            $scope.refresh('achats');
        };

        $scope.loadOldPricesGraph = function(force){
            if(!force && angular.element('#evo-price').highcharts() !== undefined) {
                return;
            }

            ArticleVisualisationService.getOldPricesData($scope.article.info_generales.id_article).then(function(response) {
                angular.forEach(response.tarifs, function(v) {
                    v.step = true;
                });

                var chart = angular.element('#evo-price').highcharts();
                if (chart !== undefined) {
                    var l = chart.series.length;
                    while (chart.series.length > 0) {
                        chart.series[0].remove();
                    }
                    angular.forEach(response.tarifs, function(v) {
                        chart.addSeries(v);
                    });
                } else {
                    var fullFormat = window.formattedDatesConfig.getType("dateShortTimeP").format;
                    new Highcharts.Chart({
                        chart: {
                            renderTo: 'evo-price',
                            type: 'line',
                            backgroundColor: 'transparent'
                        },
                        title: false,
                        subtitle: false,
                        exporting: false,
                        credits: {
                            enabled: false
                        },
                        xAxis: {
                            type: 'datetime',
                            dateTimeLabelFormats: {
                                day: '%e %b',
                                month: '%b %Y'
                            }

                        },
                        tooltip: {
                            headerFormat: "",
                            pointFormatter: function(key) {
                                return Highcharts.dateFormat(fullFormat, this.x) + " :<br />" +
                                    this.series.name + " : <b>" + this.y + " " +
                                    $scope.config.devise.default.sigle + " HT</b>";
                                }
                        },
                        yAxis: {
                            title: {
                                text: __(113034,"Prix unitaire en {devise} HT",{devise:$scope.config.devise.default.sigle})
                            }
                        },
                        plotOptions: {
                            line: {
                                dataLabels: {
                                    enabled: true
                                },
                                enableMouseTracking: true
                            }
                        },
                        series: response.tarifs
                    });
                }
            });

        };

        $scope.graphVAA = {
            'date_debut': null,
            'date_fin': null
        };

        $scope.graphPAF = {
            'date_debut': null,
            'date_fin': null
        };

        function setHighcharts(elementId, data) {
            angular.forEach(data.tarifs_achats, function(v) {
                v.step = true;
            });

            var chart = angular.element('#'+elementId).highcharts();
            if (chart !== undefined) {
                var l = chart.series.length;
                while (chart.series.length > 0) {
                    chart.series[0].remove();
                }
                angular.forEach(data.tarifs_achats, function(v) {
                    chart.addSeries(v);
                });
            } else {
                var fullFormat = window.formattedDatesConfig.getType("dateShortTimeP").format;
                new Highcharts.Chart({
                    chart: {
                        renderTo: elementId,
                        type: 'line',
                        backgroundColor: 'transparent'
                    },
                    title: false,
                    subtitle: false,
                    exporting: false,
                    credits: {
                        enabled: false
                    },
                    xAxis: {
                        type: 'datetime',
                        dateTimeLabelFormats: {
                            day: '%e %b',
                            month: '%b %Y'
                        }

                    },
                    tooltip: {
                        headerFormat: "",
                        pointFormatter: function(key) {
                            return Highcharts.dateFormat(fullFormat, this.x) + " :<br />" +
                                this.series.name + " : <b>" + this.y + " " +
                                $scope.config.devise.default.sigle + " HT</b>";
                        }
                    },
                    yAxis: {
                        title: {
                            text: __(113035,"Prix unitaire en {devise} HT",{devise:$scope.config.devise.default.sigle})
                        }
                    },
                    plotOptions: {
                        line: {
                            dataLabels: {
                                enabled: true
                            },
                            enableMouseTracking: true
                        }
                    },
                    series: data.tarifs_achats
                });
            }
        }

        $scope.refreshEvoValeursAchatsGraph = function() {
            ArticleVisualisationService.getEvoValeursAchatsData(
                $scope.article.info_generales.id_article,
                $scope.graphVAA.date_debut,
                $scope.graphVAA.date_fin
            ).then(function (response) {
                setHighcharts('evo-valeurs-achats', response);
            });
        };

        $scope.refreshEvoPrixAchatFournisseurGraph = function() {
            ArticleVisualisationService.getOldPurchasePricesData(
                $scope.article.info_generales.id_article,
                $scope.graphPAF.date_debut,
                $scope.graphPAF.date_fin
            ).then(function(response) {
                setHighcharts('evo-price-achats', response);
            });
        };

            // charger + afficher le graph d’évolution des prix d’achats par fournisseur
        $scope.loadOldPurchasePricesGraph = function(force){
            if(!force && angular.element('#evo-price-achats').highcharts() !== undefined) {
                return;
            }

            $scope.$watch('graphPAF', function (newVal) {
                $scope.refreshEvoPrixAchatFournisseurGraph(true);
            }, true);
        };

        $scope.loadEvoValeursAchatsGraph = function(force){
            if(!force && angular.element('#evo-valeurs-achats').highcharts() !== undefined) {
                return;
            }

            $scope.$watch('graphVAA', function (newVal) {
                $scope.refreshEvoValeursAchatsGraph(true);
            }, true);
        };

        $scope.showPopupEvolution = function() {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: "Evolution de la valeur du produit en stock",
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: LMBModal.SIZE_LARGE
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/pp_evolution_valeur_en_stock.phtml",
                        controller: function (ref_article) {
                            let vm = this;
                            vm.ref_article = ref_article;
                        },
                        controllerAs: "PopupEvoCtrl",
                        inputs: {
                            ref_article: $scope.article.info_generales.ref_article
                        }
                    },
                    then: function (modal) {
                    },
                    onClose: function (result) {
                    }
                }
            });
        };

        $scope.majAbo = function() {
            ArticleVisualisationService.majAbo($scope.article.info_generales.id_article,$scope.article.infos_abos).then(function(data) {
                if(data.statut) {
                    LMBToast.success({
                        message: __(120822,"Paramètres mis à jour")
                    });
                }
            });
        };

        $scope.testMinValueReengagement = function(){
            if($scope.article.infos_abos.duree > $scope.article.infos_abos.duree_reengagement){
                $scope.article.infos_abos.duree_reengagement = $scope.article.infos_abos.duree;
            }
        };

        $scope.listeComptesCredits = function() {
            if(!($scope.credits.CA || $scope.credits.CD || $scope.credits.CU || $scope.credits.CP)){
                LMBTools.alert({
                    content: __(120956,"Aucun compte associé à ce compteur")
                });
            }
            else{
                ArticleVisualisationService.viewComptesCredits($scope.credits.CompteursActifs[Object.keys($scope.credits.CompteursActifs)[0]]).then(function(data) {
                    if(data.statut) {
                        LMBModalService.showModal({
                            LMBModalConfig: {
                                titre: "Liste des comptes à crédits",
                                drag: true,
                                resizable: true,
                                overlay: true,
                                style: {width: "800px", height: "400px"}
                            },
                            angularModal: {
                                config: {
                                    templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/synthese_credits_view.phtml",
                                    controller: "creditsViewCtrl",
                                    inputs: {
                                        data: data.comptes
                                    }
                                },
                                then: function (modal) {},
                                onClose: function (result) {}
                            }
                        });
                    }
                });
            }
        };

        $scope.listeComptesAbos = function(type) {
            $scope.type = type;
            ArticleVisualisationService.viewComptesAbos($scope.article.info_generales.id_article,type).then(function(data) {
                if(data.statut) {
                    LMBModalService.showModal({
                        LMBModalConfig: {
                            titre: __(120844,"Liste des abonnés"),
                            drag: true,
                            resizable: true,
                            overlay: true,
                            style: {width: "800px", height: "400px"}
                        },
                        angularModal: {
                            config: {
                                templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/synthese_abos_view.phtml",
                                controller: "abosViewCtrl",
                                inputs: {
                                    data: data.abos
                                }
                            },
                            then: function (modal) {},
                            onClose: function (result) {}
                        }
                    });
                }
            });
        };

        $scope.editDescLongue = function() {

            let desc_longue = "";

            if($scope.article && $scope.article.info_generales && $scope.article.info_generales.desc_longue) {
                desc_longue = $scope.article.info_generales.desc_longue;
            }

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: "Modification de la description longue",
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: LMBModal.SIZE_LARGE
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/pp_edit_desc_longue.phtml",
                        controller: "editDescLongueCtrl",
                        inputs: {
                            id_article: $scope.article.info_generales.id_article,
                            desc_longue: desc_longue,
                            format_html: false
                        }
                    },
                    then: function (modal) {},
                    onClose: function (result) {
                        // Si la modification s'est bien passée, on a return: true et on a la nouvelle desc_longue
                        if(result && result.return) {
                            $scope.article.info_generales.desc_longue = result.desc_longue;
                        }
                    }
                }
            });
        };

        $scope.editDescLongueHtml = function() {

            let desc_longue_html = "";

            if($scope.article && $scope.article.info_generales && $scope.article.info_generales.desc_longue_html) {
                desc_longue_html = $scope.article.info_generales.desc_longue_html;
                /*
                * Normalement la desc_longue est un object Agnular du type TrustedValueHolderType,
                * qui représente du contenu HTML échappé, du coup on utilise la fonction qui permet de
                * récupérer la string
                */
                if(typeof desc_longue_html == "object" && desc_longue_html.hasOwnProperty('$$unwrapTrustedValue') && typeof desc_longue_html.$$unwrapTrustedValue == "function") {
                    desc_longue_html = desc_longue_html.$$unwrapTrustedValue();
                }
            }

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: _e_html(450274,"Modification de la description longue"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: LMBModal.SIZE_LARGE
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Visualisation/partials/pp_edit_desc_longue.phtml",
                        controller: "editDescLongueCtrl",
                        inputs: {
                            id_article: $scope.article.info_generales.id_article,
                            desc_longue: desc_longue_html,
                            format_html: true
                        }
                    },
                    then: function (modal) {},
                    onClose: function (result) {
                        // Si la modification s'est bien passée, on a return: true et on a la nouvelle desc_longue
                        if(result && result.return) {
                            $scope.article.info_generales.desc_longue_html = $sce.trustAsHtml(result.desc_longue);
                            //$timeout(() => { $scope.$apply() });
                        }
                    }
                }
            });
        };

        /**
         * Parcourt la liste des zones de préparation et renvoie le lib de la zone de l'article
         *
         * @param liste_zones (array) - La liste des zones de préparation
         * @param id_zone (int/string/array) - Le ou les id_preparation_zone de la zone de l'article
         *
         * @return lib_zone (string) - Le lib de la zone ou "Aucune"
         */
        $scope.getLibZonePreparation = function(liste_zones, id_zone) {
            let lib_zone = [];
            if(!id_zone) {
                lib_zone = "Aucune";
            } else {
                angular.forEach(liste_zones, (zone) => {
                    if(Array.isArray(id_zone)) {
                        if(id_zone.indexOf(zone.id_preparation_zone) != -1) {
                            lib_zone.push(zone.lib);
                        }
                    } else {
                        if(zone.id_preparation_zone == id_zone) {
                            lib_zone.push(zone.lib);
                        }
                    }
                });
            }
            return lib_zone.join(', ');
        }

    }]);

    module.controller('ImagesCtrl', ['$rootScope', '$scope', 'FileUploader', 'LMBAjaxService', '$timeout', function ($rootScope, $scope, FileUploader, LMBAjaxService, $timeout) {
        $scope.uploading = false;
        $scope.directUpload = false;

        $scope.sortableOptions = {
            axis: 'y',
            handle: '.button-sortable',
            cursor: 'move',
            cancel: '',
            helper: function (event, ui) {
                ui.children().each(function () {
                    jQuery(this).width(jQuery(this).width());
                });
                return ui;
            },
            update: function (e, ui) {
                $timeout(function () {
                    var ordres = [];
                    angular.forEach($scope.article.images.list, function (image, key) {
                        var img = {id_image: image.id_image, ordre: $scope.article.images.list.indexOf(image)};
                        ordres.push(img);
                    });

                    LMBAjaxService.post('page.php/Article/Standard/Standard/updateOrdresImages', {
                        ref_article: $scope.article.info_generales.ref_article,
                        ordres: ordres
                    });

                }, 500);
            }
        };

        var uploader = $scope.uploader = new FileUploader({
            url: 'upload.php'
        });

        uploader.filters.push({
            name: 'imageFilter',
            fn: function (item, options) {
                var type = '|' + item.type.slice(item.type.lastIndexOf('/') + 1) + '|';
                return '|jpg|png|jpeg|bmp|gif|'.indexOf(type) !== -1;
            }
        });

        $scope.init = function (directUpload) {
            $scope.directUpload = directUpload;
        };

        $scope.uploadURL = function () {
            $scope.uploading = true;
            var url = 'page.php' + '/Components/Standard/Datas/getFileFromUrl';
            if (!$scope.urlImageUpload) {
                let content = __(521938,"Impossible d'importer l'image depuis l'URL renseignée. Vous devez renseigner le champ avec une URL d'image valide.");
                LMBTools.alert({
                    content: content
                });
                $scope.uploading = false;
                return;
            }
            LMBAjaxService.post(url, {url: $scope.urlImageUpload}, false, {
                transformRequest: angular.identity,
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                responseType: 'blob'
            }).then(function success(response) {
                var blob = response.data;
                var file = new File([blob], "uploadurl_" + uploader.queue.length + "." + blob.type.split("/")[1], {
                    lastModified: new Date(0),
                    type: blob.type
                });
                uploader.addToQueue(file, false, false);

                if ($scope.directUpload) {
                    $scope.article.images.push(uploader.queue);
                } else {
                    $scope.article.new_images = uploader.queue;
                }
            }, function failure(response) {
                $scope.uploading = false;
                LMBTools.alert({title: __(112606, "Erreur"), content: __(112607, "L’URL indiquée n’a pas pu être téléchargée :<br />")
                        + __(112608, "Erreur ") + response.status + " (" + response.statusText + ")"});
            });
        };

        uploader.onAfterAddingAll = function (addedFileItems) {
            $scope.article.images.queue = this.queue;
            $scope.uploading = false;
        };

        uploader.removeFromQueue = function (value) {
            var index = this.getIndexOfItem(value);
            var item = this.queue[index];
            if (item.isUploading)
                item.cancel();
            this.queue.splice(index, 1);
            item._destroy();
            this.progress = this._getTotalProgress();
            $scope.article.images.queue = this.queue;
        };

        $scope.rmImage = function (image) {
            LMBTools.confirm({
                content: __(112609, "Êtes-vous sûr de vouloir supprimer cette image ?"),
                confirm: __(112610, "Supprimer"),
                onValid: function (confirm) {
                    if (confirm) {

                        if ($scope.directUpload) {

                            LMBAjaxService.post('page.php/Article/Standard/Standard/delImage', {
                                ref_article: $scope.article.info_generales.ref_article,
                                id_image: image.id_image
                            });
                        }

                        var index = $scope.article.images.list.indexOf(image);
                        $scope.article.images.list.splice(index, 1);

                        $rootScope.safeApply();
                    }
                }
            });
        };

        $scope.addImageUrl = function (url) {
            var elm = $j('.jFiler-input-icon i');
            var icon = elm.attr('class');
            elm.attr('class', 'spinner');

            LMBAjaxService.post('page.php/Article/Standard/Standard/saveImageUrl', {ref_article: $scope.article.info_generales.ref_article, url: url})
                .then(function success(response) {

                    $scope.refreshImages();
                    elm.attr('class', icon);
                    $j('#jfiler-add-url').val('');

                }, function failure(response) {
                    $scope.uploading = false;
                    LMBTools.alert({title: __(112611, "Erreur"), content: __(112613, "L’URL indiquée n’a pas pu être téléchargée :<br />")
                            + __(112612, "Erreur ") + response.status + " (" + response.statusText + ")"});
                });
        };

        $scope.refreshImages = function () {
            LMBAjaxService.post('page.php/Article/Standard/Standard/refreshImages', {ref_article: $scope.article.info_generales.ref_article})
                .then(function success(response) {
                    $scope.article.images = response.data.datas;
                });
        };

        $scope.updateTitre = function (image) {
            LMBAjaxService.post('page.php/Article/Standard/Standard/updateTitreImage', {
                id_image: image.id_image,
                titre: image.titre
            }).then(function (res) {
                if (res.error) {
                    LMBToast.error({
                        title: 'Image',
                        message: 'Une erreur est survenue lors de la mise à jour du titre de l\'image.'
                    });
                }
            });
        };

        $scope.updateDescription = function (image) {
            LMBAjaxService.post('page.php/Article/Standard/Standard/updateDescriptionImage', {
                id_image: image.id_image,
                description: image.description
            }).then(function (res) {
                if (res.error) {
                    LMBToast.error({
                        title: 'Image',
                        message: 'Une erreur est survenue lors de la mise à jour de la description de l\'image.'
                    });
                }
            });
        };

    }]);


    module.controller('editDescLongueCtrl', ['$scope', 'close', 'ArticleVisualisationService', 'id_article', 'desc_longue', 'format_html', function($scope, close, ArticleVisualisationService, id_article, desc_longue, format_html) {

        $scope.id_article = id_article;
        $scope.desc_longue = desc_longue;
        $scope.format_html = format_html;

        $scope.save = function(evt) {
            evt.preventDefault();
            ArticleVisualisationService.saveDescLongue($scope.id_article, $scope.desc_longue, $scope.format_html).then(function(datas){
                let success = false;
                if(datas && typeof datas.statut != "undefined" && datas.statut == "OK") {
                    success = true;
                } else {
                    if(datas.statut == "KO" && typeof datas.errur != "undefined") {
                        console.error(datas.erreur);
                    }
                }
                close({
                  return: success,
                  desc_longue: $scope.desc_longue
                }, 100);
            });
        }
    }]);

    module.controller('SortieStockCtrl', ['$scope', 'close' ,'ref_article', 'id_stock', 'raisons', 'ArticleVisualisationService', function($scope, close ,ref_article, id_stock, raisons, ArticleVisualisationService) {
            $scope.display = true;
            $scope.ref_article = ref_article;
            $scope.id_stock = id_stock;
            $scope.qte = 1;
            $scope.raison = 1;
            $scope.raisons = raisons;
            $scope.stock_sns = [];
            $scope._stock_sns = [];
            $scope.error_message = '';

            $scope.init = function() {
                ArticleVisualisationService.loadStockData(ref_article,id_stock).then(function(datas){
                    $scope.data = datas;
                    $scope.stock_sns = datas.stock_sn.sn;
                    $scope._stock_sns = $scope.stock_sns;

                    $scope.$watch('data.stock_sn.sn', function() {
                       var cartTotal = 0;
                       $scope.data.stock_sn.sn.forEach(function(item) {
                         cartTotal += item.qte ? parseFloat(item.qte) : 0;
                       });

                       $scope.data.stock_sn.qte = cartTotal;
                     }, true);

                    $scope.$watch('qte', $scope.check);

                    if($scope.data.gestion_sn > 0 && $scope.qte > 0) {
                        angular.element('#button-form-stock-out-valid').attr('disabled', true);
                    }
                });
            };

            $scope.addSnItem = function() {
                if($scope.data.gestion_sn == 2 || $scope.data.gestion_sn == 4) {
                    $scope._stock_sns.push({date: '', sn: '', qte: 0});
                    $scope.check();
                }
            };

            $scope.getFilledSNs = function() {
                return $scope._stock_sns.filter((item, index) => {
                    return ('' + item.sn).trim().length != 0;
                });
            };

            $scope.getSNDate = function(sn) {
               var tmp = $scope.stock_sns.filter(function(item) {
                    return item.sn === sn;
               });
               $scope._stock_sns.filter((item) => {
                   return item.sn == sn;
               })[0].date = tmp.length > 0 ? tmp[0].date : '';

               return tmp.length > 0 ? tmp[0].date.split(' ')[0].replace(/(\d{4})-(\d{2})-(\d{2})/, '$3/$2/$1') : '';
            };

            $scope.check = function() {
                $scope.error_message = '';
                var tmpValues = $scope.getFilledSNs();

                do {
                    if($scope.data) {
                        if($scope.data.gestion_sn == 1 && tmpValues.length < $scope.qte) {
                            $scope.error_message = __(230008,"Tous les numéros de série ne sont pas renseignés.");
                            break;
                        }

                        // check les SN (doublons & erronés)
                        if($scope.data.gestion_sn > 0) {
                            tmpValues.forEach((item, index) => {
                                // colore erreur si le SN n'existe pas
                                if($scope.stock_sns.some((oldSN) => { return ''+oldSN.sn == ''+item.sn; })) {
                                    angular.element('#table-adjust-sn .sn-input:eq(' + (index) + ')').removeClass('text-error');
                                }
                                else {
                                    angular.element('#table-adjust-sn .sn-input:eq(' + (index) + ')').addClass('text-error');
                                    $scope.error_message = __(230009,"Des numéros de série sont invalides.");
                                }
                            });

                            var sns = tmpValues.map((item) => { return item.sn; });
                            if(sns.some((item, index) => { return sns.indexOf(item) != index})) {
                                $scope.error_message = __(230011,"Des numéros de série sont en double.");
                            }
                        }

                        // check les quantités
                        if($scope.data.gestion_sn == 2 || $scope.data.gestion_sn == 4) {
                            var qteTotale = tmpValues.reduce((acc, b) => { return acc+parseFloat(b.qte);}, 0);

                            if(qteTotale > $scope.data.stock.qte) {
                                $scope.error_message = __(230012,"Pas assez d'articles en stock.");
                            }
                        }
                    }
                } while(false);

                angular.element('#button-form-stock-out-valid').attr('disabled', $scope.error_message.length > 0);
            };

            $scope.confirme = function() {
                $scope.display = false;

                var sns = $scope.getFilledSNs();
                var dataToSend;
                
                if($scope.data.gestion_sn > 0) {
                    $scope.data.stock_sn.qte = $scope.data.gestion_sn == 1 ? sns.length : sns.reduce((acc, b) => { return acc+parseFloat(b.qte);}, 0);
                    $scope.data.stock_sn.sn = $scope.data.gestion_sn == 1 ? sns.map((item) => { item.qte = 1; return item; }) : sns;

                    $scope.data.stock_sn.sn.forEach((item) => {
                        if(item.date){
                            item.sn = item.date.split(' ')[0] + ' ' + item.sn;
                        }
                        return item;
                    });
                    dataToSend = $scope.data.stock_sn;
                }
                else {
                    dataToSend = $scope.data.stock_sn;
                    dataToSend.qte = $scope.qte;
                    dataToSend.sn = null;
                }

                 ArticleVisualisationService.saveSortieStock(ref_article, id_stock, $scope.raison, dataToSend).then((data) => {
                    close({
                        return: true
                    }, 100);
                 });
            };

            $scope.cancel = function() {
              $scope.display = false;
              close({
                  return: false
              }, 100);
            };

            $scope.init();
    }]);

    module.controller('ArticlePjCtrl', ['$scope','ArticleVisualisationService','$state', '$window', function($scope, ArticleVisualisationService, $state, $window) {

        $scope.$watch('statedata.pj',function(oldval){
           if (angular.isDefined(oldval)){
                $scope.myData = $scope.statedata['pj'];
           }
        });

        $scope.pjOrderBy = '';
        $scope.pjOrderReverse = false;

        $scope.order = function(pjOrderBy) {
          $scope.pjOrderReverse = ($scope.pjOrderBy === pjOrderBy) ? !$scope.pjOrderReverse : false;
          $scope.pjOrderBy = pjOrderBy;
        };

        $scope.getPathPJ = function (idPiece) {

            var myDataPromise = ArticleVisualisationService.getPathPJ(idPiece);
            myDataPromise.then(function(result) {
                return result.path
            });
        }

        $scope.changePJDate = function(id_piece, lib_piece, date_piece, id_type) {
            ArticleVisualisationService.changePJDate(id_piece, lib_piece, date_piece, id_type);
        };

        $scope.changePJLib = function(id_piece, lib_piece, id_type) {
            ArticleVisualisationService.changePJLib(id_piece, lib_piece, id_type);
        };

        $scope.changePJType = function(id_piece, id_type, ref_article = '') {
            ArticleVisualisationService.changePJType(id_piece, id_type, ref_article);
        };

        $scope.deletePj = function(id, ref_article){
          LMBTools.confirm({
              content: __(113035,"Êtes-vous sûr de vouloir supprimer cette pièce jointe ?"),
              confirm: __(113037,"Supprimer"),
              onValid: function(confirm){
                  if (confirm) {
                      ArticleVisualisationService.deletePieceJointe(id, ref_article).then(function(data) {
						  // On refresh la liste des PJ seulement une fois que la requête est finie
						  // (Sinon àa cuase des problèmes de requetes asynchroens on peut récup lal iste des PJ AVANT qu'elle soit
						  // supprimée du coup lal iste ne change pas visuellement)
						  // $scope.refresh('pj'); a remplacé car il y  plus le state
                          if(data.status=='OK') {
                              LMBNavigation.refresh();
                          }
					  });
                  }
              }
          });
        };

    }]);

    module.controller('ArticleConnecteursCtrl', ['$scope', 'ArticleEdiService','$state', 'LMBModalService', function($scope, ArticleEdiService, $state, LMBModalService) {

        $scope.$watch('statedata.connecteurs',function(oldval){
           if (angular.isDefined(oldval)){
                $scope.myData = $scope.statedata['connecteurs'];
           }
        });

        $scope.ediAction = function(actionName,canal){
            ArticleEdiService[actionName]($scope.myData.ref_article,canal).then(function(datas){
                if (datas['canal'])
                    $scope.refresh('connecteurs');
            });
        };

        $scope.ediRechercherLiaison = function (canal) {
            if(canal.id_edi_type == 24 || canal.id_edi_type == 25) {
                var query = $scope.article.info_generales.lib_article;
                LMBModalService.showModal({
                    LMBModalConfig: {
                        titre: __(340067, "Recherche d'une liaison Amazon"),
                        drag: true,
                        resizable: true,
                        overlay: true,
                        style: {width: "1000px", height: "550px"}
                    },
                    angularModal: {
                        config: {
                            templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/phtml/Recherche/pp_recherche_amazon.phtml",
                            controller: "RechercheAmazonController",
                            inputs: {
                                query: query
                            }
                        },
                        then: function (modal) {
                        },
                        onClose: function (result) {
                            if (result.selected) {
                                canal.id_distant = result.product.asin;
                                $scope.ediAction('update_id_distant', canal);
                            }
                        }
                    }
                });
            }
        };
    }]);

    module.controller('ArticleAvis_clientCtrl', ['$scope','ArticleVisualisationService' ,'ArticleAvisClientService', 'LMBModalService', '$timeout', function($scope, ArticleVisualisationService, ArticleAvisClientService, LMBModalService, $timeout) {

        $scope.$watch('statedata.avis_client',function(oldval){
           if (angular.isDefined(oldval)){
                $scope.myData = $scope.statedata['avis_client'];
           }
        });

        $scope.maxEvaluation = 5;
        $scope.evaluationRange = new Array($scope.maxEvaluation);

        $scope.avisFiltreValide = function(item){
            return item.statut=="valide";
        };

        $scope.avisFiltreAvalider = function(item){
            return item.statut=="avalider";
        };

        $scope.avisFiltreRejete = function(item){
            return item.statut=="rejet";
        };

        $scope.avisRejete = function (avis){
            avis.statut = "rejet";
            $scope.avisSave(avis);
        };

        $scope.avisSave = function(avis){
            ArticleAvisClientService.save(avis).then(function(datas){
                if (datas['infos'])
                    $scope.myData.infos = datas['infos'];
            });
        };

        $scope.avisAvantageSave = function(avisAvantage, e){
            ArticleAvisClientService.saveAvantage(avisAvantage).then(function(){
                if(e != undefined){
                    $timeout(function(){
                        $j(e.target).parents('tr').find('.edit').click();
                    }, 1);
                }
            });
        };

        $scope.addFocus = function(e){
            $timeout(function(){
                $j(e.target).parents('tr').find('.avantage-lib').focus();
            }, 1);
        };

        $scope.editAvis = function(id_avis_article){
            var pp_gestion_avis_client = new LMBModal("pp_gestion_avis_client", {titre: __(113048,"Saisir un avis client"), drag:true, resizable:true, overlay:true, type: 'get', style: {width:"850px", height: "700px"}});
            pp_gestion_avis_client.request(
                'page.php/Article/Templates/ArticleSimple/Partial:'+id_avis_article+'/gestion_avis_client/',
                {
                    ref_article:$scope.article.info_generales.ref_article,
                    id_avis_article:id_avis_article
                },
                {
                    type: "post",
                    'promise':
                        function(){
                            $scope.refresh('avis_client');
                        }
            });
            pp_gestion_avis_client.open();
        };

    }]);

    module.controller('gestionAvisClientCtrl', ['$scope', '$element', 'close','ArticleAvisClientService' ,'id_avis_article', function($scope, $element, close, ArticleAvisClientService, id_avis_article) {
            $scope.display = true;
            $scope.id_avis_article = id_avis_article;

            $scope.popup_title = $scope.id_avis_article ? __(113049,"Edition d'un avis client") : __(113050,"Saisie d'un avis client");

            ArticleAvisClientService.load(id_avis_article).then(function (avis){
               $scope.avis = avis;
            });

            $scope.confirme = function() {
                $scope.display = false;
                close({
                  return: true
                }, 500);
            };

            $scope.close = function() {
                $scope.display = false;
                close({
                  return: false
                }, 500);
            };

            $scope.cancel = function() {
              $scope.display = false;
              close({
                  return: false
              }, 500);
            };
        }]);

    module.controller('stockAdjustCtrl', ['$scope', '$element', 'close', 'ref_article', 'id_stock', 'qte_nb_decimales', 'ArticleVisualisationService', function($scope, $element, close, ref_article, id_stock, qte_nb_decimales, ArticleVisualisationService) {
            $scope.display = true;
            $scope.ref_article = ref_article;
            $scope.id_stock = id_stock;
            $scope.qte_nb_decimales = qte_nb_decimales;
            $scope.stock_sns = [];

            // These should be constants
            $scope.TYPE_SN = 1;
            $scope.TYPE_LOT = 2;
            $scope.TYPE_DLC = 4;

            $scope.init = function (){
                ArticleVisualisationService.loadStockData(ref_article, id_stock).then(function(datas){
                    $scope.data = datas;
                    $scope.stock_sns = datas.stock_sn.sn;

                    // Présélection du premier motif d'ajustement par défaut
                    if(!$scope.data?.id_motif_stock_ajustement && $scope.data?.stock_ajustements_motifs?.length) {
                        $scope.data.id_motif_stock_ajustement = $scope.data.stock_ajustements_motifs[0].id_motif_stock_ajustement || null;
                        $scope.selectMotif();
                    }

                    if($scope.data?.stock && !$scope.data.stock.qte_move) {
                        $scope.data.stock.qte_move = 0;
                    }

                    // $scope.$watch('data.stock_sn.sn', function() {
                    //    var cartTotal = 0;
                    //    $scope.data.stock_sn.sn.forEach(function(item) {
                    //      cartTotal += item.qte ? parseFloat(item.qte) : 0;
                    //    });
                    //
                    //    $scope.data.stock_sn.qte = cartTotal;
                    // }, true);

                    $scope._stock_sns = $scope.stock_sns;

                    $scope.$watch('_stock_sns', function (newVal) {
                        var check_duplicate = [];
                        var count_added = 0;
                        var count_dup = 0;
                        angular.forEach($scope._stock_sns, function (item) {
                            item.duplicated = false;
                            if(item.sn) {
                                count_added++;
                                if(check_duplicate[item.sn] === undefined) {
                                    check_duplicate[item.sn] = item;
                                }
                                else {
                                    if(!check_duplicate[item.sn].duplicated) {
                                        check_duplicate[item.sn].duplicated = true;
                                        count_dup++;
                                    }
                                    item.duplicated = true;
                                    count_dup++;
                                }
                            }
                        });
                        $scope.added_sns = count_added;
                        $scope.duplicated_sns = count_dup;
                    }, true);

                    setTimeout(function() {
                        angular.element("#table-adjust-sn .sn-input:first").trigger("focus");
                    });
                    // $scope.$watch("data.stock.qte", gererSn);
                });
            };
            const isStockAjQtyValid = (value) => {
                if(!$scope.data.motif?.id_motif_stock_ajustement) {
                    LMBToast.error({
                        title: __(114436, "Erreur"),
                        message: __(512071,"Motif d'ajustement requis")
                    });

                    return false;
                }

                if(isNaN(value)) {
                    return false;
                }

                value = parseFloat(value);

                switch ($scope.data.motif.sens) {
                    case 'entree':
                        if (value < 0) {
                            LMBToast.warning({
                                title: __(114436, "Erreur"),
                                message: __(512069,"Le motif d'ajustement choisi ne permet pas une valeur négative")
                            });
                            return false;
                        }
                        break;
                    case 'sortie':
                        if (value > 0) {
                            LMBToast.warning({
                                title: __(114436, "Erreur"),
                                message: __(512068,"Le motif d'ajustement choisi ne permet pas une valeur positive")
                            });
                            return false;
                        }
                        break;
                }

                return true;
            };

            $scope.confirme = function() {
                if($scope.data.gestion_sn > 0) {
                    $scope.data.stock_sn.qte_move = $scope.data.stock.qte_move;
                    $scope.data.stock_sn.sn = $scope._stock_sns;
                }

                // Pas de sauvegarde si duplicate des numéros de série
                let has_duplicate = false;
                $scope._stock_sns.forEach(item => {
                    has_duplicate = has_duplicate || item.duplicated;
                });

                if(has_duplicate) {
                    let message = __(182901, "Des numéros sont en doublon");
                    switch (parseInt($scope.data.gestion_sn)) {
                        case $scope.TYPE_LOT :
                            message = __(182902, "Des numéros de lot sont en doublon");
                            break;
                        case $scope.TYPE_DLC :
                            message = __(182903, "Des numéros de série sont en doublon");
                            break;
                    }
                    LMBToast.error({
                        title: __(182904,"Sauvegarde impossible"),
                        message
                    });
                    return false;
                }

                const { stock, stock_sn, gestion_sn, id_motif_stock_ajustement } = $scope.data;

                const data = {
                    ref_article,
                    id_stock,
                    id_motif_stock_ajustement
                };

                data.stock = gestion_sn > 0 ?  stock_sn : stock;
                if(!isStockAjQtyValid(data.stock?.qte_move)) {
                    return false;
                }

                $scope.display = false;
                $scope.isDisabled = true;

                ArticleVisualisationService.saveStockAjustement(data).then(function(data){
                    if(data.error) {
                        LMBToast.error({
                            title: __(114436, "Erreur"),
                            message: typeof data.error ==='string' ?
                                data.error :
                                __(512072,"Erreur durant l'enregistrement de l'ajustement de stock")
                        });
                    }

                    if(data.success) {
                        LMBToast.success({
                            title: __(512046, "Ajustement de stock sauvegardé"),
                        })
                    }

                    close({
                        return: true
                    }, 100);
                });
            };

            $scope.selectMotif = function() {
                $scope.data.motif = $scope.data.stock_ajustements_motifs.find(motif => motif.id_motif_stock_ajustement == $scope.data.id_motif_stock_ajustement);
                if ($scope.data.gestion_sn != 0) {
                    angular.forEach($scope._stock_sns, function(item) {
                        $scope.checkQteSn(item);
                    })
                    $scope.data.stock.qte_move = getQteTotale();
                }
            }

            $scope.addSnItem = function(qte) {
                var item = {date: '', sn: '', qte: 0};
                switch (parseInt($scope.data.gestion_sn)) {
                    case $scope.TYPE_LOT :
                    case $scope.TYPE_DLC :
                        if (qte) {
                            item.qte = qte;
                        }
                        break;
                }
                $scope._stock_sns.push(item);
                $scope.check();
            };

            $scope.focusNextSN = function(e) {
                setTimeout(function() {
                    angular.element("#table-adjust-sn .sn-input:eq("+(e.$index+1)+")").trigger("focus");
                });
            };

            $scope.check = function() {
                $scope.statutCheck = "";
                if($scope.data && ($scope.data.gestion_sn == 2 || $scope.data.gestion_sn == 4)) {
                    const qteTotale = getQteTotale();

                    if(qteTotale > $scope.data.stock.qte) {
                        $scope.statutCheck = __(113051,"Quantité erronée");
                    }
                }
            };

            $scope.cancel = function() {
              $scope.display = false;
              close({
                  return: false
              }, 100);
            };

            // Fonction appelée à chaque modif de la quantité afin de rajouter / supprimer des champs pour les SN
            $scope.gererSn = function () {
                if ($scope.data) {
                    const qteTotale = getQteTotale();
                    switch (parseInt($scope.data.gestion_sn)) {
                        
                        case $scope.TYPE_SN:
                            const newArray = Array(Math.max(0, $scope.data.stock.qte - qteTotale)).fill(0).map(function () {
                                return {date: '', sn: '', qte: 1};
                            });
                            $scope._stock_sns = $scope._stock_sns.concat(newArray);
                            break;

                        case $scope.TYPE_LOT:
                        case $scope.TYPE_DLC:
                            
                            if (qteTotale < $scope.data.stock.qte) {
                                const last_line = $scope._stock_sns.last() || {};
                                if (last_line.sn === "") {
                                    last_line.qte = Number(last_line.qte) + Number($scope.data.stock.qte) - qteTotale;
                                    $scope._stock_sns.pop();
                                    $scope._stock_sns.push(last_line);
                                } else {
                                    $scope.addSnItem($scope.data.stock.qte - qteTotale);
                                }
                            }
                            break;
                    }
                }
                $scope.check();
            };

            $scope.updateNewQte = function (item, originQte) {
                if ($scope.data.gestion_sn == $scope.TYPE_SN && item.qte > 1 && item.sn !='') {
                    item.qte = originQte;
                } else {
                    $scope.data.stock.qte = getQteTotale();
                    $scope.statutCheck = "";
                }
            };

            function getQteTotale() {
                var qteTotale = 0;

                angular.forEach($scope._stock_sns, function(el) {
                    qteTotale += Number(el.qte);
                });

                return qteTotale;
            }

            $scope.init();
        }]);

    module.controller('statsController', ['$scope', '$state', 'ArticleVisualisationService', function($scope, $state, ArticleVisualisationService) {

        $scope.ref_article = $scope.article.info_generales.ref_article;
        $scope.params = {segmentation:'month',ref_article: $scope.article.info_generales.ref_article, order_by:'-valeur_ttc',aff_valeur:'ttc', type:'ca'};

        if ($state.current.name=='vente.statistiques.top_vendeurs')
            $scope.params.type='topVendeur';
        if ($state.current.name=='vente.statistiques.top_secteurs')
            $scope.params.type='topSecteur';

        $scope.refreshData = function(){
              ArticleVisualisationService.getStatsData($scope.params).then(function(result){
                $scope.data = result.datas;
            });
        };

        $scope.statsTopVendeur = function(){
            $scope.params.type='topVendeur';
            $scope.refreshData();
        };

        $scope.statsTopSecteur = function(){
            $scope.params.type='topSecteur';
            $scope.refreshData();
        };

        $scope.statsTopFournisseur = function(){
            $scope.params.type='topFournisseur';
            $scope.refreshData();
        };

        $scope.refreshData();
    }]);
})();

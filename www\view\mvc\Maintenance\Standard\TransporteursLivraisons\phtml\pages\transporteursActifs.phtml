<div class="portlet">
    <div class="portlet-header-flex">
        <h1>Modules transporteurs</h1>
    </div>
    <div class="portlet-body">
        <table class="default_list_table">
            <!-- Pas de balise de thead ou tbody pour profiter du fonctionnement des styles de base de lmb-->
            <tr class="head">
                <th style="width: 50px;"></th>
                <th>ID</th>
                <th>Référence</th>
                <th>Libellé</th>
                <th style="width: 50px">Actif</th>
            </tr>
            <tr ng-repeat="module in transporteurs">
                <td class="text_center">
                    <label>
                        <input type="checkbox" class="list_checkbox" ng-model="module.selected"/>
                    </label>
                </td>
                <td>{{module.id_transport_module}}</td>
                <td>{{module.ref_transport_module}}</td>
                <td>{{module.lib}}</td>
                <td class="text_center">
                    <label>
                        <input type="checkbox" class="checkbox-switch toggle" ng-model="module.actif"
                               ng-click="toggleActifModule(module.id_transport_module,module.actif)"
                               ng-true-value="1" ng-false-value="0"/>
                    </label>
                </td>
            </tr>
            <tr ng-hide="transporteurs.length">
                <td colspan="5" class="text_center">Aucun module</td>
            </tr>
        </table>
    </div>
    <img src="../view/mvc/Maintenance/Standard/ScriptsRobots/phtml/pages/icons/arrow_ltr.png" style='float: left; margin-top: 5px;' alt="flèche vers le haut"/>
    <div class="for_selection_box">
        <div style='padding-left: 3px; padding-top: 0;'>
            <span ng-click="selectAll()" >Tout cocher</span> &middot;
            <span ng-click="unselectAll()" >Tout décocher</span>
            &middot; <span ng-click="invertSelect()">Inverser</span>
        </div>

        <div class="right">
            <label>
                <select style="max-width: 300px;" ng-model="action" ng-change="actionSelectedModule(action)">
                    <option value="" selected>Pour la sélection</option>
                    <option value="1">Activer</option>
                    <option value="0">Désactiver</option>
                </select>
            </label>
        </div>
    </div>
</div>
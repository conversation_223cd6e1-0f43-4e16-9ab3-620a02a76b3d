<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
$files = include($DIR . "view/mvc/BackOfficeAK/TastyCloud/javascript/_vue_loader.php");
?>

<link rel="stylesheet" href="<?php echo $DIR . 'view/mvc/BackOfficeAK/TastyCloud/css/main.css'; ?>">

<div class="lmb-theme">
    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading">
                    <?php _e_html(510495,"Paramètres"); ?>
                </div>
                <h1>
                    TastyCloud Réservation
                </h1>
            </div>
            <div class="jumbotron-actions">
                <a href="#autres_parametres.php" class="btn btn-white light">
                    <i class="fa fa-arrow-left"></i>
                    <?php _e_html(340644, "Retour au menu principal"); ?>
                </a>
            </div>
        </div>
    </header>

    <div class="container" id="vue-app">
        <form class="portlet" @submit.prevent="saveModuleConfig"
            :class="!module.params.actif ? 'no-inner-line' : ''"
            v-init:ref-solution="'reservation_actif'">
            <div class="portlet-header">
                <h1>
                    <?php _e_html(182841,"Activer le module"); ?>
                    TastyCloud Réservation
                </h1>
                <div class="portlet-actions">
                    <input type="checkbox"
                        v-model="module.params.reservation_actif"
                        @change="toggleModule(module.params.reservation_actif)"
                        true-value="1"
                        false-value="0"
                        class="checkbox-switch"
                    />
                </div>
            </div>
            <div class="portlet-body" v-if="module.params.reservation_actif == 1 && module.params.actif == 1" v-cloak>
                <h2>
                    <?php _e_html(182842,"Mon compte TastyCloud"); ?>
                </h2>
                <table class="style-1 auto-layout settings-table">
                    <tr>
                        <td>
                            <?php _e_html(182857,"Clés API TastyCloud"); ?>
                        </td>
                        <td>
                            <div v-for="magasin in liste_magasins" :key="magasin.id_magasin"
                                class="sync-magasin">
                                <span>{{ magasin.lib_magasin }} :</span>
                                <input type="text" v-model="module.params.config_restaurants[magasin.id_magasin].api_token" />
                                <div class="m-l-5">
                                    <button type="button"
                                        @click="generateToken(magasin.id_magasin)"
                                        class="btn btn-primary"
                                        v-if="!module.params.config_restaurants[magasin.id_magasin].user">
                                        <?php _e_html(182898,"Générer le token"); ?>
                                    </button>
                                    <button type="button"
                                        @click="generateToken(magasin.id_magasin)"
                                        class="btn btn-secondary"
                                        v-if="module.params.config_restaurants[magasin.id_magasin].user">
                                        <?php _e_html(182899,"Regénérer le token"); ?>
                                    </button>
                                </div>
                                <a href="https://app.tastycloud.fr" target="_blank"
                                    v-if="module.params.config_restaurants[magasin.id_magasin].api_token">
                                    <?php _e_html(182862,"Accès au BO TastyCloud"); ?>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>
                            <div class="text-silver flex align-center">
                                <i class="fa fa-info valign-middle m-r-5"></i>
                                <div class="p-l-5">
                                    <?php _e_html(182900,"Un token d'authentification doit être généré et renseigné sur TastyCloud afin que la connexion entre TastyCloud et le back-office soit opérationnelle. Une fois généré, le token ne peut plus être affiché, il faut donc le renseigner directement sur TastyCloud, ou en générer un nouveau si il n'a pas été noté."); ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="portlet-footer">
                <button class="btn btn-primary" type="submit">
                    <?php _e_html(180920,"Sauvegarder"); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script type="module">
    /*
     * @TODO - Standardiser ce processus d'import
     */
    import { ConfigurationController } from '<?= $files['Configuration.js'] ?>';

    const vueApp = createApp(ConfigurationController);
    vueApp.directive('init', window.VueDirectives.init)
        .directive('lmbNumber', window.VueDirectives.lmbNumber)
        .directive('lmbDate', window.VueDirectives.lmbDate)
        .directive('date-range', window.VueDirectives.dateRange);
    vueApp.use(VueComponents.ElementPlus).mount('#vue-app');
</script>
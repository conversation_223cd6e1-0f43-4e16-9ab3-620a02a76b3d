<?php

$JS_DIR = "view/mvc/Maintenance/Standard/TransporteursLivraisons/javascript";

foreach (["module", "api", "controller", "directive","service"] as $type) {
    foreach (glob($DIR . $JS_DIR . "{,/*}", GLOB_BRACE | GLOB_ONLYDIR) as $dirjs) {
        foreach (glob($dirjs . "/*." . $type . ".js") as $filejs) {
            echo '<script type="text/javascript" src="' . langage::parse($filejs) . '?rev=' . lmbconfig::getInstance()->get("SYS_version") . '"></script>';
        }
    }
}



'use strict';

(function () {
    angular
        .module('AkEcranCuisineManager')
        .controller('popupAkEcranCuisineController', popupAkecranCuisineController);

        popupAkecranCuisineController.$inject = ['$scope', 'AkEcranCuisineApi', 'close', 'ecranCuisine'];
        function popupAkecranCuisineController($scope, AkEcranCuisineApi, close, ecranCuisine) {
            $scope.ecranCuisine = ecranCuisine || {ref_interne: '', lib:''};
            $scope.loadingPage = false;

            $scope.save = function () {
                $scope.loadingPage = true;
                if (!$scope.ecranCuisine.ref_interne || !$scope.ecranCuisine.lib) {
                    LMBTools.alert(__(520479,"Référence interne ou Libellé non défini"));
                    $scope.loadingPage = false;
                    return;
                }

                if($scope.ecranCuisine?.id_ak_cuisine) {
                    AkEcranCuisineApi.update($scope.ecranCuisine).then(function (result) {
                        handleResult(result);
                    });
                } else {
                    AkEcranCuisineApi.create($scope.ecranCuisine).then(function (result) {
                        handleResult(result);
                    });
                }
            };
    
            $scope.close = function () {
                close();
            };

            function handleResult(result) {
                $scope.loadingPage = false;
                if (result.data) {
                    close(result.data);
                } else {
                    if (!result.error) {
                        let action = $scope.ecranCuisine?.id_ak_ecranCuisine ? __(182513,"modification") : __(182514,"création");
                        result.error = __(520478, "Une erreur est survenue dans la") + " " + action;
                    }
                    LMBTools.alert({content:result.error});
                }
            }
        }
})();

'use strict';

(function () {
    angular
        .module('AkPadManager')
        .controller('popupAkPadController', popupAkPadController);

        popupAkPadController.$inject = ['$scope', 'AkPadApi', 'close', 'pad'];
        function popupAkPadController($scope, AkPadApi, close, pad) {
            $scope.pad = pad || {ref_interne: '', lib:''};
            $scope.loadingPage = false;
            $scope.config = null;

            $scope.save = function () {
                $scope.loadingPage = true;
                if (!$scope.pad.ref_interne || !$scope.pad.lib) {
                    LMBTools.alert(__(520479,"Référence interne ou Libellé non défini"));
                    $scope.loadingPage = false;
                    return;
                }

                if($scope.pad?.id_ak_pad) {
                    AkPadApi.update($scope.pad).then(function (result) {
                        handleResult(result);
                    });
                } else {
                    AkPadApi.create($scope.pad).then(function (result) {
                        handleResult(result);
                    });
                }
            };
            init();
            function init() {
                AkPadApi.config($scope.pad).then(function (result) {
                    $scope.pad.imprimantes = result.config.pad_imprimantes
                    $scope.config = result.config;
                    
                });
            }

            $scope.close = function () {
                close();
            };

            function handleResult(result) {
                $scope.loadingPage = false;
                if (result.data) {
                    close(result.data);
                } else {
                    if (!result.error) {
                        let action = $scope.pad?.id_ak_pad ? __(182513,"modification") : __(182514,"création");
                        result.error = __(520478, "Une erreur est survenue dans la") + " " + action;
                    }
                    LMBTools.alert({content:result.error});
                }
            }
        }
})();
<?php
foreach (glob($DIR . "view/mvc/Opportunite/Standard/javascript/Visualisation/*.js") as $filejs){
?>
<script type="text/javascript" src="<?php echo langage::parse($filejs) ?>?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<?php
}

foreach (glob($DIR . "view/mvc/Opportunite/Standard/javascript/Visualisation/angular/*.js") as $filejs){
?>
<script type="text/javascript" src="<?php echo langage::parse($filejs) ?>?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<?php
}

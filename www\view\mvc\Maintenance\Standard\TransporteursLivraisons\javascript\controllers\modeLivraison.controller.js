'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .controller('ModeLivraisonController', ModeLivraisonController);

    ModeLivraisonController.$inject = ['$scope', 'TransporteursLivraisonsApi','infosModeDeLivraison','$stateParams','commonMethodsService'];

    function ModeLivraisonController($scope, TransporteursLivraisonsApi,infosModeDeLivraison , $stateParams, commonMethodsService) {
        //Attribution des valeurs de base
        $scope.infoModeDeLivraison = infosModeDeLivraison.mode;
        $scope.infoModeDeLivraison.codec_restrictions = ($scope.infoModeDeLivraison.codec_restrictions) ? JSON.parse($scope.infoModeDeLivraison.codec_restrictions) : {poids_min: "" , poids_max: ""};
        $scope.infoModeDeLivraison.parametres = ($scope.infoModeDeLivraison.parametres) ? JSON.parse($scope.infoModeDeLivraison.parametres) : {"magasins" : []};

        $scope.containerVente = {};
        $scope.containerVenteTempo = {};

        let originalPrixVente = JSON.parse($scope.infoModeDeLivraison.codec_calcul_pv) ;
        $scope.containerVente.base = originalPrixVente.base;

        $scope.containerVenteTempo.config_spe = JSON.parse($scope.infoModeDeLivraison.config_spe);

        $scope.transporteur = infosModeDeLivraison;

        /**
         * Fonction privée au controlleur pour extraire les données possédant un indice dans les tableurs. Attribue aux
         * cellules vides la valeur 0.
         * @param tableur tableur de vente ou d'achat dont les données doivent correspondre à la strucure {indice , prix}
         * ou {indice , [zone.id]_prix} avec autant de colonnes [zone.id]_prix qu'il y a de zones dans le zonage sélectionné.
         * @returns {*[]} tableau de lignes avec indice.
         */
        function getDataTableur(tableur) {
            let data = [];
            tableur.getData().forEach(function (line) {
                if ((typeof line.indice) === "number") {
                    if (!line.prix) {
                        $scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone.forEach(function (zone) {
                            if ((typeof line[zone.id].prix) !== "number") line[zone.id].prix = 0;
                        });
                    } else {
                        if ((typeof line.prix) !== "number") line.prix = 0;
                    }
                    data.push(line);
                }
            });
            return data;
        }

        /**
         * Fonction du scope permettant d'enregistrer toutes les informations du mode de livraison
         */
        $scope.sauvegarder = function () {
            //Mise à la valeur 0 du poids min et poids max si vide
            if ($scope.infoModeDeLivraison.codec_restrictions.poids_max==="") $scope.infoModeDeLivraison.codec_restrictions.poids_max = 0;
            if ($scope.infoModeDeLivraison.codec_restrictions.poids_min==="") $scope.infoModeDeLivraison.codec_restrictions.poids_min = 0;

            //Création d'une copie des informations du mode de livraison pour y faire des opérations sans modifier les valeurs initiales.
            let copie = JSON.parse(JSON.stringify($scope.infoModeDeLivraison));
            copie.codec_restrictions = JSON.stringify(copie.codec_restrictions);

            //Sauvegarde des éléments stockés dans le containerVente et non stockés sur le prix de vente. En type forfait tout est dans containerVente
            let codec_calcul_pv = {}
            $j.extend(codec_calcul_pv,$scope.containerVente);
            if ($scope.containerVente.base===$scope.infos.livraisonsModes['reviens'].id) {
                let value ;
                if ($scope.containerVente.type === 'coeff') {
                    value = $scope.containerVenteTempo.coeff;
                } else if ($scope.containerVente.type === 'marge') {
                    value = $scope.containerVenteTempo.marge;
                }
                codec_calcul_pv.value = value ? value : 0;
            } else if (handsone_vente) {
                codec_calcul_pv.tarifs = getDataTableur(handsone_vente);
            }
            copie.codec_calcul_pv = JSON.stringify(codec_calcul_pv);

            //Sauvegarde des éléments du tableau de reviens
            let data = getDataTableur(handsone_achats);
            copie.codec_calcul_pa = (data.length) ? JSON.stringify({tarifs: data}) : JSON.stringify([]);

            //Sauvegarde des magasins. Récupère les magasins par le html affiché si le type de livraison est "magasin", ne peut rien récupérer sinon.
            let magasins = [];
            $j('input[name="magasins[]"]').each(function () {
                if ($j(this).is(':checked')) {
                    magasins.push($j(this).val());
                }
            });
            copie.parametres = JSON.stringify({magasins: magasins});

            //Enregistrement des paramètres spécifiques par un json avec la forme "nom":"valeur" pour chaque ligne.
            let config_spe = commonMethodsService.checkConfig($scope.transporteur.config);

            if (typeof config_spe === "boolean") return;
            copie.config_spe = JSON.stringify(config_spe);

            //Enregistrement puis réponse adaptée selon si le serveur a rencontré une erreur ou non.
            TransporteursLivraisonsApi.saveConfigModeDeLivraison(copie).then(function (response) {
                if (response.ok) LMBToast.success({
                    title: "Succès",
                    message: "Config sauvegardée avec succès"
                });
                else LMBToast.error({
                    title: "Erreur",
                    message: "Une erreur est survenue, l'enregistrement a échoué"
                });
            });
        }

        //Début des options générales pour les tableurs
        let options_menu = {
            contextMenu: {
                items: {
                    row_above: {
                        name: "Insérer une ligne au dessus"
                    },
                    row_below: {
                        name: "Insérer une ligne au dessous"
                    },
                    remove_row: {
                        name: "Supprimer la ligne"
                    },
                    hsep1: "---------",
                    undo: {
                        name: "Annuler"
                    },
                    redo: {
                        name: "Rétablir"
                    }
                }
            }
        };

        let defaultOptions = {
            manualColumnMove: true,
            manualColumnResize: true,
            manualRowResize: false,
            stretchH: 'all',
            outsideClickDeselects: true,
            rowHeaders: false,
            colHeaders: true,
            fixedColumnsLeft: 1,
            columnSorting: false,
            contextMenu: ['row_above', 'row_below', 'remove_row','-----------','undo','redo'],
            minRows: 100,
            height: 300,
            autoColumnSize: true,
        };

        defaultOptions.cells = function (row, col, prop) {
            let cellProperty = {};

            //Langue pour numeral().unformat(val)
            cellProperty.language = "en";
            return cellProperty;
        };
        //Fin des options générales pour les tableurs

        //Tableur reviens
        let handsone_achats;
        $scope.creerTableurReviens = function () {
            //Elements communs à tous les tableurs de reviens, indépendamment de la zone géographique
            let tableurReviens = {
                "columns": [
                    {
                        "data": "indice",
                        "type": "numeric",
                        "format": "0.0[0000]"
                    }
                ],
                "colHeaders": [
                    "Poids (jusqu\u2019\u00e0)"
                ]
            };

            //Si le zonage est défini, créé une colonne par zone.
            if ($scope.infoModeDeLivraison.id_zonage) {
                ($scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone).forEach(function (zone) {
                    tableurReviens["colHeaders"].push(zone.lib);
                    tableurReviens["columns"].push({
                        "data": zone.id + ".prix",
                        "type": "numeric",
                        "format": "0.0[0000]"
                    });
                });
            }//Sinon, créé la colonne standard sans zone liée.
            else {
                tableurReviens["columns"].push({
                    "data": "prix",
                    "type": "numeric",
                    "format": "0.0[0000]"
                });
                tableurReviens["colHeaders"].push("Prix");
            }

            //Création ou récupération des données selon si les données du tableau de reviens enregistré correspond au zonage actuellement défini
            let needNewData = true;
            //Si il existe une sauvegarde d'un tableau
            if ($scope.infoModeDeLivraison.codec_calcul_pa) {
                //Parse de la sauvegarde pour pouvoir la parcourir
                let data = JSON.parse($scope.infoModeDeLivraison.codec_calcul_pa)["tarifs"];
                //Si un zonage est défini
                if ($scope.infoModeDeLivraison.id_zonage) {
                    //Attribution à false pour pouvoir parcourir et à la première mauvaise valeur retourner.
                    needNewData = false;
                    //Si une zone n'est pas dans les datas, attribuer à true pour créer de nouvelles données et arrêter le parcours
                    $scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone.some(function (zone) {
                        if(!data[0][zone.id]) {
                            needNewData = true;
                            return true;
                        }
                    });
                    //Si les données sont confirmées, les attribuer
                    if (!needNewData) {
                        tableurReviens.data = data;
                    }
                }//Sinon si une colonne prix standard existe, attribuer les données enregistrées et ne pas créer de nouvelles données
                else if (data[0].prix) {
                    tableurReviens.data = data;
                    needNewData = false;
                }
            }
            //Construction du tableau de données si nécessaire
            if (needNewData) {
                //Toute ligne de données doit posséder un indice
                let data = {indice : null}
                //Si un zonage est sélectionné, nous créons une case par ligne pour chaque zone pour contenir un prix vide
                if ($scope.infoModeDeLivraison.id_zonage) {
                    $scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone.forEach(function (zone) {
                        Object.defineProperties(data,{
                            [zone.id]:{
                                value: {prix: null},
                                writable: true,
                                enumerable: true,
                            }
                        })
                    });
                }//Sinon on défini une ligne prix lambda
                else {
                    Object.defineProperties(data,{
                        prix:{
                            value: null,
                            writable: true,
                            enumerable: true,
                        }
                    })
                }
                tableurReviens.data = [data];
            }

            let options = $j.extend({},defaultOptions, tableurReviens);

            $j('.tableur_achats').handsontable(options);
            handsone_achats = $j('.tableur_achats').handsontable("getInstance");
            handsone_achats.updateSettings(options_menu);
        }

        //Initialisation de base du tableur de reviens pour le premier chargement
        $scope.creerTableurReviens();

        //Tableur du prix de vente
        let handsone_vente = null;
        let infos_tableur_vente;

        /**
         * Fonction indépendante permettant de créer le tableur pour pouvoir l'initialiser soit au chargement de son html
         * avec ngInit, soit si le html existe déjà le faire directement.
         */
        $scope.creerTableurVente = function () {
            $j('.tableur_vente').handsontable(infos_tableur_vente);
            handsone_vente = $j('.tableur_vente').handsontable("getInstance");
            handsone_vente.updateSettings(options_menu);
        }

        /**
         * Fonction indépendante permettant de créer les informations de vente selon le type de sélection et de structure
         * des données
         */
        $scope.creerInfosVente = function () {
            //Réinitialisation du conteneur des informations de vente pour ne pas enregistrer d'informations non nécessaires
            $scope.containerVente = {base: $scope.containerVente.base};
            //Si le prix est reviens, mets à null le tableur handsone_vente et attribue la valeur type dans le containerVente
            if ($scope.containerVente.base === $scope.infos.livraisonsModes['reviens'].id) {
                handsone_vente = null;
                //Si le prix est reviens dans la config enregistrée, attribue type au type de la config
                if (originalPrixVente.base === $scope.containerVente.base) {
                    $scope.containerVente.type = originalPrixVente.type;
                    //Séparation de value dans deux champs dans le containerVenteTempo, selon la sélection, pour que les deux champs affichant l'un ou l'autre ne se retrouvent pas liés
                    if (originalPrixVente.type === 'coeff') {
                        $scope.containerVenteTempo.coeff = originalPrixVente.value;
                    } else if (originalPrixVente.type === 'marge') {
                        $scope.containerVenteTempo.marge = originalPrixVente.value;
                    }
                }
                //Sinon mets une valeur par défaut
                else $scope.containerVente.type = "identique";
            }//Si le prix est forfait, mets à null le tableur handsone_vente et attribue une valeur de base à montant et app_tarifs
            else if ($scope.containerVente.base === $scope.infos.livraisonsModes['forfait'].id)  {
                handsone_vente = null;
                //Si la base de prix correspond à celle enregistrée en config
                if (originalPrixVente.base === $scope.containerVente.base) {
                    $scope.containerVente.montant = originalPrixVente.montant;
                    $scope.containerVente.app_tarifs = originalPrixVente.app_tarifs;
                } else {
                    $scope.containerVente.montant = "";
                    $scope.containerVente.app_tarifs = $scope.infos.inclusionTaxes.PU_TTC;
                }
            } //Sinon, la base de prix utilise forcément un tableur
            else {
                let tabInfo = {
                    //Elements communs à tous les tableurs de reviens, indépendamment de la zone géographique
                    "columns": [
                        {
                            "data": "indice",
                            "type": "numeric",
                            "format": "0.0[0000]"
                        }
                    ],
                    "colHeaders": (
                        ($scope.containerVente.base === $scope.infos.livraisonsModes['poids'].id || $scope.containerVente.base === $scope.infos.livraisonsModes['poids_dest'].id) ?
                            ["Poids max"]
                            :
                            ["Prix (\u00e0 partir de)"]
                    )
                };

                //Si le zonage est défini, créé une colonne par zone.
                if ($scope.infoModeDeLivraison.id_zonage && ($scope.containerVente.base === $scope.infos.livraisonsModes['poids_dest'].id || $scope.containerVente.base === $scope.infos.livraisonsModes['prix_dest'].id)) {
                    ($scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone).forEach(function (zone) {
                        tabInfo["colHeaders"].push(zone.lib);
                        tabInfo["columns"].push({
                            "data": zone.id + ".prix",
                            "type": "numeric",
                            "format": "0.0[0000]"
                        });
                    });
                }//Sinon, créé la colonne standard sans zone liée.
                else {
                    tabInfo["columns"].push({
                        "data": "prix",
                        "type": "numeric",
                        "format": "0.0[0000]"
                    });
                    tabInfo["colHeaders"].push("Prix");
                }

                //Création ou récupération des données selon si les données du tableau enregistré correspond au zonage actuellement défini et à la base
                let dataEmpty = true;
                //Si le prix de base correspond à la sélection
                if (originalPrixVente.base === $scope.containerVente.base) {

                    if (originalPrixVente.tarifs.length>0) {
                        //Si un zonage est défini
                        if ($scope.infoModeDeLivraison.id_zonage) {
                            //Attribution à false pour pouvoir parcourir et à la première mauvaise valeur retourner.
                            dataEmpty = false;
                            //Si une zone n'est pas dans les datas, attribuer à true pour créer de nouvelles données et arrêter le parcours
                            $scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone.some(function (zone) {
                                console.log(originalPrixVente.tarifs);
                                if(!originalPrixVente.tarifs[0][zone.id]) {
                                    dataEmpty = true;
                                    return true;
                                }
                            });
                            //Si les données sont confirmées, les attribuer
                            if (!dataEmpty) {
                                tabInfo.data = originalPrixVente.tarifs;
                            }
                        }//Sinon si une colonne prix standard existe, attribuer les données enregistrées et ne pas créer de nouvelles données
                        else if (originalPrixVente.tarifs[0].prix) {
                            tabInfo.data = originalPrixVente.tarifs;
                            dataEmpty = false;
                        }
                    }

                    //Si la base utilise le poids, attribuer les valeurs auDela, forfait et kilos_supp
                    if ($scope.containerVente.base === $scope.infos.livraisonsModes['poids'].id || $scope.containerVente.base === $scope.infos.livraisonsModes['poids_dest'].id) {
                        $scope.auDela = 0;
                        $scope.containerVente.forfait = originalPrixVente.forfait;
                        $scope.containerVente.kilos_supp = originalPrixVente.kilos_supp;
                    }

                    $scope.containerVente.app_tarifs = originalPrixVente.app_tarifs;

                }
                else {
                    //Si la base utilise le poids, attribuer aux valeurs forfait et kilos_supp une valeur de base vide.
                    if ($scope.containerVente.base === $scope.infos.livraisonsModes['poids'].id || $scope.containerVente.base === $scope.infos.livraisonsModes['poids_dest'].id) {
                        $scope.containerVente.forfait = "";
                        $scope.containerVente.kilos_supp = "";
                    }
                    $scope.containerVente.app_tarifs = $scope.infos.inclusionTaxes.PU_TTC;
                }

                //Construction du tableau de données si nécessaire
                if (dataEmpty) {
                    //Toute ligne de données doit posséder un indice
                    let data = {indice : null};
                    //Si un zonage est sélectionné, nous créons une case par ligne pour chaque zone pour contenir un prix vide
                    if ($scope.infoModeDeLivraison.id_zonage && ($scope.containerVente.base === $scope.infos.livraisonsModes['poids_dest'].id || $scope.containerVente.base === $scope.infos.livraisonsModes['prix_dest'].id)) {
                        $scope.infos.zonage[$scope.infoModeDeLivraison.id_zonage].zone.forEach(function (zone) {
                            Object.defineProperties(data,{
                                [zone.id]:{
                                    value: {prix: null},
                                    writable: true,
                                    enumerable: true,
                                }
                            })
                        });
                    }//Sinon on défini une ligne prix lambda
                    else {
                        Object.defineProperties(data,{
                            prix:{
                                value: null,
                                writable: true,
                                enumerable: true,
                            }
                        })
                    }
                    tabInfo.data = [data];
                }

                infos_tableur_vente = {};
                $j.extend(infos_tableur_vente,defaultOptions, tabInfo);

                /**
                 * Permets de changer l'affichage de la valeur auDela qui sert à afficher le poids au dessus duquel
                 * certaines règles s'appliquent
                 * @param changes liste des informations sur le dernier changement en date du tableau
                 */
                infos_tableur_vente.afterChange = function(changes) {
                    if (changes && changes[0][1]==="indice"){
                        $scope.auDela = changes[0][3];
                        $scope.$apply();
                    }
                };

                //Si le html tableur de vente est généré, créer le lien avec les nouvelles données. Sinon laisser faire le ngInit
                if ($j('.tableur_vente').length) $scope.creerTableurVente();
            }
        }
        //Fin tableurs
        
        $scope.ajouterValeur = function () {
            $scope.containerVenteTempo.config_spe.supplementaire.push(["",""]);
        }

        // Attribution au scope des méthodes resetConstant() et filter()
        commonMethodsService.transporteurCommun($scope);
    }

})();
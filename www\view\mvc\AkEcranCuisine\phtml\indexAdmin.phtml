<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/AkEcranCuisine/javascript/_javascript_loader.php");
?>
<div class="title-goback">
    <h1>
        <?php _e_html(710407,"Écran de cuisine"); ?>
        <a href="#terminaux_de_caisse.php" class="go_back_link" title="<?php _e(250302,"Retour au menu principal"); ?>">
            <?php _e_html(250302,"Retour au menu principal"); ?>
        </a>
    </h1>
</div>

<div id="" class="container" ng-app="AkEcranCuisineManager" ng-controller="IndexController">
    <div ng-init="init()">
        <div class="text-right">
            <a class=" blue_button btn_new_item" ng-click="addNew()">
                <?php _e_html(520188, "Ajouter"); ?>
            </a>
        </div>
   </div>
   <br/>
    <div class="lmb-theme">
        <table class="style-2 auto-layout liste-ecran_cuisines" style="max-width: 1200px">
            <thead>
                <tr>
                    <th><?php _e_html(520470, "ID"); ?></th>
                    <th><?php _e_html(520471, "Référence interne"); ?></th>
                    <th><?php _e_html(581356,"ID Terminal"); ?></th>
                    <th><?php _e_html(520472, "Libellé"); ?></th>
                    <th><?php _e_html(520473, "Statut"); ?></th>
                    <th><?php _e_html(581357,"Terminal AK associé"); ?></th>
                    <th><?php _e_html(581358,"Version"); ?></th>
                    <th class="text-center"><?php _e_html(250420,"Actions"); ?></th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="(idx, ecran_cuisine) in ak_ecran_cuisines">
                    <td ng-bind="'#' + ecran_cuisine.id_ak_cuisine"></td>
                    <td ng-bind="ecran_cuisine.ref_interne"></td>
                    <td ng-bind="ecran_cuisine.id_terminal_externe"></td>
                    <td ng-bind="ecran_cuisine.lib"></td>
                    <td  class="bold" ng-class="{'has-text-green': ecran_cuisine.statut_sync == 'Synchronisé', 'has-text-orange': ecran_cuisine.statut_sync == 'Synchronisation initiale en cours', 'has-text-red': ecran_cuisine.statut_sync != 'Synchronisé' && ecran_cuisine.statut_sync != 'Synchronisation initiale en cours' }"
                        ng-click="desync(ecran_cuisine.id_ak_ecran_cuisine, idx, ecran_cuisine.statut_sync)" ng-bind="ecran_cuisine.statut_sync_lib">
                    </td>
                    <td ng-bind="ecran_cuisine.caisse_terminal_lib"></td>
                    <td ng-bind="ecran_cuisine.version_app"></td>
                    <td class="text-center">
                        <img src="../interface_admin/images/FR/ico_settings.png"
                            ng-click="updateEcran_cuisine(ecran_cuisine)"
                            class="action_icon" title='<?php _e(182511,"Modifier"); ?>'
                            style="cursor:pointer;">
                        <img ng-if="ecran_cuisine.statut_sync == 'Non synchronisé'"
                            src="../interface_admin/images/FR/ico_delete.png"
                            ng-click="delete(ecran_cuisine.id_ak_cuisine, idx)"
                            class="action_icon" title='<?php _e(114606,"Supprimer"); ?>'
                            style="cursor:pointer;">
                    </td>
                </tr>
                <tr ng-if="!ak_ecran_cuisines.length && !loading">
                    <td colspan="100" class="text-center p-a-5">
                        <?php _e_html(181544,"Aucun terminal serveur enregistré"); ?>
                    </td>
                </tr>
                <tr ng-if="loading">
                    <td colspan="100" class="text-center p-a-5">
                        <span class="spinner big"></span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
</div>


<script>
    (function ($) {
        const app = $("*[ng-app]");
        angular.bootstrap(app[0], [app.attr('ng-app')]);
    })(jQuery);
</script>

<style type="text/css">
    .has-text-green {
        color: #90b462;
    }
    .has-text-red {
        color: #e74c3c;
    }

    table.liste-ecran_cuisines th:not(:last-child),
    table.liste-ecran_cuisines td:not(:last-child) {
        border-right: 1px solid #e1e1e1;
    }
</style>
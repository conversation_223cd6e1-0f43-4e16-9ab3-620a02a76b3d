'use strict';

(function() {

	var module = angular.module('ComptaRestaurant');

	module.controller('VentesDeliverooController', VentesDeliverooController);

	VentesDeliverooController.$inject = ['$scope', 'RestaurantService', 'LMBModalService', '$state', '$stateParams', '$timeout'];

	function VentesDeliverooController($scope, RestaurantService, LMBModalService, $state, $stateParams, $timeout) {

		let vm = this;

		LMBLangage.preloadTags([110533, 112920, 180948]);

		vm.synthese = {
			periode: {
				start: null,
				end: null
			},
			data: {
				summary: {},
				graph: {}
			}
		};

		$scope.$on('$viewContentLoaded', function() {
			if(vm.results && $state == 'caisse') {
				_loadChartCaisse();
			}
		});

		vm.initVentes = function() {
			// On init la recherche pour la dernière année
			let date_start = new Date();
			let date_end = new Date();

			date_start.setDate(1);
			date_start.setMonth(0);

			date_end.setMonth(12);
			date_end.setDate(0);

            vm.criteres = {
				date_debut: date_start.strftime('%Y-%m-%d 00:00:00'),
                date_fin: date_end.strftime('%Y-%m-%d 23:59:59'),
                id_caisse:''
            };
            vm.details = {
                FAC:false,
                CDC:false,
                AVO:false
            };
			$scope.paginationData = {};

			$timeout(() => {
				window.formattedDateRangeConfig.formattedDateRange(document.body);
				window.formattedDatesConfig.formattedDates(document.body);
				window.formattedTimesConfig.formattedTimes(document.body);
				vm.search();
			});
		}

		vm.search = function () {
			$timeout(() => {
				// On choisit la granulité selon la durée de la recherche
				// Voir https://stackoverflow.com/a/3224854/4840882 pour la méthode de calcul
				if (vm.criteres.date_debut && vm.criteres.date_fin) {
					let date_debut = new Date(vm.criteres.date_debut);
					let date_fin = new Date(vm.criteres.date_fin);
					let diffTime = Math.abs(date_fin.getTime() - date_debut.getTime());
					let diffDays = Math.ceil(diffTime / (1000 * 3600 * 24));

					if (date_fin.getHours() === 0 && date_fin.getMinutes() === 0 && date_fin.getSeconds() === 0) {
						vm.criteres.date_fin = vm.criteres.date_fin.replace('00:00:00', '23:59:59');
					}

					vm.criteres.granulite = "jour";
					if (diffDays >= 90) {
						vm.criteres.granulite = "mois";
					} else if (diffDays < 3) {
						vm.criteres.granulite = "heure";
					}
				}

				RestaurantService.searchVentesDeliveroo(vm.criteres, $scope.pagination).then(function (response) {
					if (response && typeof response.data != "undefined") {
						vm.results = response.data;

						// On formatte les chiffres de la synthèse
						vm.formatNumbers();

						// On lance le rendu du graph
						vm.renderGraph(vm.criteres.granulite);
					} else {
						LMBToast.error({
							title: __(580957, "Une erreur est survenue"),
							message: __(510602, "Les données n'ont pas pu être chargées")
						});
					}
				});
			});

		};

		/**
		 * Fonction appelée à chaque fois qu'on récupère les statistiques de vente
		 * Permet de formatter l'affichage des nombres tel qu'on le souhaite
		 */
		vm.formatNumbers = function() {
			vm.results.data_ventes_deliveroo.panier_moyen = 0;
			if (vm.results.data_ventes_deliveroo.nb_ventes != 0) {
				vm.results.data_ventes_deliveroo.panier_moyen = vm.results.data_ventes_deliveroo.montant_total / vm.results.data_ventes_deliveroo.nb_ventes; 
			}
		}

		/**
		 * Fonction appelée après la recherche afin de faire le rendu du graph
		 */
		vm.renderGraph = function(granulite = "jour") {
			let categories = [];
			let data_ventes = [];

			let date_format_ref = '%Y-%m-%d';
			let date_format_lib = '%d/%m/%Y';
			switch(granulite) {
				case "heure":
					date_format_ref = '%Y-%m-%d-%H';
					date_format_lib = '%d/%m/%Y %H:00';
					break;
				case "mois":
					date_format_ref = '%Y-%m';
					date_format_lib = '%m/%Y';
					break;
			}

			// Pour créer les tableaux de données on va loop sur les refs des dates incluses dans la durée
			// Ex: 2019-08-18, 2019-08-19, etc.
			// Si les données corespondantes existent (si il y a eu des ventes à ce moment) alors on les récupère
			let date_iterator = new Date(vm.criteres.date_debut);
			let end_date = new Date(vm.criteres.date_fin);
			while (date_iterator <= end_date) {
				let date_ref = date_iterator.strftime(date_format_ref);
				let date_lib = date_iterator.strftime(date_format_lib);
				categories.push(date_lib);

				let val_ventes = 0;

				let data = vm.results.data_ventes_deliveroo_graph[date_ref];
				if (data && typeof data.montant_total != "undefined") {
					val_ventes = parseFloat(parseFloat(data.montant_total).toFixed(2));
				}

				data_ventes.push(val_ventes);

				// On itère la date
				switch(granulite) {
					case "heure":
						date_iterator.setHours(date_iterator.getHours()+1);
						break;
					case "mois":
						date_iterator.setMonth(date_iterator.getMonth()+1);
						break;
					default:
						date_iterator.setDate(date_iterator.getDate() + 1);
						break;
				}
			}

			Highcharts.chart('graph', {
				title: '',
				colors: ['#7cb5ec', '#ff6384'],
				chart: {
					type: 'spline'
				},
				xAxis: {
					categories: categories
				},
				yAxis: {
					title: {
						text: _e_html(180948, "Montant")
					},
					labels: {
						formatter: function () {
							return this.value + '€';
						}
					}
				},
				tooltip: {
					crosshairs: true,
					shared: true,
					useHTML: true,
					headerFormat: '<small>{point.key}</small><table>',
					pointFormat: `<tr><td>{series.name}: &nbsp;&nbsp;</td>
						<td style="text-align: right"><b>{point.y} ${vm.results.devise.sigle}</b></td></tr>`,
					footerFormat: '</table>',
					valueDecimals: 2
				},
				plotOptions: {
					spline: {
						marker: {
							radius: 2
						}
					}
				},
				series: [{
					name: "Total ventes",
					marker: {
						symbol: 'circle'
					},
					data: data_ventes
				}]
			});
		}

	}

})();
<script src="<?= $DIR ?>view/mvc/Compta/Restaurant/javascript/angular/services.js?rev=<?= lmbconfig::getInstance()->get('SYS_version') ?>"></script>
<script src="<?= $DIR ?>view/mvc/Compta/Restaurant/javascript/angular/comptaRestaurant.module.js?rev=<?= lmbconfig::getInstance()->get('SYS_version') ?>"></script>
<?php
foreach (glob($DIR . "view/mvc/Compta/Restaurant/javascript/angular/controllers/*.js") as $filejs){
    ?>
    <script type="text/javascript" src="<?php echo langage::parse($filejs) ?>?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
    <?php
} ?>
<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/upload/upload.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<?php

<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/upload/upload.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/pagination/pagination.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Commandes/Standard/javascript/_javascript_loader.php");
?>

<style>
    .showDetails {
        display: block !important;
    }
</style>

<div id="page_recherche_article" class="lmb-theme">

    <div ng-app="CommandeRecherche" ng-controller="RechercheController" ng-init='init(<?= json_encode($this->view->params); ?>)'>
        
        <div class="container">
            <div class="menu_link_affichage portlet">

                <div class="portlet-header">
                    <h1><?php _e_html(113817, "Recherche"); ?></h1>
                </div>

                <form id="form_recherche_commande" ng-submit="search()" class="ng-pristine ng-valid">

                    <div class="portlet-body">

                        <div class="col-1-2">

                            <table class="style-1">
                                <tr>
                                    <td class="valign-top"><?php _e_html(113818, "Afficher"); ?></td>
                                    <td>
                                        <div class="row">
                                            <input type="radio" name="id_etat_doc" id="all_cmde" value="all_cmde" checked="checked" ng-model="filterListe" />
                                            <label for="all_cmde" id="label_all_cmde" class="labelled_text"><?php _e_html(113819, "Toutes les Commandes clients"); ?></label>
                                        </div>
                                        <div class="row">
                                            <input type="radio" name="id_etat_doc" id="cmdecours_c" value="cmdecours_c" ng-model="filterListe" />
                                            <label for="cmdecours_c" id="label_cmdecours_c"><?php _e_html(113820, "Uniquement les Commandes clients en cours"); ?></label>
                                        </div>
                                        <div class="row">
                                            <input type="radio" name="id_etat_doc" id="cmde_attente" value="cmde_attente" ng-model="filterListe" />
                                            <label for="cmde_attente" id="label_cmde_attente"><?php _e_html(113821, "Uniquement les Commandes clients en attente"); ?></label>
                                        </div>
                                        <div class="row">
                                            <input type="radio" name="id_etat_doc" id="cmde_traitement" value="cmde_traitement" ng-model="filterListe" />
                                            <label for="cmde_traitement" id="label_cmde_traitement"><?php _e_html(113822, "Uniquement les Commandes clients en traitement"); ?></label>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><?php _e_html(113823, "Afficher les détails"); ?></td>
                                    <td>
                                        <input name="details" type="checkbox" class="checkbox-switch" id="rad1" ng-model="showDetails">
                                    </td>
                                </tr>

                            </table>

                        </div>
                        <div class="col-1-2">

                            <table class="style-1">
                                <tr ng-repeat="(key,critere) in searchCriteres" ng-if="critere.type != 'switch' && critere[searchMode]">
                                    <td>
                                        <span ng-if="critere.type != 'checkbox'" ng-bind-html="critere.lib | lmbValidHtml">
                                        </span>
                                    </td>
                                    <td ng-switch="critere.name">
                                        <div ng-switch-default>
                                            <input ng-if="critere.type == 'text'" type="text" class="input-large" ng-model="critere.value" />
                                            <div class="input-group" ng-if="critere.type == 'periode'">
                                                <label for="min_1" class="highlight"><?php _e_html(113824, "Du"); ?></label>
                                                <input type="text" lmb-date class="date_debut_dispo" ng-model="date_debut_dispo">
                                                <label for="max_1" class="highlight"><?php _e_html(113825, "au"); ?></label>
                                                <input type="text" lmb-date class="date_fin_dispo" ng-model="date_fin_dispo">
                                            </div>
                                            <div ng-if="critere.type == 'select' && searchParams.listes[critere.name]" class="dropdown dropdown-checkbox input-full">
                                                <div class="dropdown-button">{{critere.placeholder ? critere.placeholder : '<?php _e(113826, "Tous"); ?>'}}</div>
                                                <ul class="dropdown-menu" ng-if="searchParams.listes[critere.name].length">
                                                    <li ng-repeat="item in searchParams.listes[critere.name]">
                                                        <input type="checkbox" ng-model="critere.value[item.value]">
                                                        <label>{{item.lib}}</label>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div ng-if="critere.type == 'checkbox'">
                                                <input type="checkbox" id="cmderetard_c" name="etat_c" /> <label id="label_cmderetard_c" for="cmderetard_c"><?php _e_html(113827, "Commandes en retard seulement"); ?></label>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </table>

                        </div>
                        <div class="clear"></div>

                    </div>

                    <div class="portlet-footer">
                        <button type="button" class="btn btn-default" ng-click="resetCriteres()"><?php _e_html(113828, "Annuler"); ?></button>
                        <button type="submit" class="btn btn-secondary" ng-click="resetPage()"><?php _e_html(113829, "Rechercher"); ?></button>
                    </div>

                </form>

            </div>

            <div id="result" ng-if="specs.length">

                <table class="style-2">
                    <thead>
                        <tr style="height: 40px;">
                            <th style="width:50px;" class="text-left"></th>
                            <th ng-repeat="result in searchResults" ng-if="result.actif" ng-class="result.align ? 'text-'+result.align : 'text-left'">
                                <span ng-bind-html="result.lib_short | lmbValidHtml"></span>
                            </th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody ng-repeat="(key, spec) in specs">
                        <tr>
                            <td class="text-center">
                                <input type="checkbox" class="checkbox_selection" value='{"spec":"{{spec.id_carac}}"}'>
                            </td>
                            <td ng-repeat="result in searchResults" ng-if="result.actif" ng-class="result.align ? 'text-'+result.align : 'text-left'" ng-switch="result.name">
                                <span ng-switch-when="ref_doc">{{spec.ref_doc ? spec.ref_doc : spec.ref_doc}}</span>
                                <span ng-switch-when="date_creation_doc" lmb-date>{{spec.date_creation_doc ? spec.date_creation_doc : spec.date_creation_doc}}</span>
                                <span ng-switch-when="nom_contact">{{spec.nom_contact ? spec.nom_contact : spec.nom_contact}}</span>
                                <span ng-switch-when="lib_etat_doc">{{spec.lib_etat_doc ? spec.lib_etat_doc : spec.lib_etat_doc}}</span>
                                <span ng-switch-when="event">{{spec.event ? spec.event : spec.event}}</span>
                                <span ng-switch-when="tarif"><span lmb-currency="suffixe:sigle">{{spec.tarif?spec.tarif:spec.tarif}}</span></span>
                            </td>
                            <td class="text-right no-wrap">
                                <button data-lmb-infobulle="<?php _e(113427, "Voir le détail"); ?>" class="btn btn-default rounded icon" ng-click="openDetails(key)">
                                    <i class="fa fa-search-plus"></i>
                                </button>
                                <a ng-href="#/page.php/Document/Standard/Editing/ShowDocument:{{spec.ref_doc}}" target="_blank" class="btn btn-primary rounded">
                                    <i class="fa fa-search"></i> <?= langage::write("voir") ?>
                                </a>
                            </td>
                        </tr>
                        <tr ng-if="spec.articles" ng-hide="!showDetails || details{{key}}" ng-repeat="article in spec.articles" class="details_commande details_{{key}}">
                            <td style="text-align: right;">{{article.qte ? article.qte : article.qte}}</td>
                            <td>{{article.ref_article ? article.ref_article : article.ref_article}}</td>
                            <td style="text-decoration: underline;">{{article.lib_article ? article.lib_article : article.lib_article}}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td style="text-align: right;">{{article.pu_ht ? article.pu_ht + ' €' : article.pu_ht + ' €'}}</td>
                            <td></td>
                        </tr>
                    </tbody>

                    <tfooter>
                        <tr>
                            <td colspan="100%">
                                <div id="search-contacts-actions"></div>
                            </td>
                        </tr>
                    </tfooter>

                </table>

                <pagination-form form="form_recherche_commande"></pagination-form>

            </div>

            <div ng-if="specs && !specs.length" class="portlet text-center" style="border: 1px solid lightgrey; padding: 20px;">
                <?php _e_html(113830, "Aucun champ spécifique ne correspond à vos critères."); ?>
            </div>

        </div>

    </div>

</div>

<script>
    (function($) {
        LMBTools.require({
            traductions: [
                [113831, 113837]
            ],
            onReady: function() {
                var app = $("*[ng-app]");
                angular.bootstrap(app[0], [app.attr('ng-app')]);
            }
        });

    })(jQuery);

    breadcrumbScheme.list = [
        { label: __(710561, "Commandes") },
        { label:  __(113816, "Commandes en cours") }
    ];

</script>
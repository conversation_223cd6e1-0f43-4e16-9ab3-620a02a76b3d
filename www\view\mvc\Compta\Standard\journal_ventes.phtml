<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/upload/upload.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/pagination/pagination.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Compta/Standard/javascript/_javascript_loader.php");
?>

<style>
    .showDetails{
        display: block !important;
    }
</style>

<div id="page_recherche_compta" class="lmb-theme">

    <div ng-app="ComptaRecherche" ng-controller="RechercheController" ng-init='init(<?= json_encode($this->view->params); ?>,<?= json_encode($this->view->id_magasin); ?>)'>

        <header class="jumbotron">
            <div class="container">
                <div class="jumbotron-title">
                    <div class="jumbotron-heading"><?php _e_html(140010,"Comptabilité"); ?></div>
                    <h1><?php _e_html(140011,"Journal des ventes"); ?></h1>
                </div>
            </div>
        </header>

        <div class="container">
            <div class="menu_link_affichage portlet">

                <div class="portlet-header">
                    <h1><?php _e_html(140009,"Recherche"); ?></h1>
                </div>

                <form id="form_recherche_compta" ng-submit="search()" class="ng-pristine ng-valid">

                    <div class="portlet-body">

                        <table class="style-1 auto-layout center-block" style="max-width: 800px;">
                            <tr ng-repeat="(key,critere) in searchCriteres" ng-if="critere.type != 'switch' && critere[searchMode]">
                                <td>{{critere.lib}}</td>
                                <td ng-switch="critere.name">
                                    <div ng-switch-default>
                                        <input ng-if="critere.type == 'text'" type="text" class="input-full" ng-model="critere.value" />
                                        <div class="input-group" ng-if="critere.type == 'periode'">
                                            <label for="date_debut" class="highlight"><?php _e_html(140020,"Du"); ?></label>
                                            <input type="text" 
                                                   lmb-date 
                                                   class="date_debut" 
                                                   ng-model="date_debut" 
                                                   name="date_debut" 
                                                   id="date_debut"
                                                   value="<?php echo date('Y').'-01-01'; ?>" >
                                            <label for="date_fin" class="highlight"><?php _e_html(140021,"au"); ?></label>
                                            <input type="text" 
                                                   lmb-date 
                                                   class="date_fin" 
                                                   ng-model="date_fin" 
                                                   name="date_fin" 
                                                   id="date_fin"
                                                   value="<?php echo date('Y').'-12-31'; ?>">
                                        </div>
                                        <select ng-if="critere.type == 'select' && searchParams.listes[critere.name] && critere.name == 'id_caisse'" class="input-full {{critere.name}}" ng-model="critere.value">
                                            <option ng-if="critere.name == 'id_caisse'" value=""><?php _e_html(113873,"Tous"); ?></option>
                                            <option ng-if="critere.name == 'id_caisse'" value="NULL"><?php _e_html(113874,"Aucun"); ?></option>
                                            <option value="{{item.value}}" ng-repeat="item in searchParams.listes[critere.name]" style="{{item.style}};">{{item.lib}}</option>
                                        </select>
                                        <select ng-if="critere.type == 'select' && searchParams.listes[critere.name] && critere.name == 'date_exercice'" 
                                                class="input-full {{critere.name}}" 
                                                ng-model="critere.value" 
                                                data-lmb-period="{'noneOption': false, 'inputDate': {'start': 'date_debut', 'end': 'date_fin'}}">
                                        </select>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="portlet-footer">
                        <button type="button" class="btn btn-default" ng-click="resetCriteres()"><?php _e_html(140012,"Annuler"); ?></button>
                        <button type="submit" class="btn btn-secondary"><?php _e_html(140013,"Rechercher"); ?></button>
                    </div>

                </form>

            </div>

            <div id="result" ng-if="data !== undefined && data.TVAS.length">

                <div id="frame_CA" class="portlet">

                    <div class="portlet-header">
                        <h1><?php _e_html(140023,"CHIFFRE D'AFFAIRES"); ?></h1>
                    </div>

                    <div class="portlet-body">
                        <table class="style-2">
                            <thead>
                                <tr style="height: 40px;">
                                    <th ng-repeat="result in searchResults" ng-if="result.actif" ng-class="result.align ? 'text-'+result.align : 'text-left'">
                                        {{result.lib_short}}
                                    </th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr ng-repeat="dataParc in data.TVAS">
                                    <td ng-if="dataParc.tva_taux" style="text-align: center;"><div class="bold">{{dataParc.tva_taux?dataParc.tva_taux:''}}</div>({{dataParc.tva_type?dataParc.tva_type:'N/A'}})</td>
                                    <td ng-if="dataParc.montant_ht" style="text-align: right;"><span lmb-currency>{{dataParc.montant_ht && dataParc.montant_ht != '0'?dataParc.montant_ht:'N/A'}}</span></td>
                                    <td ng-if="dataParc.montant_tva" style="text-align: right;"><span lmb-currency>{{dataParc.montant_tva && dataParc.montant_tva != '0'?dataParc.montant_tva:'N/A'}}</span></td>
                                    <td ng-if="dataParc.montant_ttc" style="text-align: right;"><span lmb-currency>{{dataParc.montant_ttc && dataParc.montant_ttc != '0'?dataParc.montant_ttc:'N/A'}}</span></td>
                                </tr>

                                <tr>
                                    <th class="text-right"><?php _e_html(140026,"TOTAL"); ?></th>
                                    <th class="text-right"><span lmb-currency>{{ getTotalHt(); }}</span></th>
                                    <th class="text-right"><span lmb-currency>{{ getTotalTva(); }}</span></th>
                                    <th class="text-right"><span lmb-currency>{{ getTotalTtc(); }}</span></th>
                                </tr>

                            </tbody>
                        </table>
                    </div>

                </div>

                <div id="frame_FAC" class="portlet">

                    <div class="portlet-header">
                        <h1><?php _e_html(140027,"SYNTHÈSE DES ENCAISSEMENTS SUR LES FACTURES CLIENTS"); ?><span ng-if="date_debut && date_fin"> <?php _e_html(140028,"DU"); ?> <span lmb-date>{{date_debut}}</span> <?php _e_html(140029,"AU"); ?> <span lmb-date>{{date_fin}}</span></span></h1>
                        <div class="portlet-actions">
                            <button type="button" class="btn btn-primary" ng-click="openDetails('FAC')">
                                <i class="fa fa-search"></i> <?php _e_html(140030,"Afficher le détail"); ?>
                            </button>
                        </div>
                    </div>

                    <div class="portlet-body">
                        <div class="row">

                            <div class="col-1-2" ng-repeat="(key,dataParc) in data.RGT_FAC">
                                <table class="style-1">
                                    <tr>
                                        <td>{{key}}</td>
                                        <td>
                                            <span lmb-currency>{{dataParc[0]}}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                        </div>

                        <div id="details_FAC" class="ng-hide">

                            <hr>

                            <div class="row">
                                <div class="left">
                                    <h2 style="margin-top:0;"><?php _e_html(140035,"Détails"); ?></h2>
                                </div>
                                <div class="right">
                                    <button type="button" class="btn btn-default" ng-click="export_ods('FAC');">
                                        <i class="fa fa-file-excel-o"></i>
                                        <?php _e_html(140036,"Afficher le détail au format .ods"); ?>
                                    </button>
                                    <button type="button" class="btn btn-default" ng-click="compta_journal_ventes_export_spl('FAC', 'pdf');">
                                        <i class="fa fa-file-pdf-o"></i>
                                        <?php _e_html(140037,"Afficher le détail au format .pdf"); ?>
                                    </button>
                                </div>
                            </div>

                            <table class="style-2">
                                <tr class="head">
                                    <th style="width:120px;"><?php _e_html(140038,"Date"); ?></th>
                                    <th style="width:150px;"><?php _e_html(140039,"Facture"); ?></th>
                                    <th><?php _e_html(140040,"Client"); ?></th>
                                    <th style="width:140px;"><?php _e_html(140041,"Montant facturé"); ?></th>
                                    <th style="width:140px;"><?php _e_html(140042,"Règlement"); ?></th>
                                    <th style="width:70px;"><?php _e_html(140043,"Mode"); ?></th>
									<th style="width:140px;"><?php _e_html(180312,"Détail"); ?></th>
                                    <th style="width:120px;"><?php _e_html(140044,"Echéance"); ?></th>
                                </tr>

                                <tr ng-repeat="detail in details_FAC" ng-if="details_FAC.length">
                                    <td><span lmb-date>{{detail.date}}</span></td>
                                    <td><a href="<?= strpos($_SERVER['REQUEST_URI'],'/interface_rovercash/') === false ? '#documents_edition' : 'documents_editing' ?>.php?ref_doc={{detail.ref_doc}}" style="display:block; width:100%" target="_blank">{{detail.ref_doc}}</a></td>
                                    <td>{{detail.nom}}</td>
                                    <td style="text-align: right;"><span lmb-currency>{{detail.ttc}}</span></td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;"><span lmb-currency>{{detail.rgt[0].montant}}</span></td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;">{{detail.rgt[0].rgm_mode}}</td>
									<td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;">{{detail.rgt[0].detail}}</td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;"><span lmb-date>{{detail.rgt[0].date_reglement}}</span></td>
                                </tr>

                                <tr ng-if="!details_FAC.length && details_FAC">
                                    <td colspan="100%" class="text-center"><?php _e_html(113875,"Aucun résultat"); ?></td>
                                </tr>

                            </table>

                        </div>

                    </div>

                </div>

                <div id="frame_AVO" class="portlet" ng-if="data.RGT_AVO.length">

                    <div class="portlet-header">
                        <h1><?php _e_html(140031,"Synthèse des décaissements sur les Avoirs clients"); ?></h1>
                        <div class="portlet-actions">
                            <button type="button" class="btn btn-primary" ng-click="openDetails('AVO')">
                                <i class="fa fa-search"></i> <?php _e_html(140034,"Afficher le détail"); ?>
                            </button>
                        </div>
                    </div>

                    <div class="portlet-body">
                        <div class="row">

                            <div class="col-1-2" ng-repeat="(key,dataParc) in data.RGT_AVO">
                                <table class="style-1">
                                    <tr>
                                        <td>{{key}}</td>
                                        <td>
                                            <span lmb-currency>{{dataParc[0]}}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                        </div>

                        <div id="details_AVO" class="ng-hide">

                            <hr>

                            <div class="row">
                                <div class="left">
                                    <h2 style="margin-top:0;"><?php _e_html(140035,"Détails"); ?></h2>
                                </div>
                                <div class="right">
                                    <button type="button" class="btn btn-default" ng-click="export_ods('AVO');">
                                        <i class="fa fa-file-excel-o"></i>
                                        <?php _e_html(140036,"Afficher le détail au format .ods"); ?>
                                    </button>
                                    <button type="button" class="btn btn-default" ng-click="compta_journal_ventes_export_spl('AVO', 'pdf');">
                                        <i class="fa fa-file-pdf-o"></i>
                                        <?php _e_html(140037,"Afficher le détail au format .pdf"); ?>
                                    </button>
                                </div>
                            </div>

                            <table class="style-2">
                                <tr class="head">
                                    <th style="width:120px;"><?php _e_html(140038,"Date"); ?></th>
                                    <th style="width:150px;"><?php _e_html(140039,"Facture"); ?></th>
                                    <th><?php _e_html(140040,"Client"); ?></th>
                                    <th style="width:140px;"><?php _e_html(140041,"Montant facturé"); ?></th>
                                    <th style="width:140px;"><?php _e_html(140042,"Règlement"); ?></th>
                                    <th style="width:70px;"><?php _e_html(140043,"Mode"); ?></th>
									<th style="width:140px;"><?php _e_html(180312,"Détail"); ?></th>
                                    <th style="width:120px;"><?php _e_html(140044,"Echéance"); ?></th>
                                </tr>

                                <tr ng-repeat="detail in details_AVO" ng-if="details_AVO.length">
                                    <td><span lmb-date>{{detail.date}}</span></td>
                                    <td><a href="#documents_edition.php?ref_doc={{detail.ref_doc}}" style="display:block; width:100%" target="_blank">{{detail.ref_doc}}</a></td>
                                    <td>{{detail.nom}}</td>
                                    <td style="text-align: right;"><span lmb-currency>{{detail.ttc}}</span></td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;"><span lmb-currency>{{detail.rgt[0].montant}}</span></td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;">{{detail.rgt[0].rgm_mode}}</td>
									<td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;">{{detail.rgt[0].detail}}</td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;"><span lmb-date>{{detail.rgt[0].date_reglement}}</span></td>
                                </tr>

                                <tr ng-if="!details_AVO.length && details_AVO">
                                    <td colspan="100%" class="text-center"><?php _e_html(113875,"Aucun résultat"); ?></td>
                                </tr>

                            </table>

                        </div>

                    </div>

                </div>

                <div id="frame_CDC" class="portlet" ng-if="data.RGT_CDC.length">

                    <div class="portlet-header">
                        <h1><?php _e_html(140032,"Synthèse des acomptes et arrhes sur les Commandes clients"); ?></h1>
                        <div class="portlet-actions">
                            <button type="button" class="btn btn-primary" ng-click="openDetails('CDC')">
                                <i class="fa fa-search"></i> <?php _e_html(140033,"Afficher le détail"); ?>
                            </button>
                        </div>
                    </div>

                    <div class="portlet-body">
                        <div class="row">

                            <div class="col-1-2" ng-repeat="(key,dataParc) in data.RGT_CDC">
                                <table class="style-1">
                                    <tr>
                                        <td>{{key}}</td>
                                        <td>
                                            <span lmb-currency>{{dataParc[0]}}</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>

                        </div>

                        <div id="details_CDC" class="ng-hide">

                            <hr>

                            <div class="row">
                                <div class="left">
                                    <h2 style="margin-top:0;"><?php _e_html(140035,"Détails"); ?></h2>
                                </div>
                                <div class="right">
                                    <button type="button" class="btn btn-default" ng-click="export_ods('CDC');">
                                        <i class="fa fa-file-excel-o"></i>
                                        <?php _e_html(140036,"Afficher le détail au format .ods"); ?>
                                    </button>
                                    <button type="button" class="btn btn-default" ng-click="compta_journal_ventes_export_spl('CDC', 'pdf');">
                                        <i class="fa fa-file-pdf-o"></i>
                                        <?php _e_html(140037,"Afficher le détail au format .pdf"); ?>
                                    </button>
                                </div>
                            </div>

                            <table class="style-2">
                                <tr class="head">
                                    <th style="width:120px;"><?php _e_html(140038,"Date"); ?></th>
                                    <th style="width:150px;"><?php _e_html(140039,"Facture"); ?></th>
                                    <th><?php _e_html(140040,"Client"); ?></th>
                                    <th style="width:140px;"><?php _e_html(140041,"Montant facturé"); ?></th>
                                    <th style="width:140px;"><?php _e_html(140042,"Règlement"); ?></th>
                                    <th style="width:70px;"><?php _e_html(140043,"Mode"); ?></th>
									<th style="width:140px;"><?php _e_html(180312,"Détail"); ?></th>
                                    <th style="width:120px;"><?php _e_html(140044,"Echéance"); ?></th>
                                </tr>

                                <tr ng-repeat="detail in details_CDC" ng-if="details_CDC.length">
                                    <td><span lmb-date>{{detail.date}}</span></td>
                                    <td><a href="#documents_edition.php?ref_doc={{detail.ref_doc}}" style="display:block; width:100%" target="_blank">{{detail.ref_doc}}</a></td>
                                    <td>{{detail.nom}}</td>
                                    <td style="text-align: right;"><span lmb-currency>{{detail.ttc}}</span></td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;"><span lmb-currency>{{detail.rgt[0].montant}}</span></td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;">{{detail.rgt[0].rgm_mode}}</td>
									<td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;">{{detail.rgt[0].detail}}</td>
                                    <td class="cursor_pointer" ng-click="popupDetail(detail.rgt[0].ref_reglement)" style="text-align: right;"><span lmb-date>{{detail.rgt[0].date_reglement}}</span></td>
                                </tr>

                                <tr ng-if="!details_CDC.length && details_CDC">
                                    <td colspan="100%" class="text-center"><?php _e_html(113875,"Aucun résultat"); ?></td>
                                </tr>

                            </table>

                        </div>

                    </div>

                </div>

            </div>

            <div ng-if="data && !data.TVAS.length" class="portlet text-center" style="border: 1px solid lightgrey; padding: 20px;">
                <?php _e_html(140025,"Aucun résultat"); ?>
            </div>

        </div>

    </div>

</div>

<script>

    (function($) {
        LMBTools.require({
            traductions: [[113877, 113880], [140014, 140019], 140022, 140045],
            onReady: function () {
                var app = $("*[ng-app]");
                angular.bootstrap(app[0], [app.attr('ng-app')]);
            }
        });

    })(jQuery);

</script>

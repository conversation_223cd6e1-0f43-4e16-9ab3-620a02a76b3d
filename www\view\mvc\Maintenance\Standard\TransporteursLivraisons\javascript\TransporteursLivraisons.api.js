/**
 * Gère tous les appels au controlleur de la maintenance des transporteurs
 */
'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .factory('TransporteursLivraisonsApi', TransporteursLivraisonsApi);

    TransporteursLivraisonsApi.$inject = ['LMBAjaxService'];
    function TransporteursLivraisonsApi(LMBAjaxService) {
        const apiBase = "page.php/Maintenance/Standard/TransporteursLivraisons/";
        let post = function (action, data = {}, useApiBase = true) {
            return LMBAjaxService.post((useApiBase ? apiBase : '') + action, data, function (response) {
                return response.data;
            });
        };
        return {
            getModulesTransport : () => post('getModulesTransport'),
            toggleActifModule : (data) => post('toggleActifModule',data),
            toggleMultipleActifModule : (data) => post('toggleMultipleActifModule',data),
            getModulesActifs : () => post('getModulesActifs'),
            getConfigModule : (data) => post('getConfigModule',data),
            saveConfigModule : (data) => post('saveConfigModule',data),
            getListeEtats : (data) => post('getListeEtats',data),
            getListeModesDeLivraisons : (data) => post('getListeModesDeLivraisons',data),
            getInfosModeDeLivraison : (data) => post('getInfosModeDeLivraison',data),
            getInfosGeneralesModeDeLivraison : () => post('getInfosGeneralesModeDeLivraison'),
            saveConfigModeDeLivraison : (data) => post('saveConfigModeDeLivraison',data)
        }
    }
})();
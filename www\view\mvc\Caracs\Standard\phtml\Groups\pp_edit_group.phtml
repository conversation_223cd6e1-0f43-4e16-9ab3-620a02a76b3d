<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/upload/upload.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/pagination/pagination.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<link rel="stylesheet" type="text/css" href="<?= $DIR ?>view/mvc/Caracs/Standard/css/style.css">

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Caracs/Standard/javascript/_javascript_loader.php");
?>

<div class="lmb-theme">

    <div ng-app="CaracRecherche" ng-controller="GroupeController" ng-init="init(<?= $this->view->id_groupe_carac; ?>, '<?= $this->view->group_type ?>')">
        <header class="jumbotron">
            <div class="container">
                <div class="jumbotron-title">
                    <div class="jumbotron-heading">
                        <?php _e_html(410430, "Groupe"); ?>
                    </div>
                    <h1 ng-bind="group.lib_carac_groupe">
                    </h1>
                </div>
                <div class="jumbotron-actions">
                    <a qa_id="740514" href="#page.php/Caracs/Standard/Carac/recherche:<?= $this->view->group_type ?>" class="btn btn-white light">
                        <i class="fa fa-arrow-left"></i>
                        <?php _e_html(181406, "Retour à la gestion des caractéristiques"); ?>
                    </a>

                    <button type="button" class="btn btn-white" ng-click="manageGroupe('article')" qa_id="540096">
                        <i class="fa fa-pencil"></i>
                        <?php

                        _e_html(710342, "Renommer");

                        ?>
                    </button>

                </div>
            </div>
        </header>

        <div class="container">


            <div class="portlet">


                <div class="portlet-body">

                    <div class="portlet-actions m-b-5">

                        <?php if (!\LMBCore\MultiInstances\Slave::isSlave()) : ?>
                            <button qa_id="740119" type="button" class="btn btn-secondary" ng-click="manageCarac()">
                                <i class="fa fa-plus"></i>
                                <?php _e_html(111895, "Nouvelle caractéristique"); ?>
                            </button>
                        <?php endif; ?>

                    </div>

                    <table class="style-2">
                        <thead>
                            <tr>
                                <th style="width: 90px"></th>
                                <th style="width: 100px"><?php _e_html(111905, "#ID"); ?></th>
                                <th><?php _e_html(111903, "Libellé"); ?></th>
                                <th ><?php _e_html(650240,"Description"); ?></th>
                                <th class="text-center"><?php _e_html(111909,"Type de champ"); ?></th>
                                <th class="text-center"><?php _e_html(180784,"Valeur par défaut"); ?></th>
                                <th class="text-center"><?php _e_html(113463,"Nbre d'articles"); ?></th>
                                <th class="text-right no-wrap"></th>
                            </tr>
                        </thead>
                        <tbody ui-sortable="sortableOptions" ng-model="caracs">
                            <tr ng-repeat="carac in caracs ">
                                <td>
                                    <input type="checkbox" />
                                </td>
                                <td>
                                    #{{ carac.id_carac }}
                                </td>
                                <td>
                                    {{ carac.lib_carac }}
                                </td>
                                <td>
                                    {{ carac.description }}
                                </td>
                                <td class="text-center">
                                    {{ carac.lib_carac_type }}
                                </td>
                                <td class="text-center">
                                    {{ carac.defaut_value }}
                                </td>
                                <td class="text-center">
                                    {{ carac.nb_objets }}
                                </td>
                                <td qa_id="480394" class="text-right no-wrap">
                                    <img src="<?= $THIS_DIR . langage::image('ico_move.png'); ?>" class="button-sortable" title="Déplacer" />
                                    <input type="checkbox" class="checkbox-switch" ng-model="carac.actif" ng-change="toggleCarac(carac)" />
                                    <button type="button" class="btn btn-primary rounded icon" ng-click="manageCarac(carac)" qa_id="540103">
                                        <i class="fa fa-cog"></i>
                                    </button>
                                    <button type="button" class="btn btn-red light rounded icon" ng-click="deleteCarac(carac)" qa_id="540105">
                                        <i class="fa fa-remove"></i>
                                    </button>
                                </td>
                            </tr>
                            <!-- <tr ng-if="equals({}, liste_carac_groupes)">
                                <td colspan="6" class="text-center">
                                    <?php

                                    _e_html(180988, "Vous n'avez pas de groupes de caractéristiques");

                                    ?>
                                </td>
                            </tr> -->
                        </tbody>
                    </table>
                </div>
            </div>

        </div>

    </div>

</div>
<script>
    (function($) {

        var app = $("*[ng-app]");
        angular.bootstrap(app[0], [app.attr('ng-app')]);

    })(jQuery);
</script>
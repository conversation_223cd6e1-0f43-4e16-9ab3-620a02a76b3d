<div id="page-opportunite-result" class="lmb-theme">

    <?php echo \Helper::paginationHTML($this->view->paginate, "#".$this->view->id_form); ?>

    <table class="style-2">
        <thead>
            <tr>
                <th class="text-center">Date</th>
                <th>Client</th>
                <th>Libellé</th>
                <th>Type</th>
                <th>Statut</th>
                <th class="text-right">Budget</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <?php if(count($this->view->results) === 0): ?>
                <tr><td class="text-center" colspan="100%"><?php echo langage::write("aucun_resultat"); ?></td></tr>
            <?php else: ?>
                <?php foreach ($this->view->results as $opportunite): ?>
                    <tr>
                        <td class="text-center">
                            <span lmb-date><?php echo $opportunite->date_creation ?></span>
                        </td>
                        <td class="bold">
                            <a href="#page.php/Contact/Standard/Visualisation/home:<?php echo $opportunite->id_contact_client ?>" target="_blank"><?php echo $opportunite->nom_complet ?></a>
                        </td>
                        <td>
                            <?php echo $opportunite->lib ?>
                        </td>
                        <td>
                            <?php echo $opportunite->lib_type ?>
                        </td>
                        <td>
                            <?php echo $opportunite->lib_statut ?>
                        </td>
                        <td class="text-right">
                            <span lmb-currency="type:suffixeDevise"><?php echo $opportunite->budget ?></span>
                        </td>
                        <td class="text-right">
                            <a href="#page.php/Opportunite/Standard/Standard/CreationEdition:<?php echo $opportunite->id_opportunite ?>" target="_blank" class="btn btn-default rounded icon" data-lmb-infobulle="Éditer l’opportunité">
                                <i class="fa fa-cog"></i>
                            </a>
                            <a href="#page.php/Opportunite/Standard/Standard/Visualisation:<?php echo $opportunite->id_opportunite ?>" target="_blank" class="btn btn-primary rounded">
                                <i class="fa fa-search"></i> Voir
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <?php echo \Helper::paginationHTML($this->view->paginate, "#".$this->view->id_form); ?>

</div>
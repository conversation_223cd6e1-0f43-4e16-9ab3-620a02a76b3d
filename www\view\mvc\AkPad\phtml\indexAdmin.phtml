<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/AkPad/javascript/_javascript_loader.php");
?>
<div class="title-goback">
    <h1>
        <?php _e_html(520480,"Terminaux Serveurs AirKitchen"); ?>
        <a href="#terminaux_de_caisse.php" class="go_back_link" title="<?php _e(250302,"Retour au menu principal"); ?>">
            <?php _e_html(250302,"Retour au menu principal"); ?>
        </a>
    </h1>
</div>

<div id="" class="container" ng-app="AkPadManager" ng-controller="IndexController">
    <div ng-init="init()">
        <div class="text-right">
            <a class=" blue_button btn_new_item" ng-click="addNew()">
                <?php _e_html(520188, "Ajouter"); ?>
            </a>
        </div>
   </div>
   <br/>
    <div class="lmb-theme">
        <table class="style-2 auto-layout liste-pads" style="max-width: 1200px">
            <thead>
                <tr>
                    <th><?php _e_html(520470, "ID"); ?></th>
                    <th><?php _e_html(520471, "Référence interne"); ?></th>
                    <th><?php _e_html(581356,"ID Terminal"); ?></th>
                    <th><?php _e_html(520472, "Libellé"); ?></th>
                    <th><?php _e_html(520473, "Statut"); ?></th>
                    <th><?php _e_html(581357,"Terminal AK associé"); ?></th>
                    <th><?php _e_html(581358,"Version"); ?></th>
                    <th class="text-center"><?php _e_html(250420,"Actions"); ?></th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="(idx, pad) in ak_pads">
                    <td ng-bind="'#' + pad.id_ak_pad"></td>
                    <td ng-bind="pad.ref_interne"></td>
                    <td ng-bind="pad.id_terminal_externe"></td>
                    <td ng-bind="pad.lib"></td>
                    <td  class="bold" ng-class="{'has-text-green': pad.statut_sync == 'Synchronisé', 'has-text-orange': pad.statut_sync == 'Synchronisation initiale en cours', 'has-text-red': pad.statut_sync != 'Synchronisé' && pad.statut_sync != 'Synchronisation initiale en cours' }"
                        ng-click="desync(pad.id_ak_pad, idx, pad.statut_sync)" ng-bind="pad.statut_sync_lib">
                    </td>
                    <td ng-bind="pad.caisse_terminal_lib"></td>
                    <td ng-bind="pad.version_app"></td>
                    <td class="text-center">
                        <img src="../interface_admin/images/FR/ico_settings.png"
                            ng-click="updatePad(pad)"
                            class="action_icon" title='<?php _e(182511,"Modifier"); ?>'
                            style="cursor:pointer;">
                        <img ng-if="pad.statut_sync == 'Non synchronisé'"
                            src="../interface_admin/images/FR/ico_delete.png"
                            ng-click="delete(pad.id_ak_pad, idx)"
                            class="action_icon" title='<?php _e(114606,"Supprimer"); ?>'
                            style="cursor:pointer;">
                    </td>
                </tr>
                <tr ng-if="!ak_pads.length && !loading">
                    <td colspan="100" class="text-center p-a-5">
                        <?php _e_html(181544,"Aucun terminal serveur enregistré"); ?>
                    </td>
                </tr>
                <tr ng-if="loading">
                    <td colspan="100" class="text-center p-a-5">
                        <span class="spinner big"></span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
</div>


<script>
    (function ($) {
        const app = $("*[ng-app]");
        angular.bootstrap(app[0], [app.attr('ng-app')]);
    })(jQuery);
</script>

<style type="text/css">
    .has-text-green {
        color: #90b462;
    }
    .has-text-red {
        color: #e74c3c;
    }

    table.liste-pads th:not(:last-child),
    table.liste-pads td:not(:last-child) {
        border-right: 1px solid #e1e1e1;
    }
</style>
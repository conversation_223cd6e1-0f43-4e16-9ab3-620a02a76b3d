<?php
use LMBMVC\Exception;
use LMBCore\Article\Tarifs\ArticleTarif;

require_once($DIR_MODULE . "spec/_lmb_emetteur.class.php");

class lmb_shopify_emetteur extends lmb_emetteur {

    protected $event_conversion = array(
        EDI_EVENT_UPDATE_ART_TARIFS => EDI_EVENT_UPDATE_ARTICLE
    );

    public function resync_canal($sync_from_rc = null) {
        $retour = parent::resync_canal($sync_from_rc);
        
        $default_catg = $this->canal->getParams("default_catg");
        if (!empty($default_catg)) {
            parent::sync_categorie($default_catg, true);
        }

        return $retour;
    }
    
    public function envoiDistant($fct, $param, $id_event = null) {
        $url_distant = $this->canal->getSITE_DISTANT() . "/modules/edi/_externals/liaison/distant.php?serial_code=" . $this->canal->getCODE_CONNECTION();
        trace('url', $url_distant);
        $mes = message_envoi::create($this->canal);
        $mes->set_destination($url_distant);
        $mes->set_evt_origine($id_event);
        $return = $mes->set_fonction($fct, $param);

        while (!new_process('liaison/_process_messages_envoi_queue.php', message_envoi::getProcess())) {
            sleep(5);
        }

        return $return;
    }

    protected function isMultiCatalogues() {
        return $this->canal->getParams('multicatalogues');
    }

    private $events_majcmd = array(
        EDI_EVENT_UPDATE_ETAT_CMD,
        EDI_EVENT_CREATE_REGLEMENT,
        EDI_EVENT_UPDATE_TRACKING
    );

    private $events_modifcmd = array(
        EDI_EVENT_CREATE_LINE_DOC,
        EDI_EVENT_CREATE_REGLEMENT
    );

    private $events_catalogue = array(
        'art' => array(
            EDI_EVENT_CREATE_CATEG,
            EDI_EVENT_UPDATE_ART_CATEG,
            EDI_EVENT_UPDATE_CATEG,
            EDI_EVENT_DELETE_CATEG,
            EDI_EVENT_CREATE_CARAC,
            EDI_EVENT_UPDATE_CARAC,
            EDI_EVENT_CREATE_VARIANTE,
            EDI_EVENT_CREATE_ARTICLE,
            EDI_EVENT_UPDATE_ARTICLE,
            EDI_EVENT_UPDATE_ART_STATUT,
            EDI_EVENT_DELETE_ARTICLE,
            EDI_EVENT_CREATE_ART_CARAC,
            EDI_EVENT_UPDATE_ART_CARAC,
            EDI_EVENT_CREATE_ART_IMAGE,
            EDI_EVENT_DELETE_ART_IMAGE,
            EDI_EVENT_RESYNC_ARTICLE,
            EDI_EVENT_RESYNC_ART_CARACS,
            EDI_EVENT_SYNCRO_CATEGS_CLIENT,
            EDI_EVENT_UPDATE_CONSTRUCTEUR,
            EDI_EVENT_RESYNC_ART_IMG,
            EDI_EVENT_CREATE_ART_PROMO,
            EDI_EVENT_DELETE_ART_PROMO,
            EDI_EVENT_SYNCRO_CATEG,
            EDI_EVENT_SYNCRO_ART_CATEG,
            EDI_EVENT_SYNCRO_ART_CATEGS,
            EDI_EVENT_UPDATE_ART_LIAISONS,
            EDI_EVENT_UPDATE_ART_POSITIONS_IMAGES
        ),
        'stock' => array(
            EDI_EVENT_UPDATE_STOCK_ART,
            EDI_EVENT_RESYNC_STOCK
        ),
    );

    protected function getCataloguesValidesPourSQL() {
        $bdd = \PDO_etendu::getInstance();

        $catalogues_valides = $this->getCataloguesValides();
        if ($this->isMultiCatalogues() && $catalogues_valides && !empty($catalogues_valides)) {
            return " IN ('" . implode("', '", $this->getCataloguesValides()) . "') ";
        } else {
            return " = " . $bdd->quote($this->canal->getID_CATALOGUE()) . " ";
        }
    }

    protected function getCataloguesValides() {
        $retour = array();
        if (!$this->isMultiCatalogues()) {
            return $retour;
        }

        $catalogues = $this->canal->getParams('catalogues');
        if (!$catalogues) {
            return $retour;
        }

        foreach ($catalogues as $catalogue) {
            array_push($retour, $catalogue["id_catalogue_lmb"]);
        }

        return $retour;
    }

    public function accepte($ref, $id_type, $param = null) {
        traceDebug("canal", "accepte() lmb_emetteur");

        if ($this->canal->getPARAMS("bloquer_envoi")) {
            $exceptions_blocage = array();
            if ($this->canal->getPARAMS("exceptions_blocage") && is_array($this->canal->getPARAMS("exceptions_blocage"))) {
                $exceptions_blocage = $this->canal->getPARAMS("exceptions_blocage");
            }
            if (!in_array($ref, $exceptions_blocage) && (empty($param) || !in_array($param, $exceptions_blocage))) {
                traceDebug("canal", "Blocage d'envoi par paramètrage");
                return "Blocage d'envoi par paramètrage";
            }
        }

        $bdd = PDO_etendu::getInstance();
        $ret = "Ce type d'évènement n'est pas traité par le type de connecteur.";

        if (in_array($id_type, array(EDI_EVENT_CREATE_ARTICLE))) {
            $ref = null;
        }

        //test type de ref
        if ($ref == 'force') {
            if (in_array($id_type, $this->events_catalogue['art'])) {
                if ($this->canal->IsNotAuthorizedFlux("catalogue")) {
                    traceDebug("canal", "Flux non autorisé (Catalogue > ART)");
                    return "Flux non autorisé (Catalogue > ART)";
                }
            }

            if (in_array($id_type, $this->events_catalogue['stock'])) {
                if ($this->canal->IsNotAuthorizedFlux("stock")) {
                    traceDebug("canal", "Flux non autorisé (Catalogue > STOCK)");
                    return "Flux non autorisé (Catalogue > STOCK)";
                }
            }

            if (in_array($id_type, $this->events_majcmd)) {
                if ($this->canal->IsNotAuthorizedFlux("majcommande")) {
                    traceDebug("canal", "Flux non autorisé (Maj Commande)");
                    return "Flux non autorisé (Maj Commande)";
                }
            }

            if (in_array($id_type, $this->events_modifcmd)) {
                if ($this->canal->IsNotAuthorizedFlux("modifcommande")) {
                    traceDebug("canal", "Flux non autorisé (Modif Commande)");
                    return "Flux non autorisé (Modif Commande)";
                }
            }

            // Bon OK, c'est pour forcer l'évent... Sauf que pour un création ARTICLE... Pour un article qui existe déjà, c'est absurde :) (A voir :D)
            if (in_array($id_type, array(EDI_EVENT_CREATE_ARTICLE))) {
                $ref_trouve = $this->canal->getRef_externe($param, array(LIAISON_TYPE_ARTICLE_VARIANTE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT, LIAISON_TYPE_ARTICLE));
                if (empty($ref_trouve)) {
                    return true;
                } else {
                    trace("force_denied", $id_type."|".$param."|".$ref_trouve);
                    return "Force denied";
                }
            } else {
                return true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_CREATE_ART_IMAGE,
            EDI_EVENT_CREATE_ART_PROMO,
            EDI_EVENT_CREATE_ART_CARAC,
            EDI_EVENT_UPDATE_ARTICLE,
            EDI_EVENT_UPDATE_ART_STATUT,
            EDI_EVENT_UPDATE_ART_CARAC,
            EDI_EVENT_UPDATE_ART_LIAISONS,
            EDI_EVENT_UPDATE_ART_POSITIONS_IMAGES,
            EDI_EVENT_UPDATE_STOCK_ART,
            EDI_EVENT_RESYNC_ARTICLE,
            EDI_EVENT_RESYNC_ART_CARACS,
            EDI_EVENT_REINIT_ART_CARACS,
            EDI_EVENT_RESYNC_ART_IMG))) {
            //article
            $query = "SELECT art.ref_article FROM articles art " .
                "JOIN catalogs_categs_content cccc ON art.id_article = cccc.id_article " .
                "JOIN catalogs_categs ccc ON cccc.id_catalogue_categ = ccc.id_catalogue_categ " .
                " AND ccc.id_catalogue " . $this->getCataloguesValidesPourSQL() . " AND active = 1 " .
                "WHERE art.ref_article = " . $bdd->quote($ref) . " LIMIT 1;";
            traceDebug("canal", $query);
            $res = $bdd->query($query);
            if (($var = $res->fetchObject()) && !empty($var->ref_article)) {
                traceDebug("canal", "contient1");
                $res->closeCursor();
                $ret = true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_UPDATE_ART_CATEG))) {
            //article
            $query = "SELECT art.ref_article FROM articles art " .
                "JOIN catalogs_categs_content cccc ON art.id_article = cccc.id_article " .
                "JOIN catalogs_categs ccc ON cccc.id_catalogue_categ = ccc.id_catalogue_categ " .
                " AND ccc.id_catalogue " . $this->getCataloguesValidesPourSQL() . " AND active = 1 " .
                "WHERE art.ref_article = " . $bdd->quote($param) . " LIMIT 1;";
            traceDebug("canal", $query);
            $res = $bdd->query($query);
            if (($var = $res->fetchObject()) && !empty($var->ref_article)) {
                traceDebug("canal", "contient1");
                $res->closeCursor();
                $ret = true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_DELETE_ARTICLE))) {
            $id_product = $this->canal->getRef_externe($ref);
            if (!empty($id_product)) {
                $ret = true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_CREATE_CATEG,
            EDI_EVENT_SYNCRO_CATEG,
            EDI_EVENT_SYNCRO_ART_CATEG,
            EDI_EVENT_DELETE_CATEG,
            EDI_EVENT_UPDATE_CATEG))) {
            if (!empty($this->canal->getParams("default_catg")) && $ref == $this->canal->getParams("default_catg")) {
                $ret = "Le catalogue n'est pas synchronisé par ce connecteur.";
            } else if (($id_type == EDI_EVENT_CREATE_CATEG) && $param != $this->canal->getID_CATALOGUE() && !$this->isMultiCatalogues() ) {
                $ret = "Le catalogue n'est pas synchronisé par ce connecteur.";
            } elseif (($id_type == EDI_EVENT_DELETE_CATEG) && $param == $this->canal->getID_CATALOGUE() || $this->isMultiCatalogues()) {
                $ret = true;
            } else {
                $query = "SELECT id_catalogue_categ, id_catalogue_categ_parent FROM catalogs_categs WHERE id_catalogue_categ = " . $bdd->quote($ref) . " AND id_catalogue " . $this->getCataloguesValidesPourSQL();
                if ($id_type != EDI_EVENT_UPDATE_CATEG && !$this->canal->getPARAMS("syncro_categs_inactives")) {
                    $query .= " AND active = 1;";
                }

                traceDebug("canal", $query);

                $res = $bdd->query($query);
                if ($var = $res->fetchObject()) {
                    if ($id_type == EDI_EVENT_CREATE_CATEG && !empty($var->id_catalogue_categ_parent)) {
                        $ret = "Le catalogue n'est pas synchronisé par ce connecteur.";
                    } else {
                        traceDebug("canal", "contient2");
                        $res->closeCursor();
                        $ret = true;
                    }
                }
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_CREATE_VARIANTE,
            EDI_EVENT_CREATE_ARTICLE))) {
            $query = "SELECT ref_article " .
                " FROM catalogs_categs ccc " .
                " JOIN catalogs_categs_content cccc ON cccc.id_catalogue_categ = ccc.id_catalogue_categ AND id_catalogue " . $this->getCataloguesValidesPourSQL() . " AND cccc.principal = 1" .
                " JOIN articles a ON cccc.id_article = a.id_article" .
                " WHERE ref_article = " . $bdd->quote($param) . " AND ccc.active=1 LIMIT 1";

            traceDebug("canal", $query);

            $res = $bdd->query($query);
            if ($var = $res->fetchObject()) {
                traceDebug("canal", "contient2");
                $res->closeCursor();
                $ret = true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_CREATE_CARAC))) {
            $id_catalogue = $this->getCataloguesValidesPourSQL();
            $catalogue = \catalogue::getInstance($id_catalogue);
            $carac_exclue = $catalogue->getCaracExclue($param);
            if (!$carac_exclue) {
                traceDebug("canal", "contient2");
                $ret = true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_CREATE_CARAC_VARIANT))) {
            $ret = true;
        } else if (in_array($id_type, array(
            EDI_EVENT_UPDATE_ETAT_CMD,
            EDI_EVENT_CREATE_REGLEMENT,
            EDI_EVENT_UPDATE_TRACKING))) {
            traceDebug("canal", "commande");
            $query = "SELECT ref_lmb " .
                "FROM edi_ref_liaisons " .
                "WHERE ref_lmb = " . $bdd->quote($ref) . " AND id_ref_type = ".$bdd->quote(LIAISON_TYPE_COMMANDE)." AND id_edi_canal = '" . $this->canal->getID_CANAL() . "' AND actif = 1;";

            traceDebug("canal", $query);

            $res = $bdd->query($query);
            if ($var = $res->fetchObject()) {
                traceDebug("canal", "contient3");
                $res->closeCursor();
                $ret = true;
            }
        } else if (in_array($id_type, array(
            EDI_EVENT_CREATE_LISTE_SOUHAITS,
            EDI_EVENT_UPDATE_LISTE_SOUHAITS,
            EDI_EVENT_CREATE_PARTICIPATION_LISTE,
            EDI_EVENT_UPDATE_PARTICIPATION_LISTE,
            EDI_EVENT_DELETE_PARTICIPATION_LISTE,
            EDI_EVENT_UPDATE_ARTICLE_LISTE,
            EDI_EVENT_RESYNC_DOC,
            EDI_EVENT_UPDATE_ETAT_DOC,
            EDI_EVENT_SYNCRO_CONSTRUCTEURS,
            EDI_EVENT_SYNCRO_CATEGS_CLIENT,
            EDI_EVENT_CREATE_CATEG_CLIENT,
            EDI_EVENT_UPDATE_CATEG_CLIENT,
            EDI_EVENT_RESYNC_CANAL,
            EDI_EVENT_SYNCRO_ART_CATEGS,
            EDI_EVENT_UPDATE_CARAC,
            EDI_EVENT_DELETE_CARAC,
            EDI_EVENT_GET_INFOS_COMPLEMENTAIRES,
            EDI_EVENT_RESYNC_STOCK))) {
            traceDebug("canal", "contient4");
            $ret = true;
        } else if (in_array($id_type, array(EDI_EVENT_DELETE_ART_IMAGE))) {
            // on accepte l'event si l'on souhaite supprimer toutes les images d'un article
            if ($param == "all") {
                $ret = true;
            } 
            
            $query = "SELECT ref_lmb " .
                "FROM edi_ref_liaisons " .
                "WHERE ref_lmb = '" . $param . "'
                AND actif = 1
                AND id_edi_canal = '" . $this->canal->getID_CANAL() . "'
                AND id_ref_type = '" . LIAISON_TYPE_IMAGES . "' AND actif = 1;";

            traceDebug("canal", $query);

            $res = $bdd->query($query);
            if (is_object($res)) {
                if ($var = $res->fetchObject()) {
                    traceDebug("canal", "contient5");
                    $res->closeCursor();
                    $ret = true;
                }
            }
        } else if (in_array($id_type, array(EDI_EVENT_DELETE_ART_PROMO))) {
            $query = "SELECT ref_lmb " .
                "FROM edi_ref_liaisons " .
                "WHERE ref_lmb = '" . $param . "'
                AND actif = 1
                AND id_edi_canal = '" . $this->canal->getID_CANAL() . "'
                AND id_ref_type = '" . LIAISON_TYPE_ARTICLE_PROMO . "' AND actif = 1;";

            traceDebug("canal", $query);

            $res = $bdd->query($query);
            if (is_object($res)) {
                if ($var = $res->fetchObject()) {
                    traceDebug("canal", "contient8");
                    $res->closeCursor();
                    $ret = true;
                }
            }
        } else if (in_array($id_type, array(EDI_EVENT_CATALOGUE_ARTICLE_ADD, EDI_EVENT_CATALOGUE_ARTICLE_DEL))) {
            $ret = false;

            $liste_categ = array();
            $catalogues_valides = $this->getCataloguesValides();
            // Cas de l'option multi catalogues
            if ($catalogues_valides) {
                // On teste les categs de tous les catalogues valides
                foreach ($catalogues_valides as $catalogue_valide) {
                    $catalogue = new _catalogue_client($catalogue_valide);
                    $liste_categ = array_merge($liste_categ, $catalogue->getArrayListes());
                }
            }
            // Cas classique
            else {
                $catalogue = new _catalogue_client($this->canal->getID_CATALOGUE());
                if ($catalogue->getId() == $this->canal->getID_CATALOGUE()) {
                    $liste_categ = $catalogue->getArrayListes();
                }
            }

            // On teste parmi toutes les categs valides
            if (in_array($param, $liste_categ)) {
                traceDebug("canal", "contient6");
                $ret = true;
            }
        } else if ($this->canal->getPARAMS("sync_clients") && in_array($id_type, array(EDI_EVENT_CREATE_CLIENT, EDI_EVENT_UPDATE_CLIENT))) {
            $ret = true;
        } else if ($this->canal->getPARAMS("sync_categ_client") && in_array($id_type, array(EDI_EVENT_UPDATE_CLIENT))) {
            $ret = true;
        }
        if ($ret !== true) {
            traceDebug("canal", "CONTIENT PAS");
        }

        //On dédoublone les messages ne devant pas être en double
        if ($ret === true) {
            if (in_array($id_type, array(
                EDI_EVENT_UPDATE_ARTICLE_LISTE,
                EDI_EVENT_UPDATE_ETAT_CMD,
                EDI_EVENT_DELETE_ARTICLE,
                EDI_EVENT_UPDATE_LINE_DOC,
                EDI_EVENT_UPDATE_CARAC,
                EDI_EVENT_UPDATE_CATEG,
                EDI_EVENT_UPDATE_TRACKING,
                EDI_EVENT_UPDATE_ART_TARIFS,
                EDI_EVENT_UPDATE_ART_STATUT,
                EDI_EVENT_UPDATE_ART_TAXES,
                EDI_EVENT_UPDATE_ART_PJ,
                EDI_EVENT_CREATE_ART_CARAC,
                EDI_EVENT_UPDATE_ART_CARAC,
                EDI_EVENT_UPDATE_ART_CATEG,
                EDI_EVENT_UPDATE_ART_LIAISONS,
                EDI_EVENT_UPDATE_ART_POSITIONS_IMAGES,
                EDI_EVENT_UPDATE_STOCK_ART,
                EDI_EVENT_UPDATE_PARTICIPATION_LISTE,
                EDI_EVENT_UPDATE_LISTE_SOUHAITS,
                EDI_EVENT_RESYNC_CANAL,
                EDI_EVENT_RESYNC_DOC,
                EDI_EVENT_UPDATE_ETAT_DOC,
                EDI_EVENT_SYNCRO_CATEG,
                EDI_EVENT_SYNCRO_ART_CATEG,
                EDI_EVENT_SYNCRO_ART_CATEGS,
                EDI_EVENT_SYNCRO_CONSTRUCTEURS,
                EDI_EVENT_SYNCRO_FOURNISSEURS,
                EDI_EVENT_UPDATE_CLIENT,
                EDI_EVENT_SYNCRO_CATEGS_CLIENT,
                EDI_EVENT_RESYNC_STOCK,
                EDI_EVENT_RESYNC_ARTICLE,
                EDI_EVENT_REINIT_ART_CARACS,
                EDI_EVENT_RESYNC_ART_CARACS,
                EDI_EVENT_RESYNC_ART_IMG))) {
                //Dans le cas d'un message de type "mise à jour", on controle tout event pas encore executé
                // ERREUR etat = 0, n'est pas fiable trouver autre chose....
                $query = "SELECT id FROM edi_events_queue
                    WHERE id_canal = " . $bdd->quote($this->canal->getID_CANAL()) . "
                    && id_event_type = " . $bdd->quote($id_type) . "
                    && ref_element " . ($ref?" = " . $bdd->quote($ref):"IS NULL")."
                    && param ".($param?" = " . $bdd->quote($param):"IS NULL") . "
                    && etat = ".EDI_MESSAGE_ETAT_OK;
                //Semble ne pas résoudre totalement le soucis de doublons, dans le cas ou edi_event() est appelé
                // quasiment en simultané, l'insertion du précédent n'a pas encore été prise en compte au moment de cette vérification...
            } else if ($id_type == EDI_EVENT_UPDATE_ARTICLE) {
                $ref_trouve = $this->canal->getRef_externe($ref, array(LIAISON_TYPE_ARTICLE_VARIANTE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT, LIAISON_TYPE_ARTICLE));
                if (empty($ref_trouve)) {
                    $query = "SELECT id FROM edi_events_queue
                        WHERE id_canal = " . $bdd->quote($this->canal->getID_CANAL()) . "
                        && id_event_type = " . $bdd->quote($id_type) . "
                        && ref_element " . ($ref?" = " . $bdd->quote($ref):"IS NULL")."
                        && param ".($param?" = " . $bdd->quote($param):"IS NULL") . "
                        && etat IN (".EDI_MESSAGE_ETAT_OK.", ".EDI_MESSAGE_ETAT_TRAITE .")";
                } else {
                    $query = "SELECT id FROM edi_events_queue
                        WHERE id_canal = " . $bdd->quote($this->canal->getID_CANAL()) . "
                        && id_event_type = " . $bdd->quote($id_type) . "
                        && ref_element " . ($ref?" = " . $bdd->quote($ref):"IS NULL")."
                        && param ".($param?" = " . $bdd->quote($param):"IS NULL") . "
                        && etat = ".EDI_MESSAGE_ETAT_OK;
                }
            } else {
                //Dans les autres cas (type création) on controle même les events déja executés, par exemple dans le cas ou la correspondance n'a pas encore été recue
                $query = "SELECT id FROM edi_events_queue
                    WHERE id_canal = " . $bdd->quote($this->canal->getID_CANAL()) . "
                    && id_event_type = " . $bdd->quote($id_type) . "
                    && ref_element " . ($ref?" = " . $bdd->quote($ref):"IS NULL")."
                    && param ".($param?" = " . $bdd->quote($param):"IS NULL")."
                    && etat != ".EDI_MESSAGE_ETAT_IGNORE." AND etat != ".EDI_MESSAGE_ETAT_ERREUR." AND etat != ".EDI_MESSAGE_ETAT_CRITIQUE;
            }
            $res = $bdd->query($query);
            if ($res->rowCount()) {
                $ret = "Doublon";
            }
        }
        
        if ($ret === true) {
            if (in_array($id_type, $this->events_catalogue['art'])) {
                if ($this->canal->IsNotAuthorizedFlux("catalogue")) {
                    traceDebug("canal", "Flux non autorisé (Catalogue > ART)");
                    $ret = "Flux non autorisé (Catalogue > ART)";
                }
                if ($ret === true) {
                    $canal_params = $this->canal->getParams();
                    if (!empty($canal_params['synchro_fournisseurs'])) {
                        if ($id_type == EDI_EVENT_CREATE_FOURNISSEUR) {
                            $ref_ext = $this->canal->getRef_externe($param, EDI_EVENT_CREATE_FOURNISSEUR);
                            $ret = empty($ref_ext);
                            if (!$ret) {
                                $ret = "Le fournisseur existe déjà.";
                            }
                        }
                    }
                    if (!empty($canal_params['syncro_article_unitaire'])) {
                        $art = article::getInstance($ref);
                        if ($art->getRef_article()) {
                            $canaux = explode(";", $art->getCaracValue($canal_params['syncro_article_unitaire']));
                            traceDebug("accepte", "synchro unitaire autorisée sur canal : " . $art->getCaracValue($canal_params['syncro_article_unitaire']));
                            $ret = in_array($this->canal->getID_CANAL(), $canaux);
                            if ($ret) {
                                traceDebug("accepte", "synchro unitaire acceptée sur canal " . $this->canal->getID_CANAL());
                            } else {
                                trace("accepte", "synchro unitaire refusée sur canal " . $this->canal->getID_CANAL());
                                $ret = "Synchronisation unitaire refusée sur ce canal";
                            }
                        } else {
                            $art = article::getInstance($param);
                            if ($art->getRef_article()) {
                                $canaux = explode(";", $art->getCaracValue($canal_params['syncro_article_unitaire']));
                                traceDebug("accepte", "synchro unitaire autorisée sur canal : " . $art->getCaracValue($canal_params['syncro_article_unitaire']));
                                $ret = in_array($this->canal->getID_CANAL(), $canaux);
                                if ($ret) {
                                    traceDebug("accepte", "synchro unitaire acceptée sur canal " . $this->canal->getID_CANAL());
                                } else {
                                    trace("accepte", "synchro unitaire refusée sur canal " . $this->canal->getID_CANAL());
                                    $ret = "Synchronisation unitaire refusée sur ce canal";
                                }
                            }
                        }
                    }
                }
            }

            if (in_array($id_type, $this->events_catalogue['stock'])) {
                if ($this->canal->IsNotAuthorizedFlux("stock")) {
                    traceDebug("canal", "Flux non autorisé (Catalogue > STOCK)");
                    $ret = "Flux non autorisé (Catalogue > STOCK)";
                }
            }

            if (in_array($id_type, $this->events_majcmd)) {
                if ($this->canal->IsNotAuthorizedFlux("majcommande")) {
                    traceDebug("canal", "Flux non autorisé (Maj Commande)");
                    $ret = "Flux non autorisé (Maj Commande)";
                }
            }
            
            if (in_array($id_type, $this->events_modifcmd)) {
                if ($this->canal->IsNotAuthorizedFlux("modifcommande")) {
                    traceDebug("canal", "Flux non autorisé (Modif Commande)");
                    return "Flux non autorisé (Modif Commande)";
                }
            }
        }
        return $ret;
    }

    public function install_canal_distant() {
        $url = $this->canal->getSITE_DISTANT() . "/modules/edi/_externals/liaison/install_canal.php?id_canal=" . $this->canal->getID_CANAL() . "&code_connection=" . $this->canal->getCODE_CONNECTION() . "&mail_alert=" . ConfigMailAlert() . "&edi_type=6";

        $parts = parse_url($url);

        $fp = fsockopen($parts['host'], isset($parts['port']) ? $parts['port'] : 80, $errno, $errstr, 10);
        if (!$fp) {
            trace("process", "Erreur Installation (echec connexion)");
            throw new Exception(__FUNCTION__);
        }
        $out = "POST " . $parts['path'] . " HTTP/1.1\r\n";
        $out.= "Host: " . $parts['host'] . "\r\n";
        $out.= "Content-Type: application/x-www-form-urlencoded\r\n";
        if (isset($parts['query'])) {
            $out.= "Content-Length:" . strlen($parts['query']) . "\r\n";
        } else {
            $out.= "Content-Length: 0\r\n";
        }
        $out.= "Connection: Close\r\n\r\n";
        if (isset($parts['query'])) {
            $out.= $parts['query'];
        }
        fwrite($fp, $out);
        fclose($fp);
    }

    public function getDestination() {
        return $this->canal->getSITE_DISTANT() . "/modules/edi/_externals/liaison/distant.php?serial_code=" . $this->canal->getCODE_CONNECTION();
    }

    public function create_categorie($id_categ, $id_catalogue) {
        try {
            if ($this->canal->getParams('default_not_sync_categ')) {
                return false;
            }
            if (empty($id_categ)) {
                trace_error($this->suffix_trace, "create_categorie: arguement vide");
                return false;
            }
            $categorie = new catalogue_categ($id_categ);
            $id_categorie = $categorie->getId_catalogue_categ();
            if (empty($id_categorie)) {
                return false;
            }
            if ($this->canal->getID_CATALOGUE() == $id_catalogue) {
                trace($this->suffix_trace, "******************************  DEBUT create_categorie " . $id_categ . " ***********************");
                $ref_externe = $this->canal->getRef_externe($id_categ, LIAISON_TYPE_ART_CATEG);
                if (!empty($ref_externe)) {
                    edi_event(EDI_EVENT_UPDATE_CATEG, $id_categ, null, $this->canal->getId_canal());
                    return false;
                }
                $infos = $this->getInfosCategorie($categorie);
                $infos['ref_art_categ'] = $id_categ;
                $infos['id_ref_type'] = LIAISON_TYPE_ART_CATEG;
                $infos['evt_name'] = __FUNCTION__;

                trace($this->suffix_trace, "*********************** FIN create_categorie " . $id_categ . " ************************");
                return $infos;
            }
        } catch (Exception $e) {
            throw $e;
        }

        return false;
    }

    public function update_categorie($id_categ) {
        if ($this->canal->getParams('default_not_sync_categ')) {
            return false;
        }

        if (empty($id_categ)) {
            trace_error($this->suffix_trace, "create_categorie: arguement vide");
            return false;
        }

        $categorie = new catalogue_categ($id_categ);
        $id_categorie = $categorie->getId_catalogue_categ();
        if (empty($id_categorie)) {
            return false;
        }

        trace($this->suffix_trace, "******************************  DEBUT update_categorie " . $id_categ . " ***********************");

        $ref_externe = $this->canal->getRef_externe($id_categ, LIAISON_TYPE_ART_CATEG);
        if (empty($ref_externe)) {
            edi_event(EDI_EVENT_CREATE_CATEG, $id_categ, $categorie->getId_catalogue(), $this->canal->getId_canal());
            return false;
        }

        $infos = $this->getInfosCategorie($categorie);
        $infos['ref_art_categ'] = $ref_externe;
        if (!empty($infos['ref_art_categ_parent'])) {
            edi_event(EDI_EVENT_DELETE_CATEG, $id_categ, $categorie->getId_catalogue(), $this->canal->getId_canal());
            return false;
        } else {
            $infos['evt_name'] = __FUNCTION__;
        }
        trace($this->suffix_trace, "*********************** FIN update_categorie " . $id_categ . " ************************");
        return $infos;
    }

    public function delete_categorie($ref_categ, $id_catalogue) {
        
        if ($this->canal->getParams('default_not_sync_categ')) {
            return false;
        }

        if ($this->canal->getID_CATALOGUE() == $id_catalogue) {
            trace($this->suffix_trace, "******************************  DEBUT delete_categorie " . $ref_categ . " ***********************");
            $ref_externe = $this->canal->getRef_externe($ref_categ, LIAISON_TYPE_ART_CATEG);
            if (empty($ref_externe)) {
                trace($this->suffix_trace, "delete_categorie $ref_categ, référence non trouvée");
                return false;
            }
            $this->canal->deleteRef($ref_categ, LIAISON_TYPE_ART_CATEG);
            $message['ref_art_categ'] = $ref_externe;
            $message['ref_externe'] = $ref_externe;
            $message['evt_name'] = __FUNCTION__;
            trace($this->suffix_trace, "******************************  FIN delete_categorie " . $ref_categ . " ***********************");
            return $message;
        }
        return false;
    }

    public function create_article($ref_art_categ, $ref_article) {
        trace($this->suffix_trace, "************************** DEBUT create_article " . $ref_article . " ***************************");

        $article = article::getInstance($ref_article, true);
        if ($article === false || $article->getRef_article() != $ref_article) {
            return false;
        }
        if ($article->getStatut_groupe() == article::BDD_TYPE_DECLINAISON) {
            $groupe = articles_groupes::init($article->getId_article_groupe());
            trace("envoi", "Finalement c'est une variante: " . $ref_article);
            edi_event(EDI_EVENT_CREATE_VARIANTE, $groupe->get_ref_article_parent(), $ref_article, $this->canal->getId_canal());
            return false;
        }

        $var = $this->canal->getRef_externe($ref_article);
        if (!empty($var)) {
            edi_event(EDI_EVENT_UPDATE_ARTICLE, $ref_article, null, $this->canal->getId_canal());
            return false;
        }

        if ($this->is_only_sub_catg($article)) return false;

        $tab = $this->getInfosArticle($article);
        $this->deleteEventOk(EDI_EVENT_DELETE_ARTICLE, $ref_article);
        $tab['evt_name'] = __FUNCTION__;
        $tab['id_ref_type'] = LIAISON_TYPE_ARTICLE;

        trace($this->suffix_trace, "envoi : " . json_encode($tab));
        trace($this->suffix_trace, "************************** FIN create_article ***************************");
        return $tab;
    }

    public function delete_article($ref_article) {
        $article = article::getInstance($ref_article, true);
        $id_product = $this->canal->getRef_externe($ref_article);
        if (empty($id_product)) {
            trace_error($this->suffix_trace, "delete_article $ref_article, référence non trouvée");
            return false;
        }

        $message = [];
        $message["evt_name"] = "delete_article";
        $message["statut_groupe"] = $article->getStatut_groupe();
        $message['ref_article'] = $id_product;
        $this->canal->deleteRef($ref_article, [LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_GROUPE, LIAISON_TYPE_ARTICLE_VARIANTE]);
        $this->deleteEventOk(EDI_EVENT_CREATE_ARTICLE, null, $ref_article);

        return $message;
    }

    public function create_variante($ref_article_parent, $ref_article_variant) {
        trace($this->suffix_trace, "**************************\nDEBUT create_variante " . $ref_article_parent . "," . $ref_article_variant . "\n***************************\n");
        $ref_externe_parent = $this->canal->getRef_externe($ref_article_parent, array(LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT));
        if (empty($ref_externe_parent)) {
            $article_parent = article::getInstance($ref_article_parent);
            if (empty($article_parent->getDispo())) {
                trace($this->suffix_trace, "Abandon create_variante " . $ref_article_parent . "," . $ref_article_variant . ", l'article parent est introuvable ou archivé");
            } else {
                $this->canal->trace_error("envoi", "L'article parent ($ref_article_parent) de la variante n'est pas encore lié");
                edi_event(EDI_EVENT_CREATE_ARTICLE, null, $ref_article_parent, $this->canal->getId_canal());
            }
            return false;
        }
        $var = $this->canal->getRef_externe($ref_article_variant);
        if (!empty($var)) {
            return false;
        }

        $article_variante = article::getInstance($ref_article_variant);
        $variante = $this->getInfosArticle($article_variante);
        $variante['ref_externe_parent'] = $ref_externe_parent;
        $variante['variante_caracs'] = $this->get_caracs_declinaisons($article_variante);
        $variante['id_ref_type'] = LIAISON_TYPE_ARTICLE_VARIANTE;
        $variante['id_ref_type_carac_variant'] = LIAISON_TYPE_ART_CATEGS_CARACS_VARIANT;
        $variante['evt_name'] = __FUNCTION__;
        $this->deleteEventOk(EDI_EVENT_DELETE_ARTICLE, $ref_article_variant);
        return $variante;
    }

    public function create_art_img($ref_article, $id_image) {
        $bdd = \PDO_etendu::getInstance();
        global $DIR;

        trace($this->suffix_trace, "image ref_article " . $ref_article);
        $image = array();

        $image['ref_article'] = $this->canal->getRef_externe($ref_article, array(LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT, LIAISON_TYPE_ARTICLE_VARIANTE));
        $image['id_image'] = $id_image;
        
        $article = article::getInstance($ref_article, true);
        
        if ($article->getStatut_groupe() == article::BDD_TYPE_DECLINAISON) {
            $groupe = articles_groupes::init($article->getId_article_groupe());
            $ref_article_parent = $groupe->get_ref_article_parent();
            $image['ref_parent'] = $ref_article_parent;
            $image['ref_externe_parent'] = $this->canal->getRef_externe($ref_article_parent, array(LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT));
            if (empty($image['ref_externe_parent'])) {
                throw new edi_exception("Parent Product haven't created yet");
            }
        }

        $ref_ext_image = $this->canal->getRef_externe($image['id_image'], LIAISON_TYPE_IMAGES);
        $image['ref_ext_image'] = $ref_ext_image;
        traceDebug($this->suffix_trace, "ref externe (peut etre vide): $ref_ext_image");

        if (!empty($image['ref_article'])) {
            if (empty($ref_ext_image) && !empty($id_image)) {
                if (isset($image['ref_parent']) && !empty($image['ref_parent'])) {
                    $query = "SELECT id_article_image FROM articles_images
                            WHERE id_image = ". $bdd->quote($image['id_image']) ." AND ref_article = ". $bdd->quote($image['ref_parent']);
                    $res = $bdd->query($query);
                    if (!$res->rowCount()) {
                        throw new edi_exception('create_art_img - Waiting for creating linking');
                    }
                    return;
                }
                $query = "SELECT lib_file FROM images_articles WHERE id_image = '$id_image' ;";

                $stt = $bdd->query($query);
                if (is_object($stt) && $img = $stt->fetchObject()) {

                    traceDebug($this->suffix_trace, "image ref_article distant " . $image['ref_article']);
                    $img_path = $DIR . "fichiers/images/articles/" . $img->lib_file;
                    if (!is_file($img_path)) {
                        trace_error("image", "Le fichier (" . $img_path . ") est introuvable");
                        return false;
                    }
                    $image['url'] = $this->racine_url . "fichiers/images/articles/" . $img->lib_file;
                    traceDebug($this->suffix_trace, "image ajoute");
                    $image['evt_name'] = __FUNCTION__;
                    $image['id_ref_type'] = LIAISON_TYPE_IMAGES;
                    return $image;
                }
                traceDebug($this->suffix_trace, "image non dispo");
                return false;
            } else if (!empty($id_image)) {
                $image['evt_name'] = __FUNCTION__;
                return $image;
            }
            return false;
        } else {
            return false;
        }
       
    }

    public function delete_art_img($ref_article, $id_img) {
        $ref_ext_image = $this->canal->getRef_externe($id_img, LIAISON_TYPE_IMAGES);
        if(empty($ref_ext_image)) return false;
        
        $article = article::getInstance($ref_article, true);
        if ($article->getStatut_groupe() == article::BDD_TYPE_DECLINAISON) {
            $groupe = articles_groupes::init($article->getId_article_groupe());
            $ref_parent_article = $groupe->get_ref_article_parent();
            $ref_ext_parent_article = $this->canal->getRef_externe($ref_parent_article, array(LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT,LIAISON_TYPE_ARTICLE_VARIANTE));
            if (empty($ref_ext_parent_article)) {
                throw new edi_exception("Parent Product haven't created yet");
            } else {
                $image['ref_ext_parent_article'] = $ref_ext_parent_article;
            }
        } else {
            $this->canal->deleteRef($id_img, LIAISON_TYPE_IMAGES);
        }
        
        $ref_ext_article = $this->canal->getRef_externe($ref_article, array(LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT,LIAISON_TYPE_ARTICLE_VARIANTE));
        $image['ref_ext_article'] = $ref_ext_article;
        $image['id_image'] = $ref_ext_image;
        $image['evt_name'] = __FUNCTION__;
        return $image;
    }

    public function resync_art_images($ref_article) {
        $bdd = \PDO_etendu::getInstance();

        $article = article::getInstance($ref_article);

        trace("envoi", "resync_art_images:");
        $images = $article->getImages();
        if ($article->getStatut_groupe() != articles_groupes::$statut_enfant && !empty($images)) {
            foreach ($images as $image) {
                $img = $this->canal->getRef_externe($image->id_image, LIAISON_TYPE_IMAGES);
                trace("envoi", "ID image : " . $image->id_image . " | Corresp : " . $img);
                if ($img) {
                    trace("envoi", "delete_ref image: " . $img);

                    $query = "DELETE FROM edi_events_queue
                            WHERE id_canal = " . $bdd->quote($this->canal->getId_canal()) . "
                            AND id_event_type = " .$bdd->quote(EDI_EVENT_DELETE_ART_IMAGE) . "
                            AND ref_element = " . $bdd->quote($ref_article) . "
                            AND param = " . $bdd->quote($image->id_image) . "
                            AND etat != 0";

                    $bdd->exec($query);

                    edi_event(EDI_EVENT_DELETE_ART_IMAGE, $ref_article, $image->id_image, $this->canal->getId_canal());
                }
                trace("envoi", "create_ref image: " . $image->id_image);

                $query = "DELETE FROM edi_events_queue
                            WHERE id_canal = " . $bdd->quote($this->canal->getId_canal()) . "
                            AND id_event_type = " .$bdd->quote(EDI_EVENT_CREATE_ART_IMAGE) . "
                            AND ref_element = " . $bdd->quote($ref_article) . "
                            AND param = " . $bdd->quote($image->id_image) . "
                            AND etat != 0";

                $bdd->exec($query);

                edi_event(EDI_EVENT_CREATE_ART_IMAGE, $ref_article, $image->id_image, $this->canal->getId_canal());
            }
        }
        return false;
    }

    public function update_art_carac($ref_article, $id_carac) {
        return false;
    }

    public function update_stock_art($ref_article) {
        $article = article::getInstance($ref_article);
        if (!$article->getRef_article()) {
            throw new edi_exception("Erreur lors de la récupération de l'article " . $ref_article);
        }
        
        $ref_externe = $this->canal->getRef_externe($ref_article);
        if (empty($ref_externe)) {
            trace($this->suffix_trace, "update_stock_art ignoré : $ref_article n'est pas synchronisé");
            return false;
        }

        $ids_stock = $this->canal->getIDs_STOCK();
        $tab = [];
        $tab['qte'] = $this->getStock_dispo($article, $ids_stock);

        traceDebug($this->suffix_trace, "Stock $ref_article = " . print_r($tab["qte"], true));

        $tab["variante"] = $article->getStatut_groupe() == article::BDD_TYPE_DECLINAISON;
        $tab["is_service"] = $article->getModele() == 'service';
        $tab['ref_article'] = $ref_externe;
        $tab['evt_name'] = __FUNCTION__;

        trace($this->suffix_trace, "envoi : " . json_encode($tab) . "\n");

        return $tab;
    }

    public function update_article_categorie($force, $ref_article) {
        $article = article::getInstance($ref_article);
        if (!$article->getRef_article()) {
            throw new edi_exception("Erreur lors de la récupération de l'article " . $ref_article);
        }
        
        $ref_externe = $this->canal->getRef_externe($ref_article);
        $tab = [];

        //GESTION CATEGORIES
        $contents = $article->getCatalogueContent($this->canal->getID_CATALOGUE());
        $tab['id_catalogue_categories'] = [];
        foreach ($contents as $content) {
            if($content->getId_catalogue_categ() == $this->canal->getParams('default_catg')) {
                $tab['id_default_catg'] = $content->getId_catalogue_categ();
            } else {
                $tab['id_catalogue_categories'][] = $this->canal->getRef_externe($content->getId_catalogue_categ(), LIAISON_TYPE_ART_CATEG);
            }
        }

        if ((empty($tab['id_catalogue_categories']) && !isset($tab['id_default_catg'])) || $this->is_only_sub_catg($article, $contents)) {
            if (!empty($ref_externe)) {
                edi_event(EDI_EVENT_DELETE_ARTICLE, $ref_article, null, $this->canal->getId_canal());
                return false;
            }
            return false;
        } else if (empty($ref_externe)) {
            edi_event(EDI_EVENT_CREATE_ARTICLE, null, $ref_article, $this->canal->getId_canal());
            return false;
        } else {
            $tab['id_product'] = $ref_externe;
            $tab['new_id_category'] = $tab['id_catalogue_categories'];
            $tab['evt_name'] = __FUNCTION__;
    
            trace($this->suffix_trace, "envoi : " . json_encode($tab) . "\n");
    
            return $tab;
        }
    }

    protected function is_only_sub_catg($article, $catelogue_contents = null) {
        if (empty($catelogue_contents)) {
            $catelogue_contents = $article->getCatalogueContent($this->canal->getID_CATALOGUE());
        }
        foreach ($catelogue_contents as $content) {
            $categorie = new catalogue_categ($content->getId_catalogue_categ());
            if (empty($categorie->getId_catalogue_categ_parent())) {
                return false;
            }
        }
        return true;
    }

    public function modif_etat_cmd($ref_doc, $param) {
        $ref_externe = $this->canal->getRef_externe($ref_doc, LIAISON_TYPE_COMMANDE);
        if (empty($ref_externe)) {
            trace("envoi", "Le doc " . $ref_doc . " n'est pas disponible sur le canal ".$this->canal->getID_CANAL());
            return false;
        }

        $cdc = document::getTypeInstance($ref_doc);
        $tab = [
            'ref_doc_ext' => $ref_externe,
            'ref_lmb' => $ref_doc
        ];
        switch ($param) {
            case doc_cdc::ETAT_EN_COURS:
                $reglements = $cdc->getReglements();
                $reglements = array_filter($reglements, function ($reglement) {
                    $ref_externe = $this->canal->getRef_externe($reglement->ref_reglement, LIAISON_TYPE_REGLEMENTS);
                    return empty($ref_externe);
                });
                if(count($reglements) > 0) {
                    $tab['reglements'] = $reglements;
                } else {
                    return false;
                }
                $tab['id_ref_type_reglement'] = LIAISON_TYPE_REGLEMENTS;
                $tab['evt_name'] = 'create_transaction';
                break;
            case doc_cdc::ETAT_ANNULEE:
                $this->canal->deleteRef($ref_doc, LIAISON_TYPE_COMMANDE);
                $tab['evt_name'] = 'cancel_order';
                break;
            default :
                return false;
        }
        return $tab;
    }

    public function getDefautCorrespEtatsCmd() {
        return array(
            ETAT_EN_COURS  => 'processing',
        );
    }

    public function update_etat_doc($ref_doc, $params) {
        try {
            $this->canal->trace($this->suffix_trace, 'Ref doc update : ' . $ref_doc);
            $doc = document::getTypeInstance($ref_doc);

            $id_type_doc = $doc->getID_TYPE_DOC();
            if ($id_type_doc != document::BLC) {
                return false;
            }
            if (method_exists($doc, 'getCommandesBase')) {
                $cdcs = $doc->getCommandesBase();
                $ref_cdc = reset($cdcs);
            }
            if (!empty($ref_cdc)) {
                $this->canal->trace($this->suffix_trace, 'Ref CDC update : ' . $ref_cdc);
                $ref_ext = $this->canal->getRef_externe($ref_cdc, LIAISON_TYPE_COMMANDE);
            }

            if (empty($ref_ext)) {
                trace_error($this->suffix_trace, "commande:{$ref_cdc} référence non trouvée");
                return false;
            }
            
            $tab = [
                'order_ref_external' => $ref_ext,
                'ref_doc' => $ref_doc,
                'id_ref_type' => LIAISON_TYPE_BLC
            ];

            switch ($params) {
                case doc_blc::ETAT_LIVRE:
                    $tab['evt_name'] = 'create_fulfillment';
                    $transports = $doc->getInfosTransport();
                    if (!empty($transports)) {
                        $transport = reset($transports);
                        $tracking_info = $this->transport_attr($transport);
                        $tab = array_merge($tab, $tracking_info);
                    }
                    break;
                default :
                    return false;
            }
            return $tab;

        } catch (Throwable $e) {
            trace_error($this->suffix_trace, 'update_etat_doc : ' . $e->getMessage());
            throw new edi_exception($e->getMessage(), $e->getCode());
        }  
    }

    protected function transport_attr($transport) {
        $tab = [];
        $tab['carrier'] = $transport->getLivraison_mode()->getArticle()->getLib_ticket();
        $colis = $transport->getColis();
        $tab['colis'] = [];

        foreach ($colis as $coli) {
            $item = [];
            if (!empty($coli['numero_colis'])) {
                $item['tracking_number'] = $coli['numero_colis'];
                $module_transport = $transport->getModuleTransport();
                if (method_exists($module_transport, 'getTrackingUrlFromTransport')) {
                    $item['tracking_url'] = $module_transport->getTrackingUrlFromTransport($transport, $coli['numero_colis']);
                } elseif (method_exists($module_transport, 'getTrackingUrl')) {
                    $item['tracking_url'] = $module_transport->getTrackingUrl($coli['numero_colis']);
                }
                if (empty($item['tracking_url']) && !empty($coli['lien_tracking'])) {
                    $item['tracking_url'] = $coli['lien_tracking'];
                }
                $tab['colis'][] = $item;
            }
        }

        if (!empty($tab['colis'])) {
            $tab['tracking_number'] = $transport->getNumero_expedition();
        }
        return $tab;
    }

    public function update_tracking($ref_doc, $id_expedition) {
        $ref_externe = $this->canal->getRef_externe($ref_doc, LIAISON_TYPE_COMMANDE);
        if (empty($ref_externe)) {
            // Si la liaison n'existe pas, c'est que la commande n'appartient pas à ce canal
            trace($this->suffix_trace, "Le doc " . $ref_doc . " n'est pas disponible sur le canal ".$this->canal->getID_CANAL());
            return false;
        }
        $transport = new transport($id_expedition);
        $infos = $this->transport_attr($transport);
        if (!empty($infos)) {
            $infos['order_ref_external'] = $ref_externe;
            $infos['evt_name'] = __FUNCTION__;
        } else {
            trace_error($this->suffix_trace, "Aucun numéro de colis à transmettre ($ref_doc / $id_expedition)");
        }
        return $infos;
    }

    protected function getStock_dispo(&$article, $ids_stock) {
        $ignore_stock_reserve = $this->canal->getPARAMS("ignore_stock_reserve");
        if ($article->getLot() == article::BDD_LOT_COMPO_INTERNE) {
            $max_tofab = 0;
            $countcompo = 0;
            $art_composants = $article->getComposants ();
            foreach ($art_composants as $composant) {
                if (empty($composant->qte))
                    $composant->qte=1;

                if ($composant->modele != 'materiel')
                    continue;

                $art = article::getInstance($composant->ref_article_composant);
                $stock = $this->calcul_stock_dispo($art, $ids_stock, $ignore_stock_reserve);
                if (qte_format($stock / $composant->qte) > 0) {
                    if ($max_tofab == 0 && $countcompo == 0) {
                        $max_tofab = floor($stock / $composant->qte);
                        $countcompo = 1;
                    }
                    $max_tofab = min($max_tofab, floor($stock / $composant->qte));
                }
                if (qte_format($stock / $composant->qte) <= $max_tofab) {
                    $max_tofab = floor($stock / $composant->qte);
                    $countcompo = 1;
                }
                else if (qte_format($stock / $composant->qte) == 0) {
                    $max_tofab = 0;
                    $countcompo = 1;
                }
            }
            $stock_dispo = $max_tofab;
        } else {
            $stock_dispo = $this->calcul_stock_dispo($article, $ids_stock, $ignore_stock_reserve);
        }
        
        $reserve_tous_stock = $this->canal->getPARAMS("reserve_tous_stock");
        if (!empty($reserve_tous_stock) && $article->isStockable()) {
            $stocks_rsv = $article->getStocks_rsv();
            foreach ($stocks_rsv as $id_stock => $stock_rsv) {
                $stock_dispo -= ( $stock_rsv->qte - $stock_rsv->qte_livree);
            }
        }
        
        return $stock_dispo;
    }

    protected function getInfosArticle($article) {
        $ref_article = $article->getRef_article();
        $tab = [];
        $article = article::getInstance($ref_article, true);
        if ($article === false || $article->getRef_article() != $ref_article) {
            return false;
        }

        //GESTION CATEGORIES
        $contents = $article->getCatalogueContent($this->canal->getID_CATALOGUE());
        $tab['id_catalogue_categorie'] = "";
        $tab['id_catalogue_categories'] = [];
        foreach ($contents as $content) {
            if($content->getId_catalogue_categ() == $this->canal->getParams('default_catg')) {
                $tab['id_default_catg'] = $content->getId_catalogue_categ();
            } else {
                if ($content->getPrincipal())
                    $tab['id_catalogue_categorie'] = $this->canal->getRef_externe($content->getId_catalogue_categ(), LIAISON_TYPE_ART_CATEG);
                $tab['id_catalogue_categories'][] = $this->canal->getRef_externe($content->getId_catalogue_categ(), LIAISON_TYPE_ART_CATEG);
            }
        }
        if (empty($tab['id_catalogue_categorie']) && !isset($tab['id_default_catg'])) {
            throw new edi_exception("getInfosArticle: La categ n'est pas encore syncro");
        }

        //GESTION INFOS GENERALES
        $tab['ref_lmb'] = $ref_article;
        $tab['ref_interne'] = $article->getRef_interne();
        $tab['lib'] = $article->getLib_article();

        if (!$this->canal->getParams('skip_desc')) {
            $tab['desc_courte'] = $article->getDesc_courte_html();
            $tab['desc_longue'] = $article->getDesc_longue_html();
        }

        $tab['tags'] = implode(",", $article->getTags());
        $tab['code_barre'] = $article->getCode_barre();
        $id_marque = $article->getId_marque();
        $tab['lib_marque'] = $id_marque ? article::getLibMarque($id_marque) :  "";
        $tab['statut_dispo'] = $article->getDispo() == 0 ? 0 : 1;
        $tab['date_debut_dispo'] = $article->getDate_debut_dispo_vente();
        $tab["is_service"] = $article->getModele() == 'service';

        switch ($this->canal->getCOL_REF()) {
            case 'ref_oem':
                $tab['reference'] = $article->getRef_oem();
                break;
            case 'ref_lmb':
                $tab['reference'] = $article->getRef_article();
                break;
            case 'ref_interne':
                $tab['reference'] = $article->getRef_interne();
                break;
            default :
                $tab['reference'] = '';
                break;
        }

        //GESTION TARIFS
        $prices = $this->get_price_with_promotion($article);
        $tab = array_merge($tab, $prices);

        //GESTION PARENT/ENFANT
        $tab['statut_declinaison'] = $article->getStatut_groupe();

        if ($tab['statut_declinaison'] == articles_groupes::$statut_enfant) {
            $groupe = articles_groupes::init($article->getId_article_groupe());
            $lst_caracs_declinaisons = $groupe->get_lst_caracs_declinaisons();
            $is_variante = true;
            $tab_carac = array();
            foreach ($lst_caracs_declinaisons as $id_caracteristique => $lib_carac) {
                $id_carac = $this->canal->getRef_externe($id_caracteristique, array(LIAISON_TYPE_ART_CATEGS_CARACS_VARIANT, LIAISON_TYPE_ART_CATEGS_CARACS));

                $val_carac = $article->getCaracValue($id_caracteristique);
                if (empty($val_carac)) {
                    $is_variante = false;
                    break;
                } else {
                    $toadd = array("libelle" => $lib_carac, "valeur" => $val_carac);

                    if (!empty($id_carac)) {
                        $toadd['id_carac'] = $id_carac;
                    }
                    $tab_carac[] = $toadd;
                }
            }
            if ($is_variante) {
                $ref_article_parent = $groupe->get_ref_article_parent();
                if(!empty($groupe->getDefaut_article_enfant()) && $groupe->getDefaut_article_enfant()->getId_article() == $article->getId_article()){
                    $tab['is_defaut_variante'] = true;
                }
                $tab['ref_article_parent'] = $this->canal->getRef_externe($ref_article_parent, array(LIAISON_TYPE_ARTICLE, LIAISON_TYPE_ARTICLE_VARIANTE_PARENT));
                if (empty($tab['ref_article_parent'])) {
                    throw new edi_exception("getInfosArticle : $ref_article_parent parent de la variante $ref_article pas encore lié");
                }
            }
        }

        $tab['poids'] = $article->getPoidsView() ?? 0;
        
        $tab['caracs'] = array();
        $article_caracs = $article->getCaracs();
        if (!empty($article_caracs)) {
            foreach ($article_caracs as $carac_attributes) {
                $carac["id_carac"] = $carac_attributes->id_carac;
                $carac["lib_carac"] = $carac_attributes->lib_carac;
                $carac["valeursObject"] = $carac_attributes->valeursObject;
                $tab['caracs'][] = $carac;
            }
        }
        
        $tab['variante_caracs'] = $this->get_caracs_declinaisons($article);
        $tab['caracs_personnalisations'] = $article->getPersonnalisations();
        trace($this->suffix_trace, "envoi : " . json_encode($tab));
        return $tab;
    }

    protected function get_price_with_promotion($article) {
        $tarif = ArticleTarif::getTarif($article->getId_article(), $this->canal->getID_TARIF(), 1, false);
        $retour = [];
        if (empty($tarif->promo)) {
            $retour["pu_ht"] = !empty($tarif->pu_ht) ? $tarif->pu_ht : 0;
            $retour["pu_ttc"] = !empty($tarif->pu_ttc) ? $tarif->pu_ttc : 0;
        } else {
            $retour["pu_ht"] = !empty($tarif->promo->pu_ht) ? $tarif->promo->pu_ht : 0;
            $retour["old_pu_ht"] = !empty($tarif->pu_ht) ? $tarif->pu_ht : 0;
            $retour["pu_ttc"] = !empty($tarif->promo->pu_ttc) ? $tarif->promo->pu_ttc : 0;
            $retour["old_pu_ttc"] = !empty($tarif->pu_ttc) ? $tarif->pu_ttc : 0;
        }
            return $retour;
    }

    protected function get_caracs_declinaisons($article) {
        $data     = [];
        $groupe   = articles_groupes::init($article->getId_article_groupe());
        $caracs   = $groupe->get_lst_caracs_declinaisons();
        if ($article->getStatut_groupe() == article::BDD_TYPE_DECLINAISON) {
            foreach ($caracs as $id_carac => $lib_carac) {
                $value = article::getCaracValue_by_refarticle($article->getRef_article(), $id_carac);
                $val_carac = $this->getCaracValue($id_carac, $value);
                if (!empty($val_carac)) {
                    $value = $val_carac;
                }
                $data[$id_carac] = array(
                    "lib"   => $lib_carac,
                    "valeur" => empty($value) ? "N/A" : $value
                );
            }
        }
        return $data;
    }

}

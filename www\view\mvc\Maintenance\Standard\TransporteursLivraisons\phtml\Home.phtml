<?php
global $DIR;
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Maintenance/Standard/TransporteursLivraisons/javascript/_javascript_loader.php");
?>

<div id="idtemporaire" class="lmb-theme" ng-app="TransporteursLivraisonsModule">
    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading">Maintenance</div>
                <h1>Modules transporteurs et modes de livraison</h1>
            </div>
        </div>
    </header>

    <div class="container row">
        <aside class="sidebar">
            <div class="sidebar-content">
                <nav style="margin: 20px 0;">
                    <ul class="menu" style="background: transparent">
                        <li ui-sref-active="active">
                            <a ui-sref="transporteurs()">Modules transporteurs</a>
                        </li>
                        <li ui-sref-active="active">
                            <a ui-sref="modules()">Configuration de modules</a>
                        </li>
                        <li ui-sref-active="active">
                            <a ui-sref="livraisons()">Modes de livraisons</a>
                        </li>
                    </ul>
                </nav>

            </div>
        </aside>

        <div id="global" class="page-content">
            <div id="content-container" ui-view="content-container"></div>
        </div>
    </div>
</div>

<script>
    LMBToast.expert({
        title: "Avertissement sur l'utilisation",
        message: "Cet outil n’est pas encore validé et n’est à utiliser que par la maintenance",
        fixed: true
    });
</script>
<script type="text/javascript">
    angular.bootstrap($j("#idtemporaire")[0], ["TransporteursLivraisonsModule"]);
</script>
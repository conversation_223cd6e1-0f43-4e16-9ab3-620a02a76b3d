<?php
// Liste de tous les terminaux de paiement
$Terminaux_Paiement = terminaux_paiement::getAll();
$Terminaux = array();

foreach ($Terminaux_Paiement as $Terminal) {
    // Si il s'agit d'un Tpv ( TODO : Vérifier si le terminal est actif )
    if (!empty($Terminal->getClasse())) {
        $Terminaux[] = $Terminal;
    }
}
?>


<header class="jumbotron">
    <div class="container">
        <div class="jumbotron-title">
            <div class="jumbotron-heading">Règlement</div>

        </div>
    </div>
</header>


<?php
if (!isset($params['error'])) {
    ?>
    <div class="portlet" style="margin: 40px auto 20px auto ; width:600px;">
        <div class="portlet-header-flex">

        </div>
        <div class="portlet-body">

            <?php
            foreach ($Terminaux as $Terminal) {

                echo '
                        <div class="row">
                            <input type="radio" id="terminal_' . $Terminal->getId_Terminal_Paiement() . '" name="id_terminal" value="' . $Terminal->getId_Terminal_Paiement() . '"/>
                            <label for="terminal_' . $Terminal->getId_Terminal_Paiement() . '">' . $Terminal->getLib_Tp() . '</label>
                        </div>';
            }
            ?>

        </div>
        <div class="portlet-footer">
            <button type="button" class="btn btn-tertiary" onclick="Pay()">Payer</button>
        </div>
    </div>

    <div class="AjaxReply" style="display:none"></div>		
    </body>




    <script>
        function Pay()
        {
            var ID_Terminal = $("input[name='id_terminal']:checked").val();


            $.post("?id_terminal=" + ID_Terminal + "&id=<?php echo $params['contact']->getUuid_Lm(); ?>", function (reply) {

                if (reply.type == "redirect_URL")
                {
                    window.location = reply.form;
                }

                if (reply.type == "HTML")
                {
                    $('.AjaxReply').html(reply.form);
                    $('.AjaxReply form').submit();
                }

            });

        }
    </script>
    <?php
} else {
    ?>
    <div class="portlet" style="margin: 40px auto 20px auto ; width:600px;">
        Une erreur est survenue (<?php echo $params['error']; ?>)
    </div>

    <?php
}
?>

#tab-synthese-entrant .main-content, #tab-synthese-sortant .main-content{
	margin: 24px 0;
	display: flex;
	align-items: stretch;
	flex-wrap: wrap;
}
.graph-container {
	float: left;
	padding: 16px;
	border: 1px solid #ccc;
}
#page_journal_reglements .graph-container {
	width: 50%;
}
.summary-container {
	float: left;
}
#page_journal_ventes .summary-container {
	width: 25%;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-evenly;
	align-items: flex-end;
}
#page_journal_reglements .summary-container {
	width: 50%;
	padding: 0 48px;
}
#page_journal_reglements .summary-container > h3 {
	margin-top: 0;
}

.summary-stat {
	margin: 0 24px;
	flex: 0 1 calc(50% - 48px);
	text-align: center;
}
.summary-stat .stat-title {
	margin-bottom: 12px;
}
.summary-stat .stat-block {
	border: 1px solid #ccc;
	padding: 12px;
    margin-bottom: 35px;
}
.summary-stat .stat-block .icon {
	border-radius: 50%;
	margin: 12px auto;
	width: 48px;
	height: 48px;
	padding: 12px;
}
.summary-stat .stat-block .icon > img {
	width: 24px;
	height: 24px;
}
.summary-stat .stat-block .icon.icon-ca {
	background-color: #e2fadd;
}
.summary-stat .stat-block .icon.icon-marge {
	background-color: #fce6d4;
}
.summary-stat .stat-block .icon.icon-ventes {
	background-color: #fcd4dd;
}
.summary-stat .stat-block .icon.icon-panier {
	background-color: #dddff9;
}
.summary-stat .stat-main {
	font-size: 32px;
	font-weight: bold;
	line-height: 32px;
	margin-bottom: 0;
}

@media screen and (max-width: 1600px) {
	.summary-stat {
		margin: 0 12px;
	}
	#page_journal_ventes .criteria-column {
		width: 100%;
	}
}
@media screen and (max-width: 140px) {
	.summary-stat {
		flex: 0 1 180px;
	}
}

<?php

use LMBCore\Permissions\Constants\PermissionRef;

$user = user::getInstance();
?>

<div class="lmb-theme">
    <div class="portlet-body">
        <div class="content-menu-w">
            <div class="row  content-menu">
                <?php
                if ($this->view->canEditArticles || $this->view->canImportImages) {
                    ?>
                    <div class="content-menu__bloc  content-menu__bloc--blue  border-switch  portlet">
                        <div class="portlet-header">
                            <div class="text-left">
                                <h1><?php _e_html(180026, "Importer du contenu"); ?></h1>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="row">
                                <?php
                                if ($this->view->canEditArticles) {
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration"
                                           href="#page.php/Import/Standard/Article/insert:/fichier/" qa_id="190118">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(180027, "Importer des articles à partir d'un fichier"); ?>
                                        </a>
                                    </div>
                                    <?php
                                }
                                if ($this->view->canImportImages) {
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration" href="#page.php/Import/Standard/Images/index:/import">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(180028, "Importer des images"); ?>
                                        </a>
                                    </div>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }

                if ($this->view->canExport || $this->view->canEditArticles) {
                    ?>
                    <div class="content-menu__bloc  content-menu__bloc--purple  border-switch  portlet">
                        <div class="portlet-header">
                            <div class="text-left">
                                <h1><?php _e_html(180029, "Editer & exporter"); ?></h1>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="row">
                                <?php
                                if ($this->view->canEditArticles) {
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a qa_id="740083" class="no-decoration"
                                           href="#page.php/Import/Standard/Article/update:/fichier/">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(180030, "Modifier des articles à partir d'un fichier"); ?>
                                        </a>
                                    </div>
                                    <div style="padding: 8px 0;">
                                        <a qa_id="750022" class="no-decoration"
                                           href="#page.php/Article/Standard/EditionMasse/index">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(180031, "Modifier des articles dans un tableau"); ?> (beta)
                                        </a>
                                    </div>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration" href="#page.php/Import/Standard/ArtEmplacements/update:/fichier/">
                                            <span class="bold">></span> <?php _e_html(412021, "Modifier les emplacements d’articles à partir d'un fichier"); ?>
                                        </a>
                                    </div>
                                    <?php
                                }
                                ?>


                                <?php
                                global $ETAT_APPLICATION;
                                if ($ETAT_APPLICATION == 'DEV') :
                                    ?>

                                    <?php if ($this->view->canExport) { ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration" href="" style="color: hsl(30, 80%, 50%);">
                                                <span class="bold">
                                                    >
                                                </span>
                                            <?php _e_html(180032, "Exporter des articles"); ?>
                                        </a>
                                    </div>
                                <?php } ?>

                                <?php endif; ?>

                            </div>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
            <div class="row content-menu">
                <?php
                if ($this->view->canEditPrices) {
                    ?>
                    <div class="content-menu__bloc  content-menu__bloc--green  border-switch  portlet">
                        <div class="portlet-header">
                            <div class="text-left">
                                <h1><?php _e_html(180036, "Prix de vente"); ?></h1>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="row">
                                <div style="padding: 8px 0;">
                                    <a class="no-decoration"
                                       href="#page.php/Article/Standard/Tarifs/ListeProgrammations">
                                        <span class="bold">
                                            >
                                        </span>
                                        <qaid qa_id="172096">
                                            <?php _e_html(180037, "Modifier les prix de vente en masse"); ?>
                                        </qaid>
                                    </a>
                                </div>

                                <?php
                                if (\LMBCore\Licences\FonctionnaliteChecker::Factory('LMB')->check('tarifs.promotions')) {
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration"
                                           href="#page.php/Article/Standard/Tarifs/ListePromotions" qa_id="172168">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(180038, "Gérer les promotions"); ?>
                                        </a>
                                    </div>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                    <?php
                }

                if ($this->view->canConfigure) {
                    ?>
                    <div class="content-menu__bloc  content-menu__bloc--orange  border-switch  portlet">
                        <div class="portlet-header">
                            <div class="text-left">
                                <h1><?php _e_html(320064, "Configuration"); ?></h1>
                            </div>
                        </div>
                        <div class="portlet-body">
                            <div class="row">
                                <?php
                                // Si on est sur profil_collab on affiche "Gérer les catégories d'articles"
                                if (strstr($THIS_DIR, 'profil_collab')) {
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration" href="#/page.php/Catalogue/Standard/Catalogue/index"
                                           qa_id="620032">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(251110, "Gérer les catégories d'articles et leur contenu"); ?>
                                        </a>
                                    </div>
                                    <?php
                                    // Sinon (BO RC ou BO AK), on affiche "Gérer mon catalogue"
                                } else {
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration" href="#/page.php/Catalogue/Standard/Catalogue/index">
                                            <span class="bold">
                                                >
                                            </span>
                                            <qaid qa_id="540089">
                                                <?php _e_html(343025, "Gérer mes catégories et leur contenu"); ?>
                                            </qaid>
                                        </a>
                                    </div>
                                    <?php
                                }
                                global $ETAT_APPLICATION;
                                if ($ETAT_APPLICATION == 'DEV') :
                                    ?>
                                    <div style="padding: 8px 0; ">
                                        <a class="no-decoration" href="#page.php/Catalogue/Standard/Categories"
                                           style="color: hsl(30, 80%, 50%);">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(230035, "Gérer les catégories d'articles (maquette)"); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                <?php if ($user->checkPermissionByRef(PermissionRef::GERER_MARQUES)) : ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration" href="#page.php/Brands/Standard/Brands/index">
                                            <span class="bold">
                                                >
                                            </span>
                                            <qaid qa_id="540109">
                                                <?php _e_html(180035, "Gérer les marques"); ?>
                                            </qaid>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php
                                if (
                                    \LMBCore\Licences\FonctionnaliteChecker::Factory('LMB')->check('catalogue.caracs') &&
                                    \LMBCore\Licences\FonctionnaliteChecker::Factory('LMB')->check('catalogue.declinaisons') &&
                                    $user->checkPermissionByRef(PermissionRef::GERER_CARAC_DECLINAISON)
                                ) :
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration"
                                           href="#page.php/Caracs/Standard/Carac/recherche:article">
                                            <span class="bold">
                                                >
                                            </span>
                                            <qaid qa_id="540095">
                                                <?php _e_html(320065, "Gérer les caractéristiques et déclinaisons"); ?>
                                            </qaid>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php
                                if (
                                    \LMBCore\Licences\FonctionnaliteChecker::Factory('LMB')->check('catalogue.personnalisation') &&
                                    $user->checkPermissionByRef(PermissionRef::GERER_OPTION_PERSONALISATION_VENTE)
                                ) :
                                    ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration"
                                           href="#page.php/Caracs/Standard/Carac/recherche:option_personnalisation_vente"
                                           qa_id="173024">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(580719, "Gérer les options de personnalisation de vente"); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                <div style="padding: 8px 0;">
                                    <a class="no-decoration" href="#page.php/Catalogue/Standard/Catalogue/valorisation">
                                        <span class="bold">
                                            >
                                        </span>
                                        <?php _e_html(640145, "Gérer les valorisations"); ?>
                                    </a>
                                </div>
                                <?php
                                $user = user::getInstance();
                                if (lmbconfig::getInstance()->get("CAT_use_preparation")) : ?>
                                    <div style="padding: 8px 0;">
                                        <a class="no-decoration"
                                           href="#page.php/Article/Standard/ZonePreparation/index">
                                            <span class="bold">
                                                >
                                            </span>
                                            <?php _e_html(780016, "Gérer les zones de préparation en masse"); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>
        </div>
    </div>
</div>
<script>
	LMBNavigation.setBreadcrumb([
		__(180466, "CATALOGUE"),
		{
			label: __(180025, "Gérer mon catalogue"),
            url: '#/page.php/Catalogue/Standard/Catalogue/gererMonCatalogue'
		}
	]);
</script>
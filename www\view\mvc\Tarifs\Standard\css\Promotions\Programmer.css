#page-tarifs-new [ng\:cloak],
#page-tarifs-new [ng-cloak],
#page-tarifs-new [data-ng-cloak],
#page-tarifs-new [x-ng-cloak],
#page-tarifs-new .ng-cloak,
#page-tarifs-new .x-ng-cloak,
#page-tarifs-new[ng\:cloak],
#page-tarifs-new[ng-cloak],
#page-tarifs-new[data-ng-cloak],
#page-tarifs-new[x-ng-cloak],
#page-tarifs-new.ng-cloak,
#page-tarifs-new.x-ng-cloak {
  display: none !important;
}

#page-tarifs-new > .container {
	width: 1350px !important;
}

#page-tarifs-new .jumbotron a .fa {
	vertical-align: baseline;
}

#page-tarifs-new #programmer-content {
	margin-top: 8px !important;
}

#page-tarifs-new .head-section {
	display: flex;
}

#page-tarifs-new .left-bloc {
	width: 510px;
	padding-right: 12px;
}

#page-tarifs-new .right-bloc {
	flex-grow: 1;
	display: flex;
}

#page-tarifs-new .right-bloc > .portlet {
	width: 100%;
	display: flex;
	justify-content: center;
	flex-direction: column;
}

#page-tarifs-new .left-bloc table:nth-child(1) td:first-child {
	width: 160px;
}

#page-tarifs-new .left-bloc table:nth-child(2) {
	margin-top: 10px;
}

#page-tarifs-new .left-bloc table:nth-child(2) td:first-child {
	width: 140px;
}

#page-tarifs-new .left-bloc table:nth-child(2) .permanant-label {
	cursor: pointer;
}

#page-tarifs-new .left-bloc table:nth-child(2) .permanant-label label {
	margin: 0 5px 0 10px;
}

#page-tarifs-new .left-bloc .error-icon {
	color: hsl(16, 91%, 50%);
	position: absolute;
	right: 15px;
	top: 15px;
	font-size: 20px;
}

#page-tarifs-new .right-bloc table td:first-child {
	width: 180px;
}

#page-tarifs-new .right-bloc table:last-child {
	margin-top: 40px;
}

#page-tarifs-new .add-article-container,
#page-tarifs-new .small-add-article-container {
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	margin: 20px 0;
}

#page-tarifs-new .small-add-article-container > span {
	margin-right: 17px;
	font-size: 20px;
	font-weight: 100;
	white-space: nowrap;
}

#page-tarifs-new .small-add-article-container > .input-group {
	width: 100%;
	display: flex;
	justify-content: right;
}

#page-tarifs-new .large-80 {
	width: 80%;
}

#page-tarifs-new .large-80 button {
	margin-left: 15px;
}

#page-tarifs-new .selected-articles-list tr:not(:last-child) td {
	border-bottom-color: transparent;
}

#page-tarifs-new .selected-articles-list tfoot {
	background-color: #f2f2f2;
}

#page-tarifs-new .selected-articles-list th:nth-child(5),
#page-tarifs-new .selected-articles-list th:nth-child(6),
#page-tarifs-new .selected-articles-list th:nth-child(7),
#page-tarifs-new .selected-articles-list th:nth-child(8),
#page-tarifs-new .selected-articles-list td:nth-child(5),
#page-tarifs-new .selected-articles-list td:nth-child(6),
#page-tarifs-new .selected-articles-list td:nth-child(7),
#page-tarifs-new .selected-articles-list td:nth-child(8) {
	text-align: center;
}

#page-tarifs-new .selected-articles-list .first:not(:first-of-type) td {
	border-top: solid 1px #ccc;
}

#modal-sale-definition {
	width: 965px;
	min-height: 505px;
}

.modal-sale-definition-spinner-container {
	width: 100%;
	height: 100%;
	min-height: 525px; /* 20px de plus que modal-sale-definition pour gérer la fusion des marges du hr */
	display: flex;
	justify-content: center;
	align-items: center;
}

#modal-sale-definition > div:first-of-type {
	min-height: 160px;
}

#modal-sale-definition > div:last-of-type {
	min-height: 280px;
}

#modal-sale-definition label.inline-block,
#recherche-tarifs label.inline-block {
	display: inline-block;
	margin: 5px 0;
	cursor: pointer;
}

#modal-sale-definition label.inline-block label,
#recherche-tarifs label.inline-block label {
	margin-right: 10px;
}

#modal-sale-definition .reduction-view {
	display: flex;
	justify-content: space-between;
	padding-top: 15px;
	align-items: center;
}

#modal-sale-definition .reduction-view > div:nth-child(2) tr td:first-child {
	vertical-align: middle;
}

.custom-view .portlet {
	margin: 0;
}

.custom-view .portlet-tab .remove-grille{
    position: absolute;
    right: 6px;
    top: 4px;
    display: none;
}
.custom-view .portlet-tab .remove-tarif {
    position: absolute;
    left: 12px;
    display: none;
}

.custom-view .portlet-tab li a:hover .remove-grille,
.custom-view .portlet-tab li a:hover .remove-tarif {
    display: block;
}

.custom-view .list-tarifs>.table-cell:last-child {
    width:14vw;
    vertical-align: top;
}

.custom-view .portlet-tab > ul > li.disabled > a {
    opacity: 0.4;
}

#page-tarifs-new .number-art {
    font-weight:bold;
    font-size:25px;
}
#page-tarifs-new aside .list-criteres {
    text-align: left;
}
#page-tarifs-new aside .list-criteres li {
    padding: 5px 0 5px 0;
    position: relative;
}
#page-tarifs-new aside .list-criteres li a.right{
    position: absolute;
    right: 0;
}
#page-tarifs-new aside .list-criteres li.title {
    font-weight: bold;
    margin-top: 9px;
    border-top: 1px dashed #CECECE;
}

#page-tarifs-new .portlet.list-white-black {
    cursor: pointer;
}
#page-tarifs-new .portlet.list-white-black .row+.row {
    margin-top: 10px;
}
#page-tarifs-new .portlet.criteres-container {
    cursor: pointer;
}


#page-tarifs-new .portlet.criteres-container:not(.selected) * {
	pointer-events: none;
}

#page-tarifs-new .portlet.criteres-container.selected {
    border-color:green;
}

#page-tarifs-new .list-criteres li + li.no-criteres {
    display:none!important;
}

#page-tarifs-new tr.article-inactive td {
    color: lightgrey;
}

#page-tarifs-new .other-filters .block {
	height: 39px;
	line-height: 39px;
}

#add-article-validate {
	margin-top: 20px;
}

#page-tarifs-result .btn .fa {
	vertical-align: baseline;
}

#form-recherche-tarifs .center-form {
	display: flex;
	justify-content: center;
}

#form-recherche-tarifs .center-form > div {
	width: 40%;
}
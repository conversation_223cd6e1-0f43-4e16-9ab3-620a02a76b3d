'use strict';

app.controller('ManageController', ["$scope", '$timeout', '$filter', "OpportuniteService", "CompetitorsService", 'LMBModalService', function ($scope, $timeout, $filter, OpportuniteService, CompetitorsService, LMBModalService) {
    $scope.opportunite = {
        type: '',
        budget: '',
        delay: '',
        origin: '',
        title: '',
        description: '',
        tauxSuccess: '',
        competitors: []
    };

    $scope.competitors = [];
    $scope.edition = 0;
    $scope.position = 0;
    $scope.modeDev = false;
    $scope.competitorsList = {
        all: [],
        encourse: [],
        winner: [],
        loosers: []
    };

    $scope.$on('editableContent-edit', function(e, competitor){
        $scope.sortCompetitors();
        $scope.updateCompetitor(competitor);
    });

    $scope.$on('editableContent-delete', function(e, competitor){
        $scope.removeCompetitor(competitor.position);
    });

    $scope.init = function(id, page) {
        $scope.page = page;

        if(id) {
            OpportuniteService.getDatas(id).then(function(datas) {

                $scope.opportunite = datas.opportunite;
                $scope.competitors = $scope.opportunite.competitors;

                $scope.pieces_jointes = datas.pieces_jointes || [];
                $scope.images = datas.images || [];

                if($scope.images.list.length > 0) {
                    $scope.selectMainImage($scope.images.list[0]);
                }

                $scope.position = $scope.opportunite.competitors.length + 1;

                $scope.modeDev = datas.modeDev;

                $scope.client_categ = datas.client_categ;
                $scope.client_statut = datas.client_statut;
                $scope.code_contact= datas.code_contact;

                var caracsList = angular.extend([], $scope.opportunite.caracs);

                $scope.opportunite.caracs = caracsList.filter(function(element){
                    return true;
                });

                $scope.sortCompetitors();
            });
        }
        else {
            $scope.position = 1;
        }
    };

    $scope.selectMainImage = function(image){
        $scope.image_main = image;
    };

    $scope.updateCompetitor = function(competitor){
        CompetitorsService.save(competitor).then(function(response){
            competitor.id_competitor = response.data.competitorId;
            CompetitorsService.saveOC(competitor, $scope.opportunite.id_opportunite);
        });
    };

    $scope.showModal = function(){
        LMBModalService.showModal({
            LMBModalConfig: {
                titre: 'Ajouter un concurrent'
            },
            angularModal: {
                config: {
                    templateUrl: "page.php/Opportunite/Standard/Competitor/displayNewCompetitorFromOpportunite",
                    controller: "CompetitorModalController",
                    inputs: {
                        parentScope: $scope
                    }
                },
                then: function (modal) {},
                onClose: function (result) {

                }
            }
        });
    };

    $scope.removeCompetitor = function(position){
        var nbCompetitors = $scope.competitors.length;
        var i = 0;
        var competitor = false;

        while(i < nbCompetitors && !competitor){
            if($scope.competitors[i].position == position){
                competitor = $scope.competitors[i];
                $scope.competitors.splice(i, 1);
                OpportuniteService.removeCompetitor(competitor.id_competitor, $scope.opportunite.id_opportunite);
            }
            i++;
        }
    };

    $scope.addCompetitor = function(status){
        var newCompetitor = {
            id_competitor: '',
            lib: '',
            note: '',
            position: $scope.position,
            actif: 1,
            etat: status,
            editionMode: true,
        };

        $scope.competitors.push(newCompetitor);
        $scope.competitorsList[status].push(newCompetitor);
    };

    $scope.sortCompetitors = function(){
        $scope.competitorsList.encourse = $filter('filter')($scope.competitors, {etat: 'encourse'});
        $scope.competitorsList.winner = $filter('filter')($scope.competitors, {etat: 'winner'});
        $scope.competitorsList.loosers = $filter('filter')($scope.competitors, {etat: 'loser'});
    };

}]);

app.controller('CompetitorModalController', ['$scope', 'CompetitorsService', 'OpportuniteService', 'parentScope', function($scope, CompetitorsService, OpportuniteService, parentScope){
    $scope.competitor = {
        id: null,
        lib: '',
        note: '',
        actif: 1,
        ordre: parentScope.position
    };

    $scope.create = function(){
        var competitor = {
            id: $scope.competitor.id,
            lib: $scope.competitor.lib,
            note: $scope.competitor.note,
            actif: $scope.competitor.actif
        };

        CompetitorsService.create(competitor).then(function(response){
            $scope.competitor.id = response.data.competitor.id_competitor;

            OpportuniteService.addCompetitor($scope.competitor, parentScope.opportunite.id_opportunite).then(function(response){
                parentScope.position++;
                parentScope.competitors.push(response.data.competitor);
                $j('#formCreateCompetitor').getModalInstance().close();
            });
        });
    };
}]);

app.controller('CompetitorsEditionController', ['$scope', '$filter', 'CompetitorsService', function($scope, $filter, CompetitorsService){

}]);
<script src="<?= $DIR; ?>ressources/javascript/angular-ui/angular-ui-sortable.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>

<?php
foreach (glob($DIR . "view/mvc/Caracs/Standard/javascript/angular/*.js") as $filejs){
    ?>
    <script type="text/javascript" src="<?php echo langage::parse($filejs) ?>"></script>
    <?php
}

foreach (glob($DIR."view/mvc/Caracs/Standard/javascript/angular/controllers/*.js") as $filejs): ?>
  <script type="text/javascript" src="<?= langage::parse($filejs)?>?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<?php endforeach; ?>
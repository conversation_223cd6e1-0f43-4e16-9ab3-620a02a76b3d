<form ACTION="import_mcto.php" method="post">

	<input name="secu_key" type="hidden" value="753159" />

	<label>officeid</label>
	<input name="officeid" type="text" value="1000" />
	<br />
	<label>mcto</label>
	<textarea name="mcto"><?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE message SYSTEM "mcto-1-2.dtd"> 
<message>
	<codage>
		<format value="MCTO"/>
		<version value="1.2"/>
	</codage>
	<entete>
		<origine>
			<emetteur>
				<libelle>to amadeus dev</libelle>
			</emetteur>
			<date_emission>
				<date_hms>
					<date jour="04" mois="11" annee="2005" />
					<hms heure="14" minute="39" seconde="04"/>
				</date_hms>
			</date_emission>
			<canal_distribution value="GDS"/>
		</origine>
		<recepteur code="amaftp"/>
		<decimales>2</decimales>
		<devise value="EUR"/>
	</entete>
	<dossier>
		<code_action_dossier value="C"/>
		<type_action_dossier value="30"/>
		<statut_dossier value="OK"/>
		<references_dossier>
			<interne_to>
				<libelle>3665373</libelle>
			</interne_to>
			<nom_dossier>
				<libelle>DURAND</libelle>
			</nom_dossier>
			<origine_interne_to>
				<libelle>3665373</libelle>
			</origine_interne_to>
			<facture_to>
				<libelle>7139812</libelle>
			</facture_to>
			<vendeur_agence>
				<libelle>ESTEREL</libelle>
			</vendeur_agence>
			<vendeur_to>
				<libelle></libelle>
			</vendeur_to>
		</references_dossier>
		<date_creation>
			<date jour="01" mois="10" annee="2005" />
		</date_creation>
		<date_modification>
			<date jour="01" mois="10" annee="2005" />
		</date_modification>
		<observation>
			<libelle><![CDATA[]]></libelle>
		</observation>
		<produit_principal>
			<nom_produit>
				<libelle lg="FR"><![CDATA[VDKR]]></libelle>
			</nom_produit>
			<categorie value=""/>
			<references_produit>
				<interne_to>
					<libelle>VDKR</libelle>
				</interne_to>
			</references_produit>
			<categorie_prix value="BROCHURE"/>
			<formule_produit value="volsec"/>
		</produit_principal>
		<montants_dossier>
			<montant_brut>102000</montant_brut>
			<montant_net>95440</montant_net>
			<montant_tva>0</montant_tva>
			<montant_commission>6560</montant_commission>
		</montants_dossier>
		<nombre_passagers>
			<adultes>2</adultes>
			<enfants>0</enfants>
			<bebes>0</bebes>
		</nombre_passagers>
		<voyage>
			<periode_voyage>
				<periode>
					<date_debut jour="02" mois="12" annee="2005" />
					<date_fin jour="09" mois="12" annee="2005" />
				</periode>
			</periode_voyage>
			<depart_voyage>
				<ville>
					<iata value="NTE"/>
				</ville>
			</depart_voyage>
			<retour_voyage>
				<ville>
					<iata value="NTE"/>
				</ville>

			</retour_voyage>
			<pays_sejour>
 				<libelle lg="FR">SENEGAL</libelle>
			</pays_sejour>
			<lieu_sejour>
				<ville><iata value="DKR"/></ville>
				
			</lieu_sejour>

		</voyage>
		<passagers>
			<passager numero="1">
	<titre_personne value="MR"/>
	<nom>DURAND</nom>
	<prenom>HERVE</prenom>
	
</passager>
<passager numero="2">
	<titre_personne value="MME"/>
	<nom>DURAND</nom>
	<prenom>ANITA</prenom>
	
</passager>
		</passagers>
		<transports><transport sequence="1">
	<transporteur value="BIE"/>
	<no_transport>657N</no_transport>
	<type_transport value="VC"/>
	<statut_prestation value="OK"/>
	<periode_prestation>
		<periode_etendue>
			<date_debut jour="02" mois="12" annee="2005" />
			<heure_minute_debut heure="5" minute="00"/>
			<date_fin jour="02" mois="12" annee="2005" />
			<heure_minute_fin heure="9" minute="30"/>
		</periode_etendue>
	</periode_prestation>
	<nombre_passagers>
		<adultes>2</adultes>
		<enfants>0</enfants>
		<bebes>0</bebes>
	</nombre_passagers>
	<depart_voyage>
		<ville>
			<iata value="NTE"/>
		</ville>
	</depart_voyage>
	<arrivee_voyage>
		<ville>
			<iata value="DKR"/>
		</ville>
	</arrivee_voyage>
	<observation><libelle lg="FR">Horaires non confirmés</libelle></observation>
</transport>
<transport sequence="3">
	<transporteur value="BIE"/>
	<no_transport>658N</no_transport>
	<type_transport value="VC"/>
	<statut_prestation value="OK"/>
	<periode_prestation>
		<periode_etendue>
			<date_debut jour="09" mois="12" annee="2005" />
			<heure_minute_debut heure="11" minute="40"/>
			<date_fin jour="09" mois="12" annee="2005" />
			<heure_minute_fin heure="17" minute="50"/>
		</periode_etendue>
	</periode_prestation>
	<nombre_passagers>
		<adultes>2</adultes>
		<enfants>0</enfants>
		<bebes>0</bebes>
	</nombre_passagers>
	<depart_voyage>
		<ville>
			<iata value="DKR"/>
		</ville>
	</depart_voyage>
	<arrivee_voyage>
		<ville>
			<iata value="NTE"/>
		</ville>
	</arrivee_voyage>
	<observation><libelle lg="FR">Horaires non confirmés</libelle></observation>
</transport></transports>	
		<terrestres>
<terrestre sequence="2">
	<type_terrestre value="H"/>
	<periode_prestation>
		<periode_etendue>
			<date_debut jour="02" mois="12" annee="2005" />
			<date_fin jour="09" mois="12" annee="2005" />
		</periode_etendue>
	</periode_prestation>
	<nombre_passagers>
		<adultes>2</adultes>
		<enfants>0</enfants>
		<bebes>0</bebes>
	</nombre_passagers>
	<nature_prestation value="V"/>
	<libelle_prestation>
		<libelle lg="FR">VDKR</libelle>
	</libelle_prestation>
	<statut_prestation value="OK"/>
	<categorie value=""/>
	<pension code="SA"><libelle lg="FR"></libelle></pension>
	<lieu_sejour>
		<libelle lg="FR">SENEGAL</libelle>
	</lieu_sejour>
	
</terrestre></terrestres>
		<details_tarifs><detail_tarif numero="1" type="F">
	<libelle_tarif>
		<libelle><![CDATA[TRANSPORT ALLER]]></libelle>
	</libelle_tarif>
	<quantite>2</quantite>
	<montant_brut>20000</montant_brut>
</detail_tarif>
<detail_tarif numero="2" type="F">
	<libelle_tarif>
		<libelle><![CDATA[TRANSPORT RETOUR]]></libelle>
	</libelle_tarif>
	<quantite>2</quantite>
	<montant_brut>20000</montant_brut>
</detail_tarif>
<detail_tarif numero="3" type="F">
	<libelle_tarif>
		<libelle><![CDATA[SURCHARGE CARBURANT]]></libelle>
	</libelle_tarif>
	<quantite>2</quantite>
	<montant_brut>1000</montant_brut>
</detail_tarif>
<detail_tarif numero="4" type="F">
	<libelle_tarif>
		<libelle><![CDATA[TAXE + FRAIS DOSSIER]]></libelle>
	</libelle_tarif>
	<quantite>2</quantite>
	<montant_brut>7000</montant_brut>
</detail_tarif>
<detail_tarif numero="5" type="F">
	<libelle_tarif>
		<libelle><![CDATA[TAXES AEROPORT RDIA]]></libelle>
	</libelle_tarif>
	<quantite>2</quantite>
	<montant_brut>3000</montant_brut>
</detail_tarif></details_tarifs>
		<details_clients><detail_client numero="1">
	<client_numeroto>00000000</client_numeroto>
	<client_titre></client_titre>
	<client_nom></client_nom>
	<client_prenom></client_prenom>
	<client_adr1><![CDATA[]]></client_adr1>
	<client_adr2><![CDATA[]]></client_adr2>
	<client_adr3><![CDATA[]]></client_adr3>
	<client_adr4><![CDATA[]]></client_adr4>
	<client_cp>00000</client_cp>
	<client_ville></client_ville>
	<client_pays></client_pays>
	<client_tel1></client_tel1>
	<client_tel2></client_tel2>
	<client_fax></client_fax>
	<client_email></client_email>
</detail_client></details_clients>
		<details_assurances><detail_assurance numero="1" type="S">
	<police>302752</police>
	<libelle lg="FR">ELVIA</libelle>
</detail_assurance></details_assurances>
		<details_informations><detail_information numero="1" type="A">
	<libelle lg="FR">Formalités Police - Santé</libelle>
	<detail lg="FR"><![CDATA[Pour ressortissants francais : PASSEPORT OU CARTE D'IDENTITE EN COURS DE
VALIDITE OBLIGATOIRE    ************  A T T E N T I O N **************
** POUR LES VOLS EN LIBERTE, PASSEPORT EN COURS DE VALIDITE OBLIGATOIRE*
Notre représentant à DAKAR : SENEGAL-DECOUVERTE 8, rue GALANDAU DIOUF
Angle rue Dr Theze à DAKAR          TEL : 00 221 823 03 40]]></detail>
</detail_information></details_informations>
		<details_annulations><detail_annulation numero="1">
	<libelle lg="FR">+ de 30 jours avant le départ</libelle>
	<montant>3000</montant>
	<pourcentage></pourcentage>
	<jours1></jours1>
	<jours2>31</jours2>			
</detail_annulation>
<detail_annulation numero="2">
	<libelle lg="FR">30 avant le départ</libelle>
	<montant></montant>
	<pourcentage>10000</pourcentage>
	<jours1>30</jours1>
	<jours2>0</jours2>			
</detail_annulation>
</details_annulations>
	</dossier>
</message>
</textarea>
		<br />
	<label>tourop</label>
	<input name="tourop" type="text" value="5001" />
		<br />
	<label>réseau</label>
	<input name="réseau" type="text" value="500" />
		<br />
	<label>Filename</label>
	<input name="Filename" type="text" value="TestFRAM01.xml" />

	<button>Envoyer</button>
	
</form>
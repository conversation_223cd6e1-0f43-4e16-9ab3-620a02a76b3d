#list-type-events {
    border: 1px solid #dadada;
    border-radius: 8px;
    margin-bottom: 20px;
    font-weight: lighter;
    table-layout: fixed;
}
#list-type-events td {
    text-align: center;
    padding: 16px 10px;
    font-size: 1.1em;
}
#list-type-events label {
    cursor:pointer;
}
#list-type-events label span {
    display:block;
    font-weight: lighter;
}
#list-type-events .event-icon {
    font-size: 2.2em;
}
#list-type-events input[type="radio"]:checked ~ span {
    color: #5898FF !important;
}
.block-info {
    position: relative;
    padding-right: 10px;
}
.block-icon {
    position: absolute;
    left: 5px;
    top: 3px;
    color: #717171;
    font-size: 17px;
}
.block-text {
    padding: 4px 4px 4px 25px;
    font-weight: bold;
    background-color: #f5f5f5;
    border: 1px solid #e1e1e1;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
'use strict';

(function () {
    angular
        .module('AkEcranCuisineManager')
        .controller('IndexController', IndexController);

    IndexController.$inject = ['$scope', 'AkEcranCuisineApi', 'LMBModalService'];

    function IndexController($scope, AkEcranCuisineApi, LMBModalService) {
        const DEFAULT_LICENCE = 'akecran_cuisine_standard';

        $scope.ak_ecran_cuisines = [];
        $scope.loading = true;

        $scope.init = function () {
            AkEcranCuisineApi.getList().then((res) => {
                if (res.data) {
                    $scope.ak_ecran_cuisines = res.data;
                    $scope.loading = false;
                } else {
                    LMBToast.error({
                        title: __(580957,"Une erreur est survenue"),
                        message: __(710412,"La liste des écrans cuisines n'a pas pu être chargée")
                    });
                }
            });
        };

        let addNewShowCreationModal = function () {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(710411,"Création d'un Ecran de Cuisine"),
                    drag: true,
                    resizable: false,
                    overlay: true,
                    style: {width: "750px", height: "250px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:AkEcranCuisine/phtml/popup/ecran_cuisine.phtml",
                        controller: "popupAkEcranCuisineController",
                        inputs: {
                            ecranCuisine: null
                        }
                    },
                    then: function () {
                    },
                    onClose: function (result) {
                        $scope.ak_ecran_cuisines.push(result);
                    }
                }
            });
        };

        let addNewShowAlertMoreLicenceModal = function () {
            let lmb_modal = new LMBModal("popup_alerte_add_more_licence", {
                titre: __(121325, "Souscription au service payant"),
                overlay: true,
                style: {width: "645px", height: "260px"},
                callback: function (confirmed) {
                    if (confirmed === true) {
                        addNewShowCreationModal();
                    }
                }
            });
            lmb_modal.request(
                "page.php/Licence/Standard/LicenceRight/popupAlerteAddMore"
            );
            lmb_modal.open();
        }

        let addNewShowImpossibleModal = function () {
            let lmb_modal = new LMBModal("popup_bloquer_add_more_licence", {
                titre: __(121325, "Souscription au service payant"),
                overlay: true,
                style: {width: "645px", height: "260px"},
                onClose: function () {
                }
            });
            lmb_modal.request(
                "page.php/Licence/Standard/LicenceRight/popupBloquerAddMore",
                {'ref_licence': DEFAULT_LICENCE}
            );
            lmb_modal.open();
        }

        $scope.addNew = function () {
            // LMBTools.post({
            //     url: 'page.php/Licence/Standard/LicenceRight/getLicenceRight',
            //     dataType: "json",
            //     data: {
            //         'ref_licence': DEFAULT_LICENCE
            //     },
            //     success: function (response) {
            //         let licence_right = response.licence_right;
            //         if (licence_right.qte_used < licence_right.qte_dispo) {
            //             addNewShowCreationModal();
            //         } else if (licence_right.can_add_more && licence_right.qte_used < licence_right.qte_max) {
            //             addNewShowAlertMoreLicenceModal();
            //         } else {
            //             addNewShowImpossibleModal();
            //         }
            //     }
            // });
            addNewShowCreationModal();
        };

        $scope.desync = function(id_ak_ecran_cuisine, index, statut_sync) {
            if (statut_sync != "Synchronisé") {
                return;
            }

            LMBTools.confirm({
                content: __(521851,"Voulez-vous vraiment désynchroniser le ecran_cuisine ?"),
                onValid: function(confirm) {
                    if(confirm) {
                        let data = {"id_ecran_cuisine" : id_ak_ecran_cuisine};
                        Akecran_cuisineApi.desync(data).then(function(response){
                            if(response.ecran_cuisine){
                                $scope.ak_ecran_cuisines[index] = response.ecran_cuisine;
                                LMBToast.success({
                                    title: __(521852,"ecran_cuisine désynchronisé")
                                });
                            }else if(response.error){
                                LMBToast.error({
                                    title: __(580957,"Une erreur est survenue"),
                                    message: __(521853,"La désynchronisation du ecran_cuisine a échouée.")
                                });
                            }
                        });
                    }
                }
            });
        }

        $scope.delete = function(id_ak_ecran_cuisine, index){
            LMBTools.confirm({
                content: __(710408,"Voulez-vous vraiment supprimer cet écran cuisine ?"),
                onValid: function(confirm) {
                    if(confirm) {
                        let data = {"id_ak_cuisine" : id_ak_ecran_cuisine};
                        AkEcranCuisineApi.delete(data).then(function(response){
                         
                            if(response.success){
                                $scope.ak_ecran_cuisines.splice(index, 1);
                                LMBToast.success({
                                    title: __(710409,"Écran cuisine supprimé")
                                });
                            }else if(response.error){
                                LMBToast.error({
                                    title: __(580957,"Une erreur est survenue"),
                                    message: __(710410,"La suppression de l'écran cuisine a échoué.")
                                });
                            }
                        });
                    }
                }
            });
        }

        $scope.updateEcran_cuisine = function(ecran_cuisine) {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(710413,"Modification de l'écran cuisine"),
                    drag: true,
                    resizable: false,
                    overlay: true,
                    style: {width: "750px", height: "250px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:AkEcranCuisine/phtml/popup/ecran_cuisine.phtml",
                        controller: "popupAkEcranCuisineController",
                        inputs: {
                            ecranCuisine: angular.copy(ecran_cuisine)
                        }
                    },
                    then: function () {
                    },
                    onClose: function (result) {
                        angular.forEach(result, (value, key) => {
                            ecran_cuisine[key] = value;
                        });
                    }
                }
            });
        }
    }

})();
<?php

require_once($DIR.'/ressources/ods/ods.php');

class requeteur {

    // Constante de types de requeteur
    const REQUETEUR_TYPE = [
        "ventes" => 1,
        "achats" => 2,
        "mini" => 3 // bo rc
    ];

    // Constantes de decoupage
    const DECOUPAGE_AUCUN = 0;
    const DECOUPAGE_JOUR = 5;
    const DECOUPAGE_MOIS = 1;
    const DECOUPAGE_TRIMESTRE = 2;
    const DECOUPAGE_QUADRIMESTRE = 4;
    const DECOUPAGE_ANNEE = 3;

    // Constantes des modèles
    const MODELE_TODAY = 0;
    const MODELE_WEEK = 1;
    const MODELE_MONTH = 2;
    const MODELE_YEAR = 3;
    const MODELE_3_YEAR = 4;

    // Constantes de separation
    const SEPARATION_ENSEIGNE = 1;
    const SEPARATION_MAGASIN = 2;
    const SEPARATION_CLIENT_CATEG = 3;
    const SEPARATION_CLIENT = 4;
    const SEPARATION_MARQUE = 12;
    const SEPARATION_ARTICLE_CATEG = 5;
    const SEPARATION_ARTICLE = 6;
    const SEPARATION_CATEG_COMMERCIALE = 7;
    const SEPARATION_EQUIPE_COMMERCIALE = 8;
    const SEPARATION_COMMERCIAL = 9;
    const SEPARATION_GEO = 10;
    const SEPARATION_PAYS = 11;
    const SEPARATION_FOURNISSEUR_CATEG = 13;
    const SEPARATION_FOURNISSEUR = 14;    
    const SEPARATION_ACTIVITE = 15;    
    const SEPARATION_ARTICLE_PARENT = 16;
    const SEPARATION_FOURNISSEUR_VENTE = 17;
    
    // Constantes de champs de resultats
    const QTE_COMMANDEE = 'qte_commandee';
    const QTE_FACTUREE = 'qte_facturee';
    const QTE_RECUE = 'qte_recue';
    const QTE_LIVREE = 'qte_livree';
    const QTE_ACHAT_CDC = 'qte_achat_cdc';
    const QTE_ACHAT_FAC = 'qte_achat_fac';
    const CA = 'ca_ht';
    const ACHATS = 'achats_ht';
    const RECUS = 'recus_ht';
    const MARGE = 'marge';
    const MARGE_BRUTE = 'marge_brute';
    const MARGE_POURCENT = 'marge_pourcent';
    const MARGE_BRUTE_POURCENT = 'marge_brute_pourcent';    
    const COUT_ACHAT = 'cout_achat';
    const COUT_REVIENT = 'cout_revient';
    const VALEUR_STOCK = 'valeur_stock';
    const CA_POTENTIEL = 'ca_potentiel';
    const CA_PREVISIONNEL = 'ca_previsionnel';
    const CA_CDC = 'ca_cdc';
    const CA_TTC = 'ca_ttc';
    const CA_MOYEN = 'ca_moyen';
    const QTE_VENTES = 'qte_ventes';
    const VISITES = 'visites';
    const TAUX_TRANSFORMATION = 'taux_transformation';
    const CA_MOYEN_VISITE = 'ca_moyen_visite';
    const CA_BLC_NON_FACTURE = 'ca_blc_non_facture';

    public $separations_libs = array(
        self::SEPARATION_ENSEIGNE => "Enseigne",
        self::SEPARATION_MAGASIN => "Magasin",
        self::SEPARATION_CLIENT_CATEG => "Categ Client",
        self::SEPARATION_CLIENT => "Client",
        self::SEPARATION_FOURNISSEUR_CATEG => "Categ Fournisseur",
        self::SEPARATION_FOURNISSEUR => "Fournisseur",
        self::SEPARATION_MARQUE => "Marque",
        self::SEPARATION_ARTICLE_CATEG => "Categ Article",
        self::SEPARATION_ARTICLE => "Article",
        self::SEPARATION_CATEG_COMMERCIALE => "Categ Commercial",
        self::SEPARATION_EQUIPE_COMMERCIALE => "Equipe",
        self::SEPARATION_COMMERCIAL => "Commercial",
        self::SEPARATION_GEO => "Zone",        
        self::SEPARATION_PAYS => "Pays",
        self::SEPARATION_ACTIVITE => "Activité",
        self::SEPARATION_ARTICLE_PARENT => "Article parent",
        self::SEPARATION_FOURNISSEUR_VENTE => "Fournisseur",
    );
    
    public $champs_libs = array(
        self::QTE_COMMANDEE => "Qte commandée",
        self::QTE_FACTUREE => "Qte facturée (ventes)",
        self::QTE_RECUE => "Qte reçue",
        self::QTE_LIVREE => "Qte livrée",
        self::QTE_ACHAT_CDC => "Qte commandée",
        self::QTE_ACHAT_FAC => "Qte facturée (achats)",
        self::CA => "Ca HT",
        self::RECUS => "Reçus HT",
        self::ACHATS => "Achats HT",
        self::MARGE => "Marge nette",
        self::MARGE_BRUTE => "Marge brute",
        self::MARGE_POURCENT => "Marge nette %",
        self::MARGE_BRUTE_POURCENT => "Marge brute %",
        self::COUT_ACHAT => "Cout d'achat",
        self::COUT_REVIENT => "Cout de revient",
        self::VALEUR_STOCK => "Valeur en stock",
        self::CA_POTENTIEL => "CA potentiel",
        self::CA_PREVISIONNEL => "CA prévisionnel",
        self::CA_CDC => "Montant HT CDC",
        self::CA_TTC => "Ca TTC",
        self::CA_MOYEN => "CA HT moyen",
        self::QTE_VENTES => "Quantité ventes",
        self::VISITES => "Visites",
        self::TAUX_TRANSFORMATION => "Taux transformation",
        self::CA_MOYEN_VISITE => "CA moyen par visite",
        self::CA_BLC_NON_FACTURE => "Montant HT BLC non facturés",
    );
    
    public $ods = false;
    public $lignes_ss_total = false;
    
    protected $du = "";
    protected $au = "";

    protected $modele = -1;
    
    protected $separations = array();
    protected $separations_datas = array();
    protected $champs = array();
    protected $periodes = array();
    protected $radioRequesters = array();
    
    protected $col_supp = 0; // Colonnes supplementaires 
    
    protected $decoupage = self::DECOUPAGE_ANNEE;
    
    protected $previsions = false;
    protected $fd = true;
    protected static function _getBdd(){
        return PDO_etendu::getInstanceReadOnly();
    }

    public function __construct($du, $au, $modele, array $separations, array $champs, $decoupage = self::DECOUPAGE_ANNEE, $lignes_ss_total = false, array $separations_datas = array(), array $radioRequesters = array()) {
        $this->separations_libs = array(
            self::SEPARATION_ENSEIGNE => langage::write("enseigne"),
            self::SEPARATION_MAGASIN => langage::write("magasin"),
            self::SEPARATION_CLIENT_CATEG => langage::write("categ_client"),
            self::SEPARATION_CLIENT => langage::write("client"),
            self::SEPARATION_FOURNISSEUR_CATEG => langage::write("categ_fournisseur"),
            self::SEPARATION_FOURNISSEUR => langage::write("fournisseur"),
            self::SEPARATION_MARQUE => langage::write("gs__marque"),
            self::SEPARATION_ARTICLE_CATEG => langage::write("categ_article"),
            self::SEPARATION_ARTICLE => langage::write("article"),
            self::SEPARATION_CATEG_COMMERCIALE => langage::write("categ_commercial"),
            self::SEPARATION_EQUIPE_COMMERCIALE => langage::write("equipe"),
            self::SEPARATION_COMMERCIAL => langage::write("commercial"),
            self::SEPARATION_GEO => langage::write("zone"),        
            self::SEPARATION_PAYS => langage::write("pays"),
            self::SEPARATION_ACTIVITE => langage::write("activite"),
            self::SEPARATION_ARTICLE_PARENT => langage::write("article_parent"),
            self::SEPARATION_FOURNISSEUR_VENTE => langage::write("fournisseur"),
        );

        $this->champs_libs = array(
            self::QTE_COMMANDEE => __(150094,"Quantité commandée"),
            self::QTE_FACTUREE => __(410337,"Qte facturée (ventes)"),
            self::QTE_RECUE => langage::write("qte_recue"),
            self::QTE_LIVREE => langage::write("qte_livree"),
            self::QTE_ACHAT_CDC => langage::write("qte_commandee"),
            self::QTE_ACHAT_FAC => __(410338,"Qte facturée (achats)"),
            self::CA => langage::write("CA_HT"),
            self::RECUS => "Reçus HT",
            self::ACHATS => langage::write("achats_HT"),
            self::MARGE => langage::write("marge_nette"),
            self::MARGE_BRUTE => langage::write("marge_brute"),
            self::MARGE_POURCENT => langage::write("marge_nette")." %",
            self::MARGE_BRUTE_POURCENT => langage::write("marge_brute")." %",
            self::COUT_ACHAT => langage::write("cout_d_achat"),
            self::COUT_REVIENT => langage::write("cout_de_revient"),
            self::VALEUR_STOCK => "Valeur en stock",
            self::CA_POTENTIEL => langage::write("CA_potentiel"),
            self::CA_PREVISIONNEL => langage::write("CA_previsionnel"),
            self::CA_CDC => langage::write("montant_ht_cdc"),
            self::CA_TTC => "Ca TTC",
            self::CA_MOYEN => __(550047,"Panier moyen HT"),
            self::QTE_VENTES => "Qté ventes",
            self::VISITES => __(550030,"Visites"),
            self::TAUX_TRANSFORMATION => __(550034,"Taux transformation"),
            self::CA_MOYEN_VISITE => __(550035,"Chiffre d'affaires HT moyen par visite"),
            self::CA_BLC_NON_FACTURE => __(280054,"Montant HT des bons de livraison livrés et non facturés")
        );
      
        $this->du = $du;
        $this->au = $au;
        $this->modele = $modele;
        foreach($separations as $toadd){
            if (!in_array($toadd, $this->separations))
                    $this->separations[] = $toadd;
        }
        foreach($separations_datas as $separation=>$data){
            if (in_array($separation, $this->separations))
                    $this->separations_datas[$separation] = $data;
        }

        $this->decoupage = $decoupage;
        $this->lignes_ss_total = $lignes_ss_total;
        $this->champs = $champs;
        $this->radioRequesters = $radioRequesters;
        $this->generer_periodes();
    }

    protected function generer_periodes(){
        
        $bdd = self::_getBdd();

        if(empty($this->du)){
            $resultset = $bdd->query("SELECT date_creation_doc FROM documents WHERE date_creation_doc > '0000-00-00' ORDER BY date_creation_doc ASC LIMIT 1;");
            if(!$resultset) return false;
            $result = $resultset->fetchObject();
            if(!$result) return false;
            $this->du = substr($result->date_creation_doc,0,10);
        }
        if(empty($this->au)){
            $resultset = $bdd->query("SELECT date_creation_doc FROM documents ORDER BY date_creation_doc DESC LIMIT 1;");
            if(!$resultset) return false;
            $result = $resultset->fetchObject();
            if(!$result) return false;
            $this->au = substr($result->date_creation_doc,0,10)." 23:59:59";
        }
        $this->du = preg_replace("/^(\d{2})-(\d{2})-(\d{4})$/", "$3-$2-$1", $this->du);
        $this->au = preg_replace("/^(\d{2})-(\d{2})-(\d{4})$/", "$3-$2-$1", $this->au);

        $fin = new stdClass();
        $fin->annee = date("Y",  strtotime($this->au));
        $fin->mois = date("n",  strtotime($this->au));
        $fin->jour = date("j", strtotime($this->au));
        $annee = date("Y",  strtotime($this->du));
        $mois = date("n",  strtotime($this->du));
        $jour = date("j",  strtotime($this->du));

        while($annee <= $fin->annee){
            if(in_array($this->decoupage, array(self::DECOUPAGE_JOUR,self::DECOUPAGE_MOIS)) ){
                while( ($annee == $fin->annee && $mois <= $fin->mois) || ($annee<$fin->annee && $mois<=12) ){
                    $format_mois = $annee."-".str_pad($mois, 2, '0', STR_PAD_LEFT);
                    if($this->decoupage == self::DECOUPAGE_JOUR){
                        $nb_jours = ($annee == $fin->annee && $mois == $fin->mois) ? $fin->jour : date("t",strtotime($format_mois."-01"));
                        while($jour <= $nb_jours){
                            $this->periodes[] = $format_mois."-".str_pad($jour, 2, '0', STR_PAD_LEFT);
                            $jour++;
                        }
                        $jour = 1;
                    }
                    $this->periodes[] = $format_mois;
                    $mois++;
                }
                $mois = 1;
            }
            if($this->decoupage == self::DECOUPAGE_ANNEE){
                $this->periodes[] = $annee;
            }
            $annee++;
        }

        $this->periodes[] = "total";
        return true;
    }
    
    protected function makeWhereIn($data_name,array $datas){
        
        $bdd = self::_getBdd();
        $hasNull = false;
        $result = "";
        foreach ($datas as $data){
            $data === "null" ? $hasNull = true : $result .= (!empty($result) ? "," : "").$bdd->quote($data) ; 
        }
        if (empty($result) && !$hasNull) return "";
        $clause = (!empty($result) ? " $data_name IN ($result) " : "") . ($hasNull ? (!empty($result) ? " OR " : "") . " $data_name IS NULL " : "");
        return $clause ? " ($clause) " : "";
    }
    
    public function getDatas(){
        
        if ( empty($this->separations) || empty($this->periodes) ) return false;
        
        $bdd = self::_getBdd();
        $offsetServer = \lmbDateInter::getServerTZOffset(true);
        $offsetEntreprise = \lmbDateInter::getEntrepriseTZOffset(true);
        
        $query_select = $query_group = $path = $query_order = $query_where = "";
        $query_where .= "d.id_type_doc IN (1,2,3,4,5,6,7,8,15) AND ( ( (`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) ) OR ( (`dle`.`visible` = 0) AND a2.lot=2) ) ";
        $query_where .= !empty($this->du) ? (!empty($query_where) ? " AND " : "")." CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise')>=".$bdd->quote($this->du)." " : "";
        $query_where .= !empty($this->au) ? (!empty($query_where) ? " AND " : "")." CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise')<=".$bdd->quote($this->au)." " : "";

        $separations = array(
            self::SEPARATION_ENSEIGNE => array(
                "select" => "m.id_magasin AS id_magasin_enseigne",
                "group" => "me.id_magasin_enseigne",
                "path" => "id_magasin_enseigne"
            ),
            self::SEPARATION_MAGASIN => array(
                "select" => "IF( id_etat_doc IN (1,2,3,4,5) , dd.id_magasin, IF(id_etat_doc = 15,blc.id_magasin, IF(id_etat_doc IN (18,19),df.id_magasin,IF(id_etat_doc IN (6, 7, 10, 9) ,cdc.id_magasin ,IF(id_etat_doc IN (59, 60, 62) ,tic.id_magasin ,IF(id_etat_doc = 31, blf.id_magasin, null))))) ) id_magasin",
                "group" => "IF( id_etat_doc IN (1,2,3,4,5) , dd.id_magasin, IF(id_etat_doc = 15,blc.id_magasin, IF(id_etat_doc IN (18,19),df.id_magasin,IF(id_etat_doc IN (6, 7, 10, 9) ,cdc.id_magasin ,IF(id_etat_doc IN (59, 60, 62) ,tic.id_magasin ,IF(id_etat_doc = 31, blf.id_magasin,  null))))) )",
                "path" => "id_magasin"
            ),
            self::SEPARATION_CLIENT_CATEG => array(
                "select" => "cc.id_client_categ",
                "path" => "id_client_categ"
            ),
            self::SEPARATION_FOURNISSEUR_CATEG => array(
                "select" => "fc.id_fournisseur_categ",
                "path" => "id_fournisseur_categ"
            ),
            self::SEPARATION_FOURNISSEUR => array(
                "select" => "annu.ref_contact ref_fournisseur_lmb, af.id_fournisseur",
                "group" => "af.id_fournisseur",
                "where" => "d.id_contact",
                "path" => "ref_fournisseur_lmb,id_fournisseur"
            ),
            self::SEPARATION_FOURNISSEUR_VENTE => array(
                "select" => "annuffav.ref_contact ref_fournisseur_lmb_fav, affav.id_fournisseur id_fournisseur_fav",
                "group" => "affav.id_fournisseur",
                "where" => "a.id_fournisseur_favori",
                "path" => "ref_fournisseur_lmb_fav,id_fournisseur_fav"
            ),
            self::SEPARATION_CLIENT => array(
                "select" => "ac.id_contact AS id_client_interne, ac.id_contact AS id_client, ac.id_contact AS lib_client",
                "group" => "ac.id_contact",
                "where" => "d.id_contact",
                "path" => "id_client_interne,id_client,lib_client"
            ),
            self::SEPARATION_ACTIVITE => array(
                "select" => "a.id_activite",
                "path" => "id_activite"
            ),
            self::SEPARATION_MARQUE => array(
                "select" => "a.id_marque",
                "path" => "id_marque"
            ),
            self::SEPARATION_ARTICLE_CATEG => array(
                "select" => "artc.ref_art_categ",
                "group" => "artc.system,artc.ordre_general,artc.ref_art_categ",
                "where" => "artc.ref_art_categ",
                "path" => "ref_art_categ"
            ),
            self::SEPARATION_ARTICLE => array(
                "select" => "dle.ref_article,a.lib_article",
                "group" => "dle.ref_article,a.lib_article",
                "where" => "a.id_article",
                "path" => "ref_article,lib_article"
            ),
            self::SEPARATION_CATEG_COMMERCIALE => array(
                "select" => "acomm.id_commercial_categ",
                "path" => "id_commercial_categ"
            ),
            self::SEPARATION_EQUIPE_COMMERCIALE => array(
                "select" => "acomm.id_commercial_equipe",
                "path" => "id_commercial_equipe"
            ),
            self::SEPARATION_COMMERCIAL => array(
                "select" => "dvc.id_contact AS id_commercial",
                "group" => "dvc.id_contact",
                "path" => "id_commercial"
            ),
            self::SEPARATION_GEO => array(
                "select" => "zg.id_zone_geo id_zone_geo",
                "group" => "zg.id_zone_geo",
                "path" => "id_zone_geo"
            ),
            self::SEPARATION_PAYS => array(
                "select" => "d.id_pays_contact id_pays",
                "group" => "d.id_pays_contact",
                "path" => "id_pays"
            ),
            self::SEPARATION_ARTICLE_PARENT => array(
                "select" => "a3.ref_article ref_article_parent,a3.lib_article lib_article_parent",
                "group" => "a3.ref_article,a3.lib_article",
                "where" => "a3.id_article",
                "path" => "ref_article_parent,lib_article_parent"
            )            
        );

        foreach($this->separations as $ref_separation){
            if(!empty($separation = $separations[$ref_separation]) && !empty($separation["select"]) && !empty($separation["path"])){
                $query_select .= $separation["select"].", ";
                $query_group .= ($separation["group"] ?? $separation["select"]).", ";
                $path .= $separation["path"].",";
                if (!empty($this->separations_datas[$ref_separation]) || (!empty($this->radioRequesters[$ref_separation]) && $this->radioRequesters[$ref_separation] === "perso"))
                    $query_where .= (!empty($query_where) ? " AND " : " ").$this->makeWhereIn ($separation["where"] ?? $separation["group"] ?? $separation["select"], $this->separations_datas[$ref_separation]);
            }
        }
        
        if (empty($query_select) || empty($query_group)) return false;
        
        $query_group = substr($query_group,0,-2);
        //$query_order = substr($query_order,0,-1);
        $path = substr($path,0,-1);
       

        $query = "SELECT $query_select ";
        $champs = array(
            self::CA                   => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),dle.montant_ht,0),0)) {periode_sql}ca_ht",
            self::CA_PREVISIONNEL      => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc = 3,dle.montant_ht* IFNULL(dd.taux_transformation,0)/100,0),0)) {periode_sql}ca_previsionnel",
            self::CA_POTENTIEL         => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc = 3,dle.montant_ht,0),0)) {periode_sql}ca_potentiel",
            self::CA_CDC               => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (8,9,10,74),dle.montant_ht,0),0)) {periode_sql}ca_cdc",
            self::CA_MOYEN             => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),dle.montant_ht,0) ,0) )/(SELECT COUNT(DISTINCT(IF({condition} AND id_etat_doc IN (18,19),df.ref_doc,NULL)))) {periode_sql}ca_moyen  ",
            self::VALEUR_STOCK         => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc = 31,dle.pu_ht*(IF(dle.remise/100,dle.remise/100,1))*dle.qte,0),0)) {periode_sql}valeur_stock",
            self::COUT_REVIENT         => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),((IFNULL(dle.pa_ht,0)".($this->fd ? "+IFNULL(dle.fd_ht,0)" : "").")*dle.qte),0),0)) {periode_sql}cout_revient",
            self::ACHATS               => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (34,35),dle.montant_ht,0),0)) {periode_sql}achats_ht",
            self::RECUS                => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc = 31,dle.montant_ht,0),0)) {periode_sql}recus_ht",
            self::MARGE                => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (18,19), IFNULL(dle.marge_nette_total,0) , 0),0)) {periode_sql}marge",
            self::MARGE_BRUTE          => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),((IFNULL(dle.pu_ht,0)-IFNULL(dle.pa_ht,0))*dle.qte)-dle.montant_remise,0),0)) {periode_sql}marge_brute",
            self::MARGE_POURCENT       => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),((IFNULL(dle.pu_ht,0)-IFNULL(dle.pa_ht,0)".($this->fd ? "-IFNULL(dle.fd_ht,0)" : "").")*dle.qte)-dle.montant_remise,0),0))/SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (18,19),dle.net_ht,0)*dle.qte,0))*100 {periode_sql}marge_pourcent",
            self::MARGE_BRUTE_POURCENT => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),((IFNULL(dle.pu_ht,0)-IFNULL(dle.pa_ht,0))*dle.qte)-dle.montant_remise,0),0))/SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (18,19),dle.net_ht,0)*dle.qte,0))*100 {periode_sql}marge_brute_pourcent",
            self::QTE_COMMANDEE        => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (9,10,74),dle.qte,0),0)) {periode_sql}qte_commandee",
            self::QTE_FACTUREE         => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (18,19),dle.qte,0),0)) {periode_sql}qte_facturee",
            self::QTE_LIVREE           => "SUM(IF({condition},IF(id_etat_doc = 15,dle.qte,0),0)) {periode_sql}qte_livree",
            self::QTE_RECUE            => "SUM(IF({condition},IF(id_etat_doc = 31,dle.qte,0),0)) {periode_sql}qte_recue",
            self::QTE_ACHAT_CDC        => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (27,28),dle.qte,0),0)) {periode_sql}qte_achat_cdc",
            self::QTE_ACHAT_FAC        => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (34,35),dle.qte,0),0)) {periode_sql}qte_achat_fac",
            self::COUT_ACHAT           => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc IN (18,19),((IFNULL(dle.pa_ht,0))*dle.qte),0),0)) {periode_sql}cout_achat",
            self::CA_TTC               => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),dle.montant_ttc,0),0)) {periode_sql}ca_ttc",
            self::QTE_VENTES           => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`)),IF(id_etat_doc = 62,dle.qte,0),0)) {periode_sql}qte_ventes",
            self::VISITES              => "{periode_sql}stats.visites {periode_sql}visites",
            self::TAUX_TRANSFORMATION  => "(SELECT COUNT(DISTINCT(IF({condition},df.ref_doc,NULL)))/{periode_sql}stats.visites) {periode_sql}taux_transformation",
            self::CA_MOYEN_VISITE      => "SUM(IF({condition} && ((`dle`.`visible` = 1) AND ISNULL(`dle`.`id_doc_line_parent`) AND (`dle`.`is_chiffre_affaires` = 1)),IF(id_etat_doc IN (18,19),dle.montant_ht,0),0))/{periode_sql}stats.visites {periode_sql}ca_moyen_visite",
            self::CA_BLC_NON_FACTURE   => "SUM(IF({condition},IF(id_etat_doc = 15 && dle.visible && blc.ref_doc_fac IS NULL,dle.montant_ht,0),0)) {periode_sql}ca_blc_non_facture",
        );

        $stats_left_join = (in_array(self::VISITES, $this->champs) || in_array(self::TAUX_TRANSFORMATION, $this->champs) || in_array(self::CA_MOYEN_VISITE, $this->champs)) ? array() : null;
        foreach ($this->periodes as $periode){
            $condition = ($this->decoupage == self::DECOUPAGE_JOUR || $this->decoupage == self::DECOUPAGE_MOIS || $this->decoupage == self::DECOUPAGE_ANNEE) ? "SUBSTRING(CONVERT_TZ(d.date_creation_doc,'$offsetServer','$offsetEntreprise'),1,".strlen($periode).")='$periode'" : "";
            $condition = ($periode == "total") ? "1" : $condition;
            $periode_sql = str_replace("-", "_", $periode)."_";

            if(!is_null($stats_left_join)){
                $stats_left_join[] = ($periode == "total") ? "
                    LEFT JOIN (
                        SELECT mts.id_magasin, SUM(sdt.entrees) visites FROM stats_datas_trafic sdt 
                        JOIN magasins_trafic_sources mts ON sdt.id_magasin_trafic_source = mts.id_magasin_trafic_source 
                        WHERE sdt.magasin_ouvert = 1 AND sdt.date >= {$bdd->quote($this->du)} AND sdt.date <= {$bdd->quote($this->au)}
                        GROUP BY mts.id_magasin
                    ) ".$periode_sql."stats ON ".$periode_sql."stats.id_magasin = m.id_magasin
                " : "
                    LEFT JOIN (
                        SELECT mts.id_magasin, SUM(sdt.entrees) visites FROM stats_datas_trafic sdt 
                        JOIN magasins_trafic_sources mts ON sdt.id_magasin_trafic_source = mts.id_magasin_trafic_source 
                        WHERE sdt.magasin_ouvert = 1 AND SUBSTRING(sdt.date,1,".strlen($periode).") = {$bdd->quote($periode)}
                        GROUP BY mts.id_magasin
                    ) ".$periode_sql."stats ON ".$periode_sql."stats.id_magasin = m.id_magasin
                ";
            }

            foreach($this->champs as $ref_champ){
                if(!empty($champ = $champs[$ref_champ])){
                    $query .= str_replace("{periode_sql}", $periode_sql, str_replace("{condition}", $condition, $champ)).", ";
                }
            }
        }

        $query = substr ($query, 0,-2);

        // Déterminer le type de JOIN pour art_categs
        $art_categ_join = "LEFT JOIN";
        if (!empty($this->separations_datas[self::SEPARATION_ARTICLE_CATEG])) {
            $art_categ_join = "RIGHT JOIN";
        }

        $query .= " FROM documents d
                    LEFT JOIN docs_lines_etendues_full dle ON d.ref_doc=dle.ref_doc
                    LEFT JOIN docs_lines_etendues dle2 ON dle.id_doc_line_parent=dle2.id_doc_line
                    LEFT JOIN articles a2 ON dle2.ref_article = a2.ref_article
                    LEFT JOIN doc_dev dd ON d.ref_doc = dd.ref_doc
                    LEFT JOIN doc_tic tic ON d.ref_doc = tic.ref_doc
                    LEFT JOIN doc_fac df ON d.ref_doc = df.ref_doc
                    LEFT JOIN doc_blc blc ON d.ref_doc = blc.ref_doc
                    LEFT JOIN doc_cdc cdc ON d.ref_doc = cdc.ref_doc
                    LEFT JOIN doc_blf blf ON d.ref_doc = blf.ref_doc
                    LEFT JOIN magasins m ON df.id_magasin = m.id_magasin
                    ".(!is_null($stats_left_join) ? implode(" ", $stats_left_join) : "")."
                    LEFT JOIN magasins_enseignes me ON m.id_magasin_enseigne = me.id_magasin_enseigne
                    LEFT JOIN contacts annu ON d.id_contact = annu.id_contact
                    LEFT JOIN annu_client ac ON d.id_contact = ac.id_contact
                    LEFT JOIN annu_fournisseur af ON d.id_contact = af.id_fournisseur
                    LEFT JOIN fournisseurs_categories fc ON af.id_fournisseur_categ=fc.id_fournisseur_categ
                    LEFT JOIN clients_categories cc ON ac.id_client_categ = cc.id_client_categ
                    LEFT JOIN articles a ON dle.ref_article = a.ref_article ".
                    (in_array(self::SEPARATION_ARTICLE_PARENT, $this->separations) ?
                    "LEFT JOIN articles a3 ON a.id_article_groupe=a3.id_article_groupe AND a3.statut_groupe = ".articles_groupes::$statut_parent." " : "")."
                    LEFT JOIN article_marques am ON a.id_marque = am.id_marque
                    $art_categ_join art_categs artc ON a.id_art_categ=artc.id_art_categ
                    LEFT JOIN activites act ON act.id_activite = a.id_activite ".
                    (in_array(self::SEPARATION_GEO, $this->separations) ?
                    "LEFT JOIN docs_zones_geo dzg ON d.ref_doc=dzg.ref_doc
                     LEFT JOIN zones_geo zg ON dzg.id_zone_geo=zg.id_zone_geo " : "").
                     (in_array(self::SEPARATION_CATEG_COMMERCIALE,$this->separations) || in_array(self::SEPARATION_EQUIPE_COMMERCIALE,$this->separations) || in_array(self::SEPARATION_COMMERCIAL,$this->separations) ?
                    "LEFT JOIN doc_ventes_commerciaux dvc ON d.ref_doc = dvc.ref_doc ".
                    "LEFT JOIN annu_commercial acomm ON dvc.id_contact = acomm.id_contact " : "").
                    (in_array(self::SEPARATION_FOURNISSEUR_VENTE,$this->separations)  ?
                    "LEFT JOIN contacts annuffav ON a.id_fournisseur_favori = annuffav.id_contact ".
                    "LEFT JOIN annu_fournisseur affav ON annuffav.id_contact = affav.id_fournisseur " : "").
                    "LEFT JOIN pays p ON d.id_pays_contact = p.id_pays ".
                    "WHERE $query_where
                    GROUP BY $query_group ".($this->lignes_ss_total ? "WITH ROLLUP" : "" );//."
                    //ORDER BY $query_order;";
        $this->path = $path;
        $resultset=$bdd->query($query);
        if (!$resultset) return false;
        return $resultset;
    }
    
    protected function get_lib($node,$value){
        global $_REQUETEUR_AFFICHE_REF_CLIENT,$_REQUETEUR_AFFICHE_REF_ARTICLE;
        $bdd = self::_getBdd();
        
        $return = $value;
        
        if ( is_null($value) || $value == "") return "Non défini";
        
        switch ($node){
            case 'id_magasin_enseigne' :
                $return = magasin::getInstance($value)->getLib_enseigne();
                break;
            case 'id_magasin' :
                $return = magasin::_getLib($value);
                break;
            case 'id_client_categ' :
                $resultat = $bdd->query("SELECT lib_client_categ FROM `clients_categories` WHERE id_client_categ = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib_client_categ : $value;
                break;
            case 'id_fournisseur_categ' :
                $resultat = $bdd->query("SELECT lib_fournisseur_categ FROM `fournisseurs_categories` WHERE id_fournisseur_categ = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib_fournisseur_categ : $value;
                break;            
            case 'id_marque' :
                $resultat = $bdd->query("SELECT lib FROM `article_marques` WHERE id_marque = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib : $value;
                break;             
            case 'id_client' :
                $return = \LMBCore\Contacts\Contact::getRefContact($value);
                break;            
            case 'id_client_interne' :
                $ref_interne = \LMBCore\Contacts\Contact::getRefInterne($value);
                $return = (!empty($ref_interne)?$ref_interne:"Non défini");
                break;                
            case 'lib_client' :
                $return = \LMBCore\Contacts\Contact::_getNom($value);
                break;
            case 'id_constructeur' :
            case 'id_commercial' :
            case 'id_fournisseur' :
            case 'id_fournisseur_fav' :
                $return = \LMBCore\Contacts\Contact::_getNom($value);
                break;
            case 'ref_article' :
                //choix du type de référence article à afficher
                switch ($_REQUETEUR_AFFICHE_REF_ARTICLE){
                    case "INTERNE":{
                        $ref_interne = article::getRefInterne($value);
                        $ref_article = $value.(!empty($ref_interne)?"/".$ref_interne:"");
                        break;
                    }
                    case "OEM":{
                        $ref_oem = article::getRefOEM($value);
                        $ref_article = $value.(!empty($ref_oem)?"/".$ref_oem:"");
                        break;
                    }
                    case "INTERNE_SEUL":{
                        $ref_interne = article::getRefInterne($value);
                        $ref_article = (!empty($ref_interne)?$ref_interne:$value);
                        break;
                    }
                    case "OEM_SEUL":{
                        $ref_oem = article::getRefOEM($value);
                        $ref_article = (!empty($ref_oem)?$ref_oem:$value);
                        break;
                    }
                    default :{// au cas où un dev casse la variable, on laisse le fonctionnement original
                        $ref_interne = article::getRefInterne($value);
                        $ref_article = $value.(!empty($ref_interne)?"/".$ref_interne:"");
                        break;
                        
                    }
                                      
                }
                $return = "$ref_article";
                break;
            case 'id_activite' :
                $resultat = $bdd->query("SELECT lib_activite FROM `activites` WHERE id_activite = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib_activite : $value;
                break;

            case 'ref_art_categ' :
                $resultat = $bdd->query("SELECT lib_art_categ, profondeur FROM `art_categs` WHERE ref_art_categ = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib_art_categ : $value;

	            if(!empty($resultat->profondeur) && is_numeric($resultat->profondeur)) {
					for($i = 1;$i <= $resultat->profondeur; $i++) {
						$return = '-'.$return;
					}
				}

                break;

            case 'id_commercial_categ' :
                $resultat = $bdd->query("SELECT lib_commercial_categ FROM `commerciaux_categories` WHERE id_commercial_categ = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib_commercial_categ : $value;
                break;
            case 'id_commercial_equipe' :
                $resultat = $bdd->query("SELECT lib_commercial_equipe FROM `commerciaux_equipes` WHERE id_commercial_equipe = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib_commercial_equipe : $value;
                break;
            case 'id_pays' :
                $resultat = $bdd->query("SELECT pays FROM `pays` WHERE id_pays = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->pays : $value;
                break;
            case 'id_zone_geo' :
                $resultat = $bdd->query("SELECT lib FROM `zones_geo` WHERE id_zone_geo = ".$bdd->quote($value))->fetchObject();
                $return = $resultat ? $resultat->lib : $value;
                break;            
        }
        
        return trim($return);
    }
        
    public function genere_ods(&$datas = false, $nameFile = "données_par_segment", $return_base64 = false){
        
        $datas = empty($datas) ? $this->getDatas() : $datas;
        if (empty($datas)) return false;

        $path = explode(",", $this->path);
        
        $this->ods = new ods();
        $this->table = new odsTable("Requeteur");

        $Helvetica = new odsFontFace('Helvetica');
        $this->ods->addFontFaces($Helvetica);
        
        $stylecol = new odsStyleTableColumn();
        $stylecol->setColumnWidth("10cm");
        $stylecoldatas = new odsStyleTableColumn();
        $stylecoldatas->setColumnWidth("4cm");
        $col1 = new odsTableColumn($stylecol);
        $this->table->addTableColumn($col1);
        
        $titre = new odsStyleTableCell();
        $titre->setFontWeight('bold');
        $titre->setFontSize("13pt");
        $titre->setTextAlign('left');
        $titre->setVerticalAlign('middle');
        $titre->setFontFace($Helvetica);

        // Titre
        $row = new odsTableRow();
        $cell = new odsTableCellString("Requêteur du ".date("d/m/Y \à H\hi",  strtotime($this->du))." ".langage::write('au')." ".date("d/m/Y \à H\hi",  strtotime($this->au)) . " (générée le ".date('d/m/Y').")", $titre);
        $cell->setNumberColumnsSpanned(15);
        $row->addCell( $cell );
        $this->table->addRow($row);
        
        $titre_periode = new odsStyleTableCell();
	    $titre_periode->setFontWeight('bold');
        $titre_periode->setTextAlign('center');
        $titre_periode->setBackgroundColor('#D2D2D2');
        $titre_periode->setFontFace($Helvetica);
        
        $total_cell = new odsStyleTableCell();
	    $total_cell->setFontWeight('bold');
        $total_cell->setBackgroundColor('#DEDEDE');
        $total_cell->setFontFace($Helvetica);
        
        $total_cell_empty = new odsStyleTableCell();
	    $total_cell_empty->setFontWeight('bold');
        $total_cell_empty->setBackgroundColor('#BBBBBB');
        $total_cell_empty->setColor("#CCCCCC");
        $total_cell_empty->setFontFace($Helvetica);
        
        $titre_col = new odsStyleTableCell();
	    $titre_col->setFontWeight('bold');
        $titre_col->setTextAlign('center');
        $titre_col->setFontFace($Helvetica);
        $titre_col->setBackgroundColor('#EEEEEE');
        $titre_col->setBorderLeft('0.002cm solid #000000');
        
        
        $titre_row = new odsStyleTableCell();
	    $titre_row->setFontWeight('bold');
        $titre_row->setFontFace($Helvetica);

        $empty_cell = new odsStyleTableCell();
	    $empty_cell->setColor("#CCCCCC");
        $empty_cell->setFontFace($Helvetica);
        
        $dark_cell_empty = new odsStyleTableCell();
	    $dark_cell_empty->setColor("#CCCCCC");
        $dark_cell_empty->setBackgroundColor('#DDDDDD');
        $dark_cell_empty->setFontFace($Helvetica);
        
        $dark_cell = new odsStyleTableCell();
	    $dark_cell->setBackgroundColor('#DDDDDD');
        $dark_cell->setFontFace($Helvetica);
        
        $row = new odsTableRow();
        $cell = new odsTableCellString("",$titre_periode);
        $cell->setNumberColumnsSpanned(count($path));

        $row->addCell( $cell );

        for ($i = 0;$i < count($this->periodes); $i++){
            $cell = new odsTableCellString($this->periodes[$i],$titre_periode);
            $cell->setNumberColumnsSpanned(count($this->champs));
            $row->addCell( $cell );
        }

        $this->table->addRow($row);
        
        $row = new odsTableRow();
        $separations_lib = "";

        foreach($this->separations as $separation){

           if (!empty($separation)) {

               if ($separation == self::SEPARATION_CLIENT) {
                    $separations_lib = "Ref interne";
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );                   
                    $separations_lib = "Ref LMB";
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );     
                    $separations_lib = $this->separations_libs[$separation];
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );
               }elseif ($separation == self::SEPARATION_ARTICLE) {
                    $separations_lib = "Ref article";
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );                   
                    $separations_lib = $this->separations_libs[$separation];
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );
               }elseif ($separation == self::SEPARATION_ARTICLE_PARENT) {
                    $separations_lib = "Ref article parent";
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );                   
                    $separations_lib = $this->separations_libs[$separation];
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );
               }elseif ($separation == self::SEPARATION_FOURNISSEUR) {
                    $separations_lib = "Ref fournisseur";
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );
                    $separations_lib = $this->separations_libs[$separation];
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );
               }elseif ($separation == self::SEPARATION_FOURNISSEUR_VENTE) {
               $separations_lib = "Ref fournisseur";
               $cell = new odsTableCellString($separations_lib,$titre_col);
               $row->addCell( $cell );
               $separations_lib = $this->separations_libs[$separation];
               $cell = new odsTableCellString($separations_lib,$titre_col);
               $row->addCell( $cell );
           }
               else {
                    $separations_lib = $this->separations_libs[$separation];
                    $cell = new odsTableCellString($separations_lib,$titre_col);
                    $row->addCell( $cell );
               }

           }

        }

        for ($i = 0; $i < (count($this->periodes) * count($this->champs)); $i++) {

            $coldata = new odsTableColumn($stylecoldatas);
            $this->table->addTableColumn($coldata);
            $periode_id = floor($i/count($this->champs));
            $cell = new odsTableCellString($this->champs_libs[$this->champs[$i % count($this->champs)]], $titre_col );
            $row->addCell( $cell );

        }

        $this->table->addRow($row);

        $path_old_values = array();

        foreach ($path as $init){
            $path_old_values[] = "#0#";
        }

        $path_level = 0;
        $totaux = array();
        $titre_ligne = array();
        $nb_lignes = 0;
        $ligne_valide = false;

        while ($line = $datas->fetchObject()){
            $this_titre = "";
            $row = new odsTableRow();
            foreach ($path as $id){

                if (empty($titre_ligne[$id]) || $titre_ligne[$id][0] != $line->{$id} ){
                    $titre_ligne[$id][0] = $line->{$id};
                    $titre_ligne[$id][1] = $this->get_lib ($id, $line->{$id});
                }

                $this_titre = $titre_ligne[$id][1];
                $cell = new odsTableCellString($this_titre,$titre_row);
                $row->addCell( $cell );
                $ligne_valide = $ligne_valide || ($this_titre != 'Non défini');

            }

            $colnum = 0;
            $pernum = 0;

            foreach($this->periodes as $periode){
                $periode = strtolower(str_replace("-", "_", $periode));
                foreach($this->champs as $col){
                    $valeur = ($col != self::TAUX_TRANSFORMATION) ? (round(empty ($line->{$periode."_".$col}) ? 0 : $line->{$periode."_".$col},2)) : (empty ($line->{$periode."_".$col}) ? 0 : $line->{$periode."_".$col});

	                $style = ($pernum % 2) ? (empty($valeur) ? $dark_cell_empty : $dark_cell ) : (empty($valeur) ? $empty_cell : null );

	                if($col == self::TAUX_TRANSFORMATION){
	                    $cell = new odsTableCellPercentage($valeur, $style);
                    } else if (strpos($col,'qte') !== false || strpos($col,'pourcent') !== false || strpos($col,'taux') !== false || $col == 'visites'){
                        $cell = new odsTableCellFloat($valeur, $style);
                    } else {
                        $cell = new odsTableCellCurrency($valeur, devise::getDefaut()->getAbrev_devise(), $style);
                    }

                    $row->addCell( $cell );

                    if (empty($totaux[count($path)-1][$periode."_".$col])) {
	                    $totaux[count($path)-1][$periode."_".$col] = $valeur;
                    } else {
	                    $totaux[count($path)-1][$periode."_".$col] +=  $valeur;
                    }

                    $colnum++;
                    $ligne_valide = $ligne_valide || !empty($valeur);

                }

                $pernum++;

            }

            if ($ligne_valide) {
                $this->table->addRow($row);
                $nb_lignes++;
            }

        }

        $this->table->setVerticalSplit(3);

        // Ajout de la table
        $this->ods->addTable($this->table);

        // Download the file
        if ($return_base64) {
            return $this->ods->base64OdsFile();
        } else {
            $this->ods->downloadOdsFile($nameFile.".ods");
        }

        return;
    }

    public static function getDatas_toextract($type){
        $datas_toextract = array();
        if(in_array($type, array_keys(self::REQUETEUR_TYPE))){
            if(in_array($type, ['mini', 'ventes', 'achats'])){

                $datas_toextract[] = ['value' => self::SEPARATION_MARQUE, 'text' => __(410138,"Marque"), 'type' => "multiselect", 'options' => array_merge(\marque::getAllMarques(), [['id' => "null", 'lib' => __(950129,"Non défini")]])];
                $datas_toextract[] = ['value' => self::SEPARATION_ARTICLE_CATEG, 'text' => __(410139,"Catégorie d'article"), 'type' => "multiselect", 'options' => array_merge( array_values(array_map(function($categ){
                    return array('id' => $categ->ref_art_categ, 'lib' => $categ->lib_art_categ, 'indentation' => ($categ->indentation*12).'px');
                }, art_categ::getAllCateg(true))),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                $datas_toextract[] = ['value' => self::SEPARATION_ARTICLE, 'text' => __(410140,"Article"), 'type' => "article_selector", 'text_all' => __(410513,"Tous les articles"), 'options' => []];
                $datas_toextract[] = ['value' => self::SEPARATION_ARTICLE_PARENT, 'text' => __(280119,"Article parent"), 'type' => "article_selector", 'text_all' => __(410513,"Tous les articles"), 'options' => []];

                if(in_array($type, ['mini', 'ventes'])){
                    $datas_toextract[] = ['value' => self::SEPARATION_ENSEIGNE, 'text' => __(410136,"Enseigne"), 'type' => "multiselect", 'options' => array_merge( array_map(function($enseigne){
                        return array('id' => $enseigne->id_magasin_enseigne, 'lib' => $enseigne->lib_enseigne);
                    }, (array)charger_all_enseignes()),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                    $datas_toextract[] = ['value' => self::SEPARATION_MAGASIN, 'text' => __(410137,"Centre de profit"), 'type' => "multiselect", 'options' => array_merge( array_map(function($mag){
                        return array('id' => $mag->id_magasin, 'lib' => $mag->lib_magasin);
                    }, magasin::charger_all_magasins()),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                }

                if(in_array($type, ['ventes', 'achats'])){
                    $datas_toextract[] = ['value' => self::SEPARATION_ACTIVITE, 'text' => __(410277,"Activité"), 'type' => 'multiselect', 'options' => array_merge( array_map(function($activite){
                        return array('id' => $activite->id_activite, 'lib' => $activite->lib_activite);
                    }, activites::getAllActivites()),[['id' => "null", 'lib' => __(950129,"Non défini")]])];

                    if($type == 'ventes') {
                        $datas_toextract[] = ['value' => self::SEPARATION_CLIENT_CATEG, 'text' => __(410278,"Catégorie client"), 'type' => 'multiselect', 'options' => array_merge( array_map(function ($categ) {
                            return array('id' => $categ->id_client_categ, 'lib' => $categ->lib_client_categ);
                        }, array_values(contact_client::charger_clients_categories())),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                        $datas_toextract[] = ['value' => self::SEPARATION_CLIENT, 'text' => __(410279,"Client"), 'type' => 'contact_selector', 'text_all' => __(410512,"Tous les clients"), 'options' => []];
                        $datas_toextract[] = ['value' => self::SEPARATION_EQUIPE_COMMERCIALE, 'text' => __(410280,"Équipe commerciale"), 'type' => 'multiselect', 'options' => array_merge( array_map(function ($equipe) {
                            return array('id' => $equipe->id_commercial_equipe, 'lib' => $equipe->lib_commercial_equipe);
                        }, contact_commercial::charger_commerciaux_equipes()),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                        $datas_toextract[] = ['value' => self::SEPARATION_COMMERCIAL, 'text' => __(410281,"Vendeur"), 'type' => 'multiselect', 'options' => array_merge( array_map(function ($commercial) {
                            return array('id' => $commercial->id_contact, 'lib' => $commercial->nom);
                        }, contact_commercial::getListeCommerciaux(array("actifs" => true))),[['id' => "null", 'lib' => __(950129,"Non défini")]])];

                        if (document::use_zone_geo()) {
                            $lst_zones_geo = zones_geo::getAllZones();
                            $lst_zonages = zonage::getLstZonage('action_co');
                            $zonage_actif = reset($lst_zonages);
                            if (!empty($zonage_actif)) {
                                $zonage = zonage::init($zonage_actif->id);
                                $lst_zones_geo = $zonage->get_ZonesGeo();
                            }
                            $datas_toextract[] = [
                                'value' => self::SEPARATION_GEO, 'text' => __(410282, "Zone géographique"), 'type' => 'multiselect',
                                'options' => array_map(function ($zone) {
                                    return array('id' => $zone->id_zone_geo ?? $zone->id, 'lib' => $zone->lib);
                            }, $lst_zones_geo)];
                        }

                        $datas_toextract[] = ['value' => self::SEPARATION_PAYS, 'text' => __(410283,"Pays"), 'type' => 'multiselect', 'options' => array_merge( array_map(function ($pays) {
                            return array('id' => $pays->id_pays, 'lib' => $pays->pays);
                        }, array_values(LMBCore\Contacts\Adresse::getListePays())),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                        $datas_toextract[] = ['value' => self::SEPARATION_FOURNISSEUR_VENTE, 'text' => __(410285,"Fournisseur"), 'type' => 'contact_selector', 'id_profil' => '5', 'text_all' => __(410514,"Tous les fournisseurs"), 'options' => []];
                    }

                    if($type == 'achats'){
                        $datas_toextract[] = ['value' => self::SEPARATION_FOURNISSEUR_CATEG, 'text' => __(410284,"Catégorie fournisseur"), 'type' => 'multiselect', 'options' => array_merge( array_map(function($categ){
                            return array('id' => $categ->id_fournisseur_categ, 'lib' => $categ->lib_fournisseur_categ);
                        }, contact_fournisseur::charger_fournisseurs_categories()),[['id' => "null", 'lib' => __(950129,"Non défini")]])];
                        $datas_toextract[] = ['value' => self::SEPARATION_FOURNISSEUR, 'text' => __(410285,"Fournisseur"), 'type' => 'contact_selector', 'text_all' => __(410514,"Tous les fournisseurs"), 'options' => []];
                    }
                }
            }
        }
        return $datas_toextract;
    }

    public static function getDatas_toshow($type){
        $datas_toshow = array();
        $acces_prix_achat = \user::getInstance()->checkPermissionByRef(\LMBCore\Permissions\Constants\PermissionRef::VOIR_MODIFIER_TARIFS_ACHAT);
        if(in_array($type, array_keys(self::REQUETEUR_TYPE))){
            switch($type){
                case 'mini':
                    $datas_toshow[0]['title'] = __(410339,"Ventes");
                    $datas_toshow[0]['datas'][] = ['value' => self::CA, 'text' => __(410286,"Chiffre d'affaires HT"), 'infobulle' => __(520126,"Montant total HT des factures clients")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_TTC, 'text' => __(410287,"Chiffre d'affaires TTC"), 'infobulle' => __(520127,"Montant total TTC des factures clients")];
                    $datas_toshow[0]['datas'][] = ['value' => self::QTE_VENTES, 'text' => __(410335,"Quantité vendue"), 'infobulle' => __(520128,"Quantité totale relevée sur les tickets encaissés")];
                    $datas_toshow[0]['datas'][] = ['value' => self::MARGE_BRUTE, 'text' => __(410289,"Marge brute"), 'infobulle' => __(520129,"Différence entre le Chiffre d’affaires et le Montant total HT des prix d’achat des factures clients")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_MOYEN, 'text' => __(550047,"Panier moyen HT"), 'infobulle' => __(520130,"Chiffre d’affaire moyen d’une vente")];

                    if(\lmbconfig::getInstance()->get('MOD_comptage_trafic')) {
                        $datas_toshow[1]['title'][] = __(550032,"Trafic");
                        $datas_toshow[1]['datas'][] = ['value' => self::VISITES, 'text' => __(550030,"Visites"), 'infobulle' => __(520131,"Nombre total de visites")];
                        $datas_toshow[1]['datas'][] = ['value' => self::TAUX_TRANSFORMATION, 'text' => __(550034,"Taux transformation"), 'infobulle' => __(520132,"Nombre de vente moyen par visite")];
                        $datas_toshow[1]['datas'][] = ['value' => self::CA_MOYEN_VISITE, 'text' => __(550046,"Chiffre d’affaires HT moyen par visite"), 'infobulle' => __(520133,"Chiffre d’affaire moyen par visite")];
                    }
                    break;
                case 'ventes':
                    //Données financières ventes
                    $datas_toshow[0]['title'] = __(410340,"Données financières ventes");
                    $datas_toshow[0]['datas'][] = ['value' => self::CA, 'text' => __(410290,"Chiffre d'affaires"), 'infobulle' => __(520134,"Montant total HT des factures clients")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_TTC, 'text' => __(130050,"Chiffre d'affaires TTC"), 'infobulle' => __(520135,"Montant total TTC des factures clients")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_PREVISIONNEL, 'text' => __(410334,"Chiffre d'affaires prévisionnel"), 'infobulle' => __(520136,"Chiffre d’affaires HT des devis clients en attente multiplié par leur taux de réussite")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_POTENTIEL, 'text' => __(410292,"Chiffre d'affaires potentiel"), 'infobulle' => __(520137,"Chiffre d’affaires HT des devis clients en attente")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_CDC, 'text' => __(410293,"Montant HT commandes clients"), 'infobulle' => __(520138,"Montant total HT des commandes client non traitées")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_BLC_NON_FACTURE, 'text' => __(280055,"Montant HT livré non facturé"), 'infobulle' => __(280054,"Montant HT des bons de livraison livrés et non facturés")];
                    $datas_toshow[0]['datas'][] = ['value' => self::CA_MOYEN, 'text' => __(550047,"Panier moyen HT"), 'infobulle' => __(520139,"Chiffre d’affaire moyen d’une vente")];
                    //Données financières achats
                    $next_index = 1;
                    if($acces_prix_achat){
                        $datas_toshow[$next_index]['title'] = __(410341,"Données financières achats");
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::VALEUR_STOCK, 'text' => __(410294,"Valeur en stock"), 'infobulle' => __(520140,"Montant total HT des BLF réceptionnés")];
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::COUT_REVIENT, 'text' => __(410295,"Coût de revient"), 'infobulle' => __(520141,"Montant total HT des prix d’achat + frais de distribution des factures clients")];
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::ACHATS, 'text' => __(410296,"Montant HT total des produits achetés"), 'infobulle' => __(520142,"Montant total HT des factures fournisseurs")];
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::RECUS, 'text' => __(410297,"Montant HT total des produits reçus"), 'infobulle' => __(520143,"Montant total HT des BLF réceptionnés")];
                        $next_index++;
                    }
                    //Données quantitative ventes
                    $datas_toshow[$next_index]['title'] = __(410342,"Données quantitative ventes");
                    $datas_toshow[$next_index]['datas'][] = ['value' => self::QTE_COMMANDEE, 'text' => __(150094,"Quantité commandée"), 'infobulle' => __(520144,"Quantité totale relevée sur les commandes clients en cours, en traitement, traitée")];
                    $datas_toshow[$next_index]['datas'][] = ['value' => self::QTE_LIVREE, 'text' => __(410299,"Quantitée livrée"), 'infobulle' => __(520145,"Quantité totale relevée sur les BLC livrés")];
                    $datas_toshow[$next_index]['datas'][] = ['value' => self::QTE_FACTUREE, 'text' => __(410298,"Quantité facturée"), 'infobulle' => __(520146,"Quantité totale relevée sur les factures clients")];
                    $next_index++;
                    //Données quantitative achats
                    $datas_toshow[$next_index]['title'] = __(410343,"Données quantitative achats");
                    $datas_toshow[$next_index]['datas'][] = ['value' => self::QTE_RECUE, 'text' => __(410300,"Quantité reçue"), 'infobulle' => __(520147,"Quantité totale relevée sur les BLF réceptionnés")];
                    $datas_toshow[$next_index]['datas'][] = ['value' => self::QTE_ACHAT_FAC, 'text' => __(410301,"Quantité facturée"), 'infobulle' => __(520148,"Quantité totale relevée sur les factures fournisseurs")];
                    $next_index++;
                    //Marges
                    if($acces_prix_achat){
                        $datas_toshow[$next_index]['title'] = __(410344,"Marges");
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::MARGE, 'text' => __(410302,"Marge nette"), 'infobulle' => __(520149,"Différence entre le Chiffre d’affaires et le Coût de revient")];
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::MARGE_POURCENT, 'text' => __(410303,"Marge nette %"), 'infobulle' => __(520150,"Marge nette exprimée en pourcentage")];
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::MARGE_BRUTE, 'text' => __(410304,"Marge brute"), 'infobulle' => __(520151,"Différence entre le Chiffre d’affaires et le Montant total HT des prix d’achat des factures clients")];
                        $datas_toshow[$next_index]['datas'][] = ['value' => self::MARGE_BRUTE_POURCENT, 'text' => __(410305,"Marge brute %"), 'infobulle' => __(520152,"Marge brute exprimée en pourcentage")];
                        $next_index++;
                    }
                    break;
                case 'achats':
                    //Données financières ventes
                    $datas_toshow[0]['title'] = __(410346,"Données financières achats");
                    $datas_toshow[0]['datas'][] = ['value' => self::QTE_ACHAT_CDC, 'text' => __(410306,"Quantité commandée"), 'infobulle' => __(520153,"Quantité total relevée sur les commandes fournisseurs en cours et traitée")];
                    $datas_toshow[0]['datas'][] = ['value' => self::QTE_RECUE, 'text' => __(410307,"Quantité reçue"), 'infobulle' => __(520154,"Quantité total relevée sur les BLF réceptionnés")];
                    $datas_toshow[0]['datas'][] = ['value' => self::QTE_ACHAT_FAC, 'text' => __(410309,"Quantité facturée"), 'infobulle' => __(520155,"Quantité total relevée sur les factures fournisseurs")];
                    if(\user::getInstance()->checkPermissionByRef(\LMBCore\Permissions\Constants\PermissionRef::VOIR_MODIFIER_TARIFS_ACHAT)){
                        $datas_toshow[0]['datas'][] = ['value' => self::ACHATS, 'text' => __(410308,"Montant HT total des produits achetés"), 'infobulle' => __(520156,"Coût total HT des produits achetés")];
                    }
                    break;
            }

        }
        return $datas_toshow;
    }

    public static function getDecoupages(){
        return array(
            [
                "id" => self::DECOUPAGE_AUCUN,
                "lib" => __(410310,"Aucun")
            ],
            [
                "id" => self::DECOUPAGE_JOUR,
                "lib" => __(410311,"Jour")
            ],
            [
                "id" => self::DECOUPAGE_MOIS,
                "lib" => __(410312,"Mois")
            ],
            [
                "id" => self::DECOUPAGE_ANNEE,
                "lib" => __(410313,"Année")
            ]
        );
    }

    public static function getModeles(){
        return array(
            [
                "id" => self::MODELE_TODAY,
                "lib" => __(410314,"Aujourd'hui")
            ],
            [
                "id" => self::MODELE_WEEK,
                "lib" => __(410315,"Cette semaine")
            ],
            [
                "id" => self::MODELE_MONTH,
                "lib" => __(410316,"Ce mois")
            ],
            [
                "id" => self::MODELE_YEAR,
                "lib" => __(410317,"Cette année")
            ],
            [
                "id" => self::MODELE_3_YEAR,
                "lib" => __(410318,"Depuis 3 ans")
            ]
        );
    }

    public static function loadRequeteurModele($id_search_favori){
        $bdd = PDO_etendu::getInstance();
        $res = $bdd->query("SELECT * FROM search_favoris WHERE id_search_favori = ".$bdd->quote($id_search_favori))->fetchObject();
        $res->params = json_decode($res->params);
        return $res;
    }

    public static function saveRequeteurModele($datas){
        $bdd = PDO_etendu::getInstance();
        $modele = [
            "id_search_type" => $datas["id_search_type"],
            "date" => date("Y-m-d"),
            "ref_user" => "corporation",
            "params" => json_encode($datas["params"] ?? [])
        ];
        if (isset($datas["desc"]))
            $modele['desc'] = $datas["desc"];
        if(empty($datas["id_search_favori"])){
            $modele["lib"] = $datas["lib"];
            return !empty($bdd->insert("search_favoris", $modele));
        }else{
            return !empty($bdd->update("search_favoris", $modele, "id_search_favori = ".$bdd->quote($datas["id_search_favori"])));
        }
    }

}
<div class="lmb-theme">

    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading"><?php _e_html(122326,"Gestion de stock"); ?></div>
                <h1><?php echo utf8_htmlentities(ucfirst($this->view->stock->getLib_stock())); ?></h1>
            </div>
        </div>
    </header>

    <div class="container row">

        <aside class="sidebar">
            <div class="sidebar-content">

                <ul class="menu">

                    <li class="menu-title"><?php _e_html(122327,"Opérations de gestion"); ?></li>

                    <li>
                        <a href="#/page.php/Stock/Standard/Gestion/Infos?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122328,"Tableau de Bord"); ?>
                        </a>
                    </li>
                    <li>
                        <a href="#/page.php/Stock/Standard/Gestion/Etat?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122329,"Etat de stock"); ?>
                        </a>
                    </li>
                    <li>
                        <a href="#/page.php/Stock/Standard/Etat/Recherche?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(180607,"Etat de stock (ancien)"); ?>
                        </a>
                    </li>

                    <?php if(user::getInstance()->checkPermissionByRef(\LMBCore\Permissions\Constants\PermissionRef::OUTIL_CALCUL_REAPPROVISIONNEMENTS, 'ANY')) : ?>
                        <li>
                            <a href="#documents_commandes_fournisseurs_reappro.php?id_stock=<?php echo $this->view->id_stock; ?>" target="_blank">
                                <?php _e_html(122330,"Stocks à renouveler"); ?>
                            </a>
                        </li>
                    <?php endif; ?>

                    <li class="separator"></li>

                    <li>
                        <a href="#/page.php/Stock/Standard/Historique/EcartsInventaires?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122331,"Historique des écarts d'inventaires"); ?>
                        </a>
                    </li>
                    <li>
                        <a href="#/page.php/Stock/Standard/Historique/EcartsTransferts?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(583024,"Historique des écarts de transfert"); ?>
                        </a>
                    </li>

                    <li class="separator"></li>

                    <li>
                        <a href="#stocks_gestion2.php?page=mouvements&id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122334,"Historique par mouvement"); ?>
                        </a>
                    </li>
                    <li>
                        <a href="#stocks_gestion2.php?page=docs&id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122335,"Historique par document"); ?>
                        </a>
                    </li>
                    <li>
                        <a href="#/page.php/Stock/Standard/Historique/SerialNumber?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122336,"Historique par numéros de séries"); ?>
                        </a>
                    </li>

                    <li class="separator"></li>

                    <li>
                        <a href="#/page.php/Stock/Standard/Historique/Evolution?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(180805,"Evolution du stock"); ?>
                        </a>
                    </li>

                    <li class="active">
                        <a href="#/page.php/Stock/Standard/Gestion/Raz?id_stock=<?php echo $this->view->id_stock; ?>">
                            <?php _e_html(122337,"Réinitialiser le stock"); ?>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <div id="corps_gestion_stock" class="page-content">
            <div class="portlet">
                <div class="portlet-header">
                    <h1><?php _e_html(122338,"Remise à zéro du stock"); ?></h1>
                </div>

                <div id="raz-content">

                    <div id="first_before_raz">
                        <div class="portlet-body text-center">

                            <p class="text-error bold">
                                <?php _e_html(122339,"Attention, si vous réinitialisez ce stock, les quantités et la valorisation monétaire de chaque article de ce stock seront remises à zéro."); ?>
                            </p>

                        </div>
                        <div class="portlet-footer text-center">
                            <a href="#/page.php/Stock/Standard/Gestion/Infos?id_stock=<?php echo $this->view->id_stock; ?>" 
                               class="btn btn-default">
                                <?php _e_html(122340,"Annuler"); ?>
                            </a>
                            <a onclick="" 
                               class="btn btn-secondary" 
                               data-lmb-toggle="#first_before_raz, #next_before_raz"
                               qa_id="620074">
                                <?php _e_html(122341,"Ignorer cet avertissement et réinitialiser le stock"); ?>
                            </a>
                        </div>
                    </div>

                    <div id="next_before_raz" class="hidden">
                            <div class="portlet-body">
                                <table class="style-1 auto-layout width-auto center-block">
                                    <tr>
                                        <td><?php _e_html(122342,"Valorisation"); ?></td>
                                        <td>
                                            <span lmb-currency>
                                                <?php echo (user::getInstance()->checkPermissionByRef(\LMBCore\Permissions\Constants\PermissionRef::VOIR_MODIFIER_TARIFS_ACHAT)) ? $this->view->stock->valeur_stock() : __(122343,"ND"); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><?php _e_html(122344,"Dernier inventaire"); ?></td>
                                        <td>
                                            <?php echo date_Us_to_Fr($this->view->last_inventaire_stock) . ' ' . getTime_from_date($this->view->last_inventaire_stock); ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="portlet-footer text-center">
                                <input id="id_stock" name="id_stock" value="<?php echo $this->view->id_stock; ?>" type="hidden"/>

                                <button qa_id="750006"
                                        type="submit"
                                        class="btn btn-tertiary"
                                        onclick='validerRaz()'>
                                    <?php _e_html(122345,"Valider l'opération"); ?>
                                </button>
                            </div>
                    </div>

                </div>

            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
	function validerRaz(){
            window.page.traitecontent('stocks_gestion2','page.php/Stock/Standard/Gestion/RazValid?id_stock=<?php echo $this->view->id_stock; ?>','true','sub_content');
        };
</script>
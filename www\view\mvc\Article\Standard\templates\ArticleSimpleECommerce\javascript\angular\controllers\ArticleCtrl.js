'use strict';
// 'ui.sortable',
(function () {

    var module = angular.module('ArticleSimpleECommerce', ['ArticleSimpleFilters', 'article.SimpleServices', 'angularModalService', 'standardDirectives', 'internationalisation.Directives', 'angularFileUpload', 'GenericServices', 'Upload', 'Pagination', 'ngJsTree', 'lmb']);

    module.controller('ArticleCtrl', ['$scope', '$rootScope', '$location', '$timeout', 'ArticleService', 'LMBArticleService', 'LMBModalService', '$window', '$interval', function ($scope, $rootScope, $location, $timeout, ArticleService, LMBArticleService, LMBModalService, $window, $interval) {

        var $ = jQuery;
        var majAllPvInterval, postInitTimeout;

        LMBLangage.preloadTags([112602, 112603, 520080, 650281, 181738, 112341, 181375]);


        $scope.change_referencements_title = function () {
            $scope.list_referencement.forEach(element => {
                if (element.modify_title) {
                    element.meta_title = $scope.article.info_generales.lib_article;
                }
                if (element.modify_url) {
                    element.url = formatToSEOUrl(element.meta_title);
                }
            });
            let new_referencement = $scope.list_referencement.find(x => x.id_ecommerce == $scope.curent_referencement.id_ecommerce);
            $scope.curent_referencement = new_referencement
        }


        function convertToPlain(){
            var html = $scope.article.info_generales.desc_courte_html;
            // Create a new div element
            var tempDivElement = document.createElement("div");
        
            // Set the HTML content with the given value
            tempDivElement.innerHTML = html;
        
            // Retrieve the text property of the element 
            return tempDivElement.textContent || tempDivElement.innerText || "";
        };
        



        $scope.$on('$destroy', () => {
            $interval.cancel(majAllPvInterval);
            $timeout.cancel(postInitTimeout);
            delete $scope.article;
            delete $scope.config;
        });

        $scope.limitDisplayCatalogues = 10;
        $scope.content_loaded = false;
        $scope.indexation_options = ["index, follow", "index, nofollow", "noindex, follow", "noindex, nofollow"];
        $scope.tarifs_secondaires = [];

        $scope.manual_update_url = false;
        $scope.list_ecommerce = [];
        $scope.list_referencement = [];
        $scope.curent_referencement = {};
        $scope.show_referencement = false;

        $scope.tarif_principle = null;
        $scope.toggle_caracs = false;
        $scope.toggle_articles_lies = false;
        $scope.toggle_fichiers_attaches = false;
        $scope.toggle_parametrages_avances = false;

        $scope.init = function (id, copie) {
            $scope.caracs_choice = {};


            LMBTools.require({
                traductions: [[112600, 112613], [112614, 112619], [112641, 112652], 112602, 112603, 520080, 650281],
                onReady: function () {
                    $rootScope.safeApply();
                }
            });
            var callbackGetDatas = function (datas) {
                $scope.config = datas.config;
                if (typeof datas.config.unites_gestion === 'object' && !Array.isArray(datas.config.unites_gestion)) {
                    datas.config.unites_gestion = Object.values(datas.config.unites_gestion);
                }
                $scope.list_ecommerce = datas.article.list_ecommerce;
                $scope.list_referencement = datas.article.referencement || [];

                $scope.curent_referencement = $scope.list_referencement[0]
                $scope.show_referencement = !!$scope.list_ecommerce?.length;


                $scope.pieces_jointes = {};
                $scope.pieces_jointes = angular.extend($scope.pieces_jointes, datas.pieces_jointes);

                $scope.article = datas.article;
                $scope.article.tarifs_achats.cout_achat_initial = Number($scope.article.tarifs_achats.cout_achat);
                $scope.article.tarifs_achats.old_cmp = $scope.article.tarifs_achats.cout_moyen_pondere ? Number($scope.article.tarifs_achats.cout_moyen_pondere) : 0;

                if ($scope.article.info_generales.ref_article === undefined) {
                    $scope.article.info_generales.ref_article = "";
                }

                $scope.article.tarifs_achats.current_tab_fournisseur = 1;
                $scope.article.tarifs_ventes.current_tab_grille = $scope.article.tarifs_ventes.grilles[0].id_tarif || 1;

                if ($scope.config.use_fournisseur == "1") { // = HA_gestion_achats
                    $scope.article.gestionAchat = true;
                    $scope.article.gestionAchatVisible = ($scope.article.info_generales.lot == 0 || $scope.article.info_generales.lot == 3);
                    if (!$scope.article.tarifs_achats.tarifs.length) {
                        $scope.addTarifFournisseurArticle();
                    }
                    $scope.majlisteFournisseursArticle();
                } else {
                    $scope.article.gestionAchat = false;
                    $scope.article.tarifs_achats.valeur_achat = $scope.article.tarifs_achats.cout_achat = ($scope.article.tarifs_achats.paa) ? parseFloat($scope.article.tarifs_achats.paa) : 0;
                }

                if ($scope.article.info_generales.type === 'produit') {
                    $scope.article.info_generales.modele = 'service';
                }

                if ($scope.article.info_generales.type == 'service' && $scope.article.info_generales.modele != 'service') {
                    $scope.article.isInfosGAdvOpen = true;
                } else {
                    $scope.article.isInfosGAdvOpen = false;
                }

                if (Object.keys($scope.article.declinaisons_copie).length > 0) {
                    angular.forEach($scope.article.declinaisons_copie, function (carac, id) {
                        $scope.addCaracDeclinaison(id);
                        var decli = $scope.article.declinaisons.filter(function (e) {
                            return e.id_carac == id;
                        });
                        if (decli.length > 0) {
                            decli[0].valeur = carac;
                            decli[0].options = [];
                            angular.forEach(carac, function (val, i) {
                                decli[0].options.push({ $order: i, text: val, value: val });
                            });
                        }
                    });
                }
                delete $scope.article.declinaisons_copie;

                $scope.select_id_liaison_type = '1-has';
                $scope.select_carac_simple = '0';
                $scope.select_carac_declinaison = '0';
                $scope.select_carac_personnalisation = '0';
                $scope.article.liaisons_modif = false;

                if ($scope.article.tarifs_ventes.aff_ppc === 'TTC') {
                    $scope.article.tarifs_ventes.ppc = $scope.article.tarifs_ventes.ppc * (1 + $scope.config.tvas_vente[$scope.article.tarifs_ventes.id_tva].taux / 100);
                }

                if ($scope.article.tarifs_ventes.aff_ppd === 'TTC') {
                    $scope.article.tarifs_ventes.ppd = $scope.article.tarifs_ventes.ppd * (1 + $scope.config.tvas_vente[$scope.article.tarifs_ventes.id_tva].taux / 100);
                }

                // On set 'selected' pour les caracs simple dans la liste de ceux dispo
                angular.forEach($scope.article.caracs, function (car) {
                    let carac = ($scope.config.caracs.simple || []).find(function (c) {
                        return c.id_carac == car.id_carac;
                    });
                    if (carac) {
                        carac.selected = true;
                    }
                });

                // On fait pareil pour les caracs de personnalisation
                angular.forEach($scope.article.personnalisations, function (car) {
                    let carac = ($scope.config.caracs.personnalisations || []).find(function (c) {
                        return c.id_carac == car.id_carac;
                    });
                    if (carac) {
                        carac.selected = true;
                    }
                });

                $scope.majAllPaNet();

                $scope.$broadcast("initAllPv");
                $scope.$broadcast("majAllPv");
                $scope.recalc_colisage();
                if ($scope.article.colisage.nb_colis === 0) {
                    $scope.ajouterColis();
                }

                // Calcul des montants totaux des frais de distribution
                angular.forEach($scope.article.tarifs_ventes.grilles, function (tarif_grille) {
                    angular.forEach(tarif_grille.frais, function (frais) {
                        $scope.majFraisDistribValeurFinal(frais, tarif_grille);
                    });
                });

                // On parse les params personnalisés des liaisons
                angular.forEach($scope.article.liaisons, (liaison) => {
                    angular.forEach(liaison.articles, (article) => {
                        $scope.parseParamsLiaisonArticle(article);
                    });
                });

                $scope.countCataloguesClients = Object.keys($scope.config.catalogues_client).length;
                $scope.config.available_unites_gestion = $scope.getUnitesGestionList({
                    id_unite_gestion: (value => value != $scope.article.id_unite_gestion)
                });

                $scope.config.active_unites_gestion = [];
                $scope.config.allow_unites_contenues = $scope.config.available_unites_gestion?.filter(unit => unit.ref_unite_gestion != 'unite').length

                if (
                    typeof $scope.article.unites_contenues === 'object' &&
                    !$scope.isEmpty($scope.article.unites_contenues)
                ) {
                    $scope.config.active_unites_gestion = Object.keys($scope.article.unites_contenues);
                }

                $scope.article.lib_unite_gestion = $scope.getLibUniteGestion($scope.article.id_unite_gestion || $scope.config.defaut_id_unite_gestion);

                postInitTimeout = $timeout(postInit, 0, false); // permet de lancer postInit() après le rendu du DOM

                // $scope.activerLiaisons();

                if ($scope.article.info_generales.lot == '6' || $scope.article.info_generales.lot == '5') {
                    angular.forEach($scope.article.recette.liste_articles, function (comp) {
                        $scope.updatePrixAchatTotalComposant(comp);
                    });

                    $scope.updateTotalRecette();
                }
                $scope.tarif_principle = $scope.article.tarifs_ventes.grilles[0].tarifs[0];
                $scope.tarif_principle.aff_tarif = $scope.tarif_principle.resultat === 'PU_HT' ? 'HT' : 'TTC';
                $scope.tarif_principle.resultat = $scope.tarif_principle.resultat === 'PU_HT' ? 'PU_HT' : 'PU_TTC';
                if ($scope.article.tarifs_ventes.grilles[0].tarifs.length > 1) {
                    for (var i = 1; i < $scope.article.tarifs_ventes.grilles[0].tarifs.length; i++) {
                        var tarif = $scope.article.tarifs_ventes.grilles[0].tarifs[i];
                        tarif.aff_tarif = tarif.resultat === 'PU_HT' ? 'HT' : 'TTC';
                        tarif.resultat = tarif.resultat === 'PU_HT' ? 'PU_HT' : 'PU_TTC';
                        $scope.tarifs_secondaires.push(tarif);
                    }
                }

                // Si l'article a des déclinaisons on affiche les paramètres avancés
                if ($scope.article.info_generales.statut_groupe) {
                    $scope.toggle_parametrages_avances = true;
                }

                $scope.article.info_generales.ordre_preparation = $scope.article.info_generales.ordre_preparation ??= -1;
            };

            if (copie == "") {
                ArticleService.getDatas(id).then(callbackGetDatas);
            } else {
                ArticleService.dupliquer(copie).then(function (data) {
                    ArticleService.getDatas(id, data.jsonData).then(callbackGetDatas);
                });
            }


        };

        $scope.showMoreItems = function (type) {
            switch (type) {
                case 'catalogues_clients':
                    $scope.limitDisplayCatalogues += 1000;
                    break;
                case 'tarifs':
                    $scope.limitDisplayTarifs += 1000;
                    break;
                case 'magasins':
                    $scope.limitDisplayMagasins += 1000;
                    break;
            }
        }

        // Chargement dans un deuxième temps, pour ne pas bloquer l’affichage de la page : catégories, marques, fournisseurs, catalogues clients…
        var postInit = function () {
            ArticleService.loadAfter().then(function (result) {
                $scope.config.art_categs = result.datas.categories;
                $scope.config.art_categs_select = result.datas.categories_select;
                $scope.config.marques = result.datas.marques;
                $scope.config.fournisseurs = result.datas.fournisseurs;
                if ($scope.article.info_generales.ref_art_categ == null) {
                    $scope.article.info_generales.ref_art_categ = $scope.config.art_categs[0].id;
                    $scope.article.info_generales.lib_art_categ = $scope.config.art_categs[0].lib;
                    if ($scope.article.info_generales.ref_article == "") {
                        $scope.changeCategory();
                    }
                }
                majAllPvInterval = $interval(function () {
                    $scope.$broadcast("majAllPv");
                }, 0);

            });

            $scope.content_loaded = true;

            refreshLibsCategsCatalogues();
            $scope.toggle_caracs = ($scope.article.caracs.length || $scope.article.personnalisations.length) ? 1 : 0;
            $scope.$watch("article.info_generales.lot", function (newVal, oldVal) {
                if (newVal != 0 && newVal != 3) {
                    $scope.article.gestionAchatVisible = false;
                }
            });
            $scope.tarif_four = $scope.article.tarifs_achats.tarifs[0];
        };

        $scope.setChangedTarif = function (tarif_vente) {
            tarif_vente.changed = true;
        }
        $scope.getUnitesGestionList = function (criteria = {}) {

            return $scope.config.unites_gestion.reduce((acc, cur) => {
                return acc.concat(cur.list_valo || [cur]);
            }, []).filter(unit => {
                let match = true;
                for (const [prop, value] of Object.entries(criteria)) {
                    if (typeof value === 'function') {
                        match = match && value(unit[prop])
                    } else {
                        match = match && unit[prop] == value;
                    }
                }

                return match;
            })
        }

        $scope.getLibUniteGestion = function (id_unite_gestion = $scope.article.info_generales.id_unite_gestion) {
            return $scope.getUnitesGestionList().find(unit => unit.id_unite_gestion == id_unite_gestion)?.lib || __(580297, "Unité");
        }

        $scope.verifierTarifsQuantitatifs = function () {
            var checked = true;
            angular.forEach($scope.tarifs_secondaires, function (tarif) {
                if (tarif.indice_qte && parseInt(tarif.indice_qte) < 2) {
                    LMBToast.warning({
                        title: __(610201, "Validation impossible"),
                        message: __(610202, "Pour un tarif quantitatif, la quantité doit être strictement supérieure à 1")
                    });
                    tarif.invalid_qte = true;
                    checked = false;
                } else if ((!tarif.pu_ht && !tarif.pu_ttc && tarif.indice_qte) ||
                    ((tarif.pu_ht || tarif.pu_ttc) && !tarif.indice_qte)) {
                    LMBToast.warning({
                        title: __(610192, "Validation impossible"),
                        message: __(610193, "Veuillez renseigner tous les champs de votre tarif quantitatif")
                    });
                    if (!tarif.indice_qte) {
                        tarif.invalid_qte = true;
                        tarif.invalid_tarif = false;
                    } else {
                        tarif.invalid_tarif = true;
                        tarif.invalid_qte = false;
                    }
                    checked = false;
                } else {
                    tarif.invalid_qte = false;
                    tarif.invalid_tarif = false;
                }
            });
            return checked;
        }
        $scope.saveArticle = function () {
            if ($scope.verifierTarifsQuantitatifs()) {
                $scope.article.tarifs_ventes.grilles[0].tarifs[0] = $scope.tarif_principle;
                if ($scope.tarifs_secondaires.length > 0) {
                    for (var i = 1; i <= $scope.tarifs_secondaires.length; i++) {
                        $scope.article.tarifs_ventes.grilles[0].tarifs[i] = $scope.tarifs_secondaires[i - 1];
                    }
                }
                console.log("in save", $scope.article.referencement);
                $scope.article.referencement.forEach(element => {
                    if (element.meta_desc === "") {
                        console.log("in save", "ok to change");
                        element.meta_desc = LMBTools.convertToPlain($scope.article.info_generales.desc_courte_html);
                    }
                    
                });
                // On crée un nouvel objet dans lequel on ne garde que les propriétés dont on a besoin (on vire les fonctions)
                let clean_scope = {
                    'article': $scope.article,
                    'pieces_jointes': $scope.pieces_jointes
                };
                // On le nettoie pour virer les $$hashkey
                clean_scope.article.caracs = JSON.parse(angular.toJson(clean_scope.article.caracs));
                clean_scope.article.personnalisations = JSON.parse(angular.toJson(clean_scope.article.personnalisations));
                clean_scope.article.catalogues_clients = $scope.cleanCataloguesClients();
                ArticleService.save(clean_scope).then(function (data) {
                    if (data.redirect_path !== undefined) {
                        LMBNavigation.redirect(data.redirect_path);
                    }
                });
            }
        };

        /**
          * Nettoie l'objet article.catalogues_clients afin de ne garder que les catégories
          * sélectionnées pour alléger les données envoyées
          */
        $scope.cleanCataloguesClients = function () {
            let catalogues_clients = {};
            angular.forEach($scope.article.catalogues_clients, (cat, id_cat) => {
                let categs_actives = [];
                angular.forEach(cat.libs, categ => {
                    categs_actives.push(categ.id);
                });

                let catalog_content = angular.copy(cat);
                catalog_content.categories = $scope.getCatalogueContent(catalog_content.categories, categs_actives, cat.categ_unique);

                catalogues_clients[id_cat] = catalog_content;
            });
            return catalogues_clients;
        }

        /**
         * Fonction récursive qui parcourt le catalogue donné pour ne garder que
         * les catégories sélectionnées
         *
         * @param {obj} categories - La liste des catégories
         * @param {array} categs_actives - Les ID des catégories actives
         * @param {int} categ_unique - L'id de la categ unique
         */
        $scope.getCatalogueContent = function (categories, categs_actives, categ_unique) {
            let list_categs = []
            angular.forEach(categories, categ => {
                let children = [];
                if (categ.children && categ.children.length) {
                    children = $scope.getCatalogueContent(categ.children, categs_actives, categ_unique);
                }
                categ.children = children;

                // On ajout la categ à la liste de retour si elle est sélectionnée ou un de ses
                // enfants l'est
                if (categs_actives.indexOf(categ.id_catalogue_categ) != -1 || categ.id_catalogue_categ == categ_unique || categ.children.length) {
                    list_categs.push(categ);
                }
            });
            return list_categs;
        }

        $scope.changeCategory = function () {

            if (typeof $scope.article.info_generales.ref_art_categ != 'undefined') {
                ArticleService.getCategoryData($scope.article.info_generales.ref_art_categ).then(function (data) {
                    var datas = data.datas;
                    if (datas) {
                        $scope.article.caracs = datas.caracs;
                        $scope.caracs = datas.caracs;
                        $scope.article.info_generales.id_activite = (datas.defaut_id_activite || 0);
                        $scope.article.tarifs_achats.id_tva_ha = (datas.defaut_id_tva_achat || 0);
                        $scope.article.tarifs_ventes.id_tva = (datas.defaut_id_tva || 0);
                        $scope.article.info_generales.ordre_preparation = datas.defaut_ordre_preparation ??= -1;
                        $scope.article.info_generales.qte_nb_decimales = datas.defaut_qte_nb_decimales;
                        $scope.article.info_generales.price_nb_decimales = datas.defaut_price_nb_decimales;
                        $scope.article.info_generales.valeur_achat_nb_decimales = datas.defaut_valeur_achat_nb_decimales;
                        $scope.article.info_generales.id_valo = datas.defaut_id_valorisation;
                        $scope.article.info_generales.id_valo = datas.defaut_id_valorisation;
                        $scope.article.preparations_zones.forEach(function (mag) {
                            mag.zone_selected = (datas.defaut_zone_preparation || []);
                            $timeout(function () {
                                $j('.prepa-zones').trigger('refresh-placeholder')
                            });
                        });

                        if (!$scope.article.info_generales.id_article) {
                            $scope.article.tarifs_ventes.grilles.forEach(function (g) {
                                var iTarifx1 = null;
                                for (var i = 0; iTarifx1 === null && i < g.tarifs.length; i++) {
                                    if (g.tarifs[i].indice_qte == 1) {
                                        iTarifx1 = i;
                                    }
                                }
                                $scope.$broadcast("initTarifFromConfig", {
                                    tarif: g.tarifs[iTarifx1],
                                    ref_art_categ: datas.id_art_categ
                                });
                                if ($rootScope.addTarif) {
                                    g.tarifs[iTarifx1] = $rootScope.addTarif;
                                    delete $rootScope.addTarif;
                                }
                            });
                        }

                        // Suppression des caracs vides ou définies à la valeur par défaut
                        angular.forEach($scope.article.caracs, function (carac) {
                            if (!carac.valeur || carac.valeur == carac.defaut_value) {
                                $scope.rmCaracSimple(carac.id_carac);
                            }
                        });
                        // Suppression des caracs de déclinaison vides ou définies à la valeur par défaut
                        angular.forEach($scope.article.declinaisons, function (carac) {
                            if (!carac.valeur || carac.valeur.length == 0 || (carac.valeur.length == 1 && carac.valeur[0] == carac.defaut_value)) {
                                $scope.rmCaracDeclinaison(carac.id_carac);
                            }
                        });
                        // Suppression des caracs de personnalisation vides ou définies à la valeur par défaut
                        angular.forEach($scope.article.personnalisations, function (carac) {
                            if (!carac.valeur || carac.valeur.length == 0 || (carac.valeur.length == 1 && carac.valeur[0] == carac.defaut_value)) {
                                $scope.rmCaracPersonnalisation(carac.id_carac);
                            }
                        });

                        // Parcours des caracs de la catégorie
                        angular.forEach(datas.caracs, function (carac) {
                            var params_cibles = {};
                            if (carac.parametres_cibles) {
                                params_cibles = JSON.parse(carac.parametres_cibles);
                            }
                            var isDeclinaison = (params_cibles.proposer_declinaison == "on" || params_cibles.proposer_declinaison == "1");
                            var isPersonnalisation = (params_cibles.personnalisation == "1");
                            //var isSimple = !(((typeof params.critere_simple) != 'undefined') && (params.critere_simple == "0"));
                            if (isDeclinaison) {
                                var carac_existante = $scope.article.declinaisons.filter(function (e) {
                                    return e.id_carac == carac.id_carac
                                });
                                // Ajout de la carac de déclinaison, si elle n’existe pas encore
                                if (carac_existante.length == 0) {
                                    $scope.addCaracDeclinaison(carac.id_carac);
                                }
                            } else if (isPersonnalisation) {
                                var carac_existante = $scope.article.personnalisations.filter(function (e) {
                                    return e.id_carac == carac.id_carac
                                });
                                // Ajout de la carac de personnalisation, si elle n’existe pas encore
                                if (carac_existante.length == 0) {
                                    $scope.addCaracPersonnalisation(carac.id_carac);
                                }
                            } else {
                                var carac_existante = $scope.article.caracs.filter(function (e) {
                                    return e.id_carac == carac.id_carac
                                });
                                // Ajout de la carac, si elle n’existe pas encore
                                if (carac_existante.length == 0) {
                                    $scope.addCaracSimple(carac.id_carac);
                                }
                            }
                        });
                        $scope.article.info_generales.has_declinaisons = ($scope.article.declinaisons.length > 0);
                    }
                });
            }
        };

        $scope.toggleDeclinaisons = function () {
            if ($scope.article.info_generales.has_declinaisons) {
                $scope.article.caracs.forEach(carac => {
                    if (carac.proposer_declinaisons) {
                        $scope.rmCaracSimple(carac.id_carac);
                        $scope.addCaracDeclinaison(carac.id_carac);
                    }
                });
            } else {
                $scope.article.declinaisons.forEach(carac => {
                    $scope.rmCaracDeclinaison(carac.id_carac);
                    $scope.addCaracSimple(carac.id_carac);
                });
            }
        };

        var refreshLibsCategsCatalogues = function () {
            angular.forEach($scope.article.catalogues_clients, function (cat) {
                var fav = 0;
                var len = cat.libs.length;
                if (len == 0) {
                    cat.description = "";
                } else {
                    for (var i = 0; i < len; i++) {
                        if (cat.fav && cat.fav[0] && cat.libs[i].id == cat.fav[0].id_catalogue_categ) {
                            fav = i;
                            cat.libs[fav].chemin_array = cat.libs[fav].lib.split(">");
                        }
                    }
                    if (len > 1) {
                        cat.description = "(" + cat.libs[fav].lib + "<i class='fa fa-star'></i>";
                        var infobulle = "";
                        var counter = 1;
                        for (var i = 0; i < len; i++) {
                            if (i == fav) {
                                continue;
                            }
                            if (counter < 2) {
                                cat.description += ", " + cat.libs[i].lib;
                            } else if (counter > 2) {
                                infobulle += ", " + cat.libs[i].lib;
                            } else {
                                infobulle += cat.libs[i].lib;
                            }
                            counter++;
                            cat.libs[i].chemin_array = cat.libs[i].lib.split(">");
                        }
                        if (len > 2)
                            cat.description += " <span data-lmb-infobulle='" + infobulle + "'>+ " + (len - 2) + " autre" + (len > 3 ? "s" : "") + "</span>";
                    } else {
                        cat.description = "(" + cat.libs[fav].lib;
                    }
                    cat.description += ")";
                }
            });
        };

        $scope.$emit("majFraisDistribValeurFinal", function (evt, args) {
            $scope.majFraisDistribValeurFinal(args.frais, args.grille);
        });

        $scope.isEmpty = function (obj) {
            for (var prop in obj) {
                if (obj.hasOwnProperty(prop))
                    return false;
            }
            return true;
        };

        $scope.updateTypeArticle = function () {
            switch ($scope.article.info_generales.type) {
                case 'produit':
                    $scope.article.info_generales.gestion_stock = true;
                    $scope.article.info_generales.has_declinaisons = false;
                    break;
                case 'produit_decline':
                    $scope.article.info_generales.gestion_stock = true;
                    $scope.article.info_generales.has_declinaisons = true;
                    $scope.toggle_parametrages_avances = true;
                    break;
                case 'service':
                    $scope.article.info_generales.gestion_stock = false;
                    $scope.article.info_generales.has_declinaisons = false;
                    break;
                case 'service_decline':
                    $scope.article.info_generales.gestion_stock = false;
                    $scope.article.info_generales.has_declinaisons = true;
                    $scope.toggle_parametrages_avances = true;
                    break;
                case 'compo':
                    $scope.article.info_generales.gestion_stock = true;
                    $scope.article.info_generales.has_declinaisons = false;
                    break;
            }
        }

        $scope.addTarifFournisseurArticle = function () {
            $scope.autoCheckFourniseurFavori();

            var newTarifFournisseur = {};

            newTarifFournisseur.id_atf = 0;
            newTarifFournisseur.ref_article = '';
            newTarifFournisseur.id_fournisseur = 0;
            newTarifFournisseur.ref_article_externe = '';
            newTarifFournisseur.lib_article_externe = '';
            newTarifFournisseur.id_devise = $scope.config.devise.default.id_devise;
            newTarifFournisseur.taux_change = 1;
            newTarifFournisseur.pa_brut = $scope.article.tarifs_achats.paa;
            newTarifFournisseur.pa_net = $scope.article.tarifs_achats.paa;
            newTarifFournisseur.mode_ht_ttc = 'HT';
            newTarifFournisseur.date_pa = '';
            newTarifFournisseur.remise = 0;
            newTarifFournisseur.indice_qte = 1;
            newTarifFournisseur.id_tva = 0;
            newTarifFournisseur.dropshipping = 0;
            newTarifFournisseur.nom_fournisseur = 'Non défini';
            newTarifFournisseur.base_tarif = 'PAB';
            newTarifFournisseur.tab_number = $scope.article.tarifs_achats.tarifs.length * Math.round(new Date().getTime() + (Math.random() * 100));
            newTarifFournisseur.frais = [];
            newTarifFournisseur.codec_remise_arriere = '';
            //$scope.addFraisApproche($scope.addFraisApproche(newTarifFournisseur));

            $scope.article.tarifs_achats.tarifs.push(newTarifFournisseur);
            $scope.article.tarifs_achats.current_tab_fournisseur = newTarifFournisseur.tab_number;

            $scope.majPaNet(newTarifFournisseur);
            return true;
        };

        $scope.majFraisDistribValeurFinal = function (frais, grille) {
            if (frais.operateur === 'valeur') {
                frais.taux_change = $scope.getTauxDevise(frais.id_devise);
                frais.valeur_final_euro = frais.valeur / frais.taux_change;
            } else if (frais.operateur === 'pourcent') {
                frais.valeur_final_euro = $scope.article.tarifs_achats.valeur_achat * frais.valeur / 100;
            }

            grille.frais_distrib_mt_total = 0;
            for (var i = 0; i < grille.frais.length; i++) {
                if (typeof grille.frais[i].valeur_final_euro !== "undefined") {
                    grille.frais_distrib_mt_total += grille.frais[i].valeur_final_euro;
                }
            }

            if ($scope.article.tarifs_achats.valeur_achat === undefined) {
                $scope.article.tarifs_achats.valeur_achat = 0;
            }
            grille.cout_revient = parseFloat($scope.article.tarifs_achats.valeur_achat) + parseFloat(grille.frais_distrib_mt_total);

            $.each(grille.tarifs, function (k, tarif_vente) {
                tarif_vente.marge = tarif_vente.pv_ht - grille.cout_revient;
                if (tarif_vente.pv_ht === 0) {
                    tarif_vente.marge_pourcent = 0;
                } else if (grille.cout_revient === 0) {
                    tarif_vente.marge_pourcent = null;
                } else {
                    tarif_vente.marge_pourcent = (tarif_vente.marge * 100) / grille.cout_revient;
                }
            });

            grille.frais_distrib_pourcent_total = 0;
        };

        $scope.majlisteFournisseursArticle = function () {
            $scope.article.tarifs_achats.fournisseursArticle = [];
            $.each($scope.article.tarifs_achats.tarifs, function (k, tarif_four) {
                $scope.article.tarifs_achats.fournisseursArticle.push(tarif_four.id_fournisseur);
            });
        };

        $scope.inFournisseursArticle = function (id_fournisseur) {
            return typeof ($scope.article.tarifs_achats) !== 'undefined'
                && $scope.article.tarifs_achats.fournisseursArticle.indexOf(id_fournisseur) >= 0;
        };

        $scope.majAllPaNet = function () {
            $.each($scope.article.tarifs_achats.tarifs, function (k, tarif_four) {
                $scope.majPaNet(tarif_four);
            });
        };

        $scope.filterFournisseursArticle = function (tarif) {
            let fournisseurs = $scope.config.fournisseurs || [];
            return fournisseurs.filter((four) => {
                return (!$scope.inFournisseursArticle(four.id_fournisseur) || four.id_fournisseur == tarif.id_fournisseur);
            });
        };

        $scope.majPaNet = function (tarif_four) {
            var pa_brut = tarif_four.pa_brut;
            if (tarif_four.mode_ht_ttc === 'TTC') {
                if ($scope.article.tarifs_achats.id_tva_ha !== 0) {
                    pa_brut = pa_brut / (1 + $scope.config.tvas_achat[$scope.article.tarifs_achats.id_tva_ha].taux / 100);
                }
            }

            tarif_four.pa_net = pa_brut - (pa_brut * tarif_four.remise / 100);
            tarif_four.pa_net_euro = tarif_four.pa_net / tarif_four.taux_change;
            tarif_four.cout_achat = tarif_four.pa_net_euro || 0;

            $.each(tarif_four.frais, function (k, frais_approche) {
                $scope.majFraisValeurFinal(frais_approche, tarif_four);
            });

            $scope.majRemiseArriereValeurFinal(tarif_four);

            if (tarif_four.frais.length === 0) {
                $scope.majAchatPrixMoyen();
            }
        };

        $scope.majAllPaBrut = function () {
            $.each($scope.article.tarifs_achats.tarifs, function (k, tarif_four) {
                if (tarif_four.pa_brut) {
                    $scope.majPaBrut(tarif_four);
                }
            });
        };

        $scope.majPaBrut = function (tarif_four) {
            var pa_brut = 0;

            if (tarif_four.base_tarif == "PPC") {
                tarif_four.pa_brut = parseFloat($scope.article.tarifs_ventes.ppc);
                tarif_four.mode_ht_ttc = $scope.article.tarifs_ventes.aff_ppc;
            } else if (tarif_four.base_tarif == "PPD") {
                tarif_four.pa_brut = parseFloat($scope.article.tarifs_ventes.ppd);
                tarif_four.mode_ht_ttc = $scope.article.tarifs_ventes.aff_ppd;
            } else {
                if (tarif_four.pa_net !== 0) {
                    if (tarif_four.remise === 100) {
                        tarif_four.remise = 0;
                    }

                    pa_brut = tarif_four.pa_net / (1 - tarif_four.remise / 100);
                }

                if (tarif_four.mode_ht_ttc === 'TTC') {
                    if ($scope.article.tarifs_achats.id_tva_ha !== 0) {
                        pa_brut = pa_brut * (1 + $scope.config.tvas_achat[$scope.article.tarifs_achats.id_tva_ha].taux / 100);
                    }
                }
                tarif_four.pa_brut = pa_brut;
            }
            $scope.majPaNet(tarif_four);

            $.each(tarif_four.frais, function (k, frais_approche) {
                $scope.majFraisValeurFinal(frais_approche, tarif_four);
            });

            $scope.majAchatPrixMoyen();

        };

        $scope.majPPCD = function (tarif_four) {
            if (tarif_four.base_tarif == "PPC") {
                $scope.article.tarifs_ventes.ppc = tarif_four.pa_brut;
                $scope.article.tarifs_ventes.aff_ppc = tarif_four.mode_ht_ttc;
            } else if (tarif_four.base_tarif == "PPD") {
                $scope.article.tarifs_ventes.ppd = tarif_four.pa_brut;
                $scope.article.tarifs_ventes.aff_ppd = tarif_four.mode_ht_ttc;
            }
        }

        $scope.majFraisValeurFinal = function (frais, tarif_four) {
            if (frais.operateur === 'valeur') {
                frais.taux_change = $scope.getTauxDevise(frais.id_devise);
                frais.valeur_final_euro = frais.valeur / frais.taux_change;
            } else if (frais.operateur === 'pourcent') {
                if (frais.valeur != 0) {
                    var pa_net_euro = tarif_four.pa_net / tarif_four.taux_change;
                    frais.valeur_final_euro = pa_net_euro * frais.valeur / 100;
                } else {
                    frais.valeur_final_euro = 0;
                }
                frais.valeur_final_euro = isNaN(frais.valeur_final_euro) ? 0 : frais.valeur_final_euro;
            }
            $scope.updateCoutAchat(tarif_four);
        };

        $scope.updateCoutAchat = function (tarif_four) {
            tarif_four.frais_approche_mt_total = 0;
            for (var i = 0; i < tarif_four.frais.length; i++) {
                tarif_four.frais_approche_mt_total += tarif_four.frais[i].valeur_final_euro;
            }

            tarif_four.pa_net_euro = tarif_four.pa_net / tarif_four.taux_change;
            tarif_four.cout_achat = tarif_four.pa_net_euro + tarif_four.frais_approche_mt_total;

            if (tarif_four.valeur_remise_arriere_final_euro > 0) {
                tarif_four.cout_achat -= tarif_four.valeur_remise_arriere_final_euro;
            }

            tarif_four.cout_achat = isNaN(tarif_four.cout_achat) ? 0 : tarif_four.cout_achat;

            if (tarif_four.cout_achat !== 0) {
                tarif_four.frais_approche_pourcent_total = tarif_four.frais_approche_mt_total * 100 / tarif_four.cout_achat;
            } else {
                tarif_four.frais_approche_pourcent_total = 0;
            }
            $scope.majAchatPrixMoyen();
        }


        $scope.majRemiseArriereValeurFinal = function (tarif_four) {
            const codec_remise_arriere = tarif_four.codec_remise_arriere;
            if (!codec_remise_arriere) {
                tarif_four.valeur_remise_arriere_final_euro = 0;
            } else {
                if (codec_remise_arriere.mode_de_calcul === 'MONTANT') {
                    tarif_four.valeur_remise_arriere_final_euro = codec_remise_arriere.valeur;
                } else if (codec_remise_arriere.mode_de_calcul === 'TAUX') {
                    tarif_four.valeur_remise_arriere_final_euro = tarif_four.pa_net_euro * codec_remise_arriere.valeur / 100;
                }
                tarif_four.valeur_remise_arriere_final_euro = isNaN(tarif_four.valeur_remise_arriere_final_euro) ? 0 : tarif_four.valeur_remise_arriere_final_euro;
            }
            $scope.updateCoutAchat(tarif_four);
        }


        $scope.majAchatPrixMoyen = function () {
            $scope.updateValeurAchat();
        };

        $scope.autoCheckFourniseurFavori = function () {
            var fav_fournisseur_found = false;
            if ($scope.article.tarifs_achats.tarifs.length > 0) {
                for (var i = 0; i < $scope.article.tarifs_achats.tarifs.length; i++) {
                    var tarif_four = $scope.article.tarifs_achats.tarifs[i];
                    if (tarif_four.id_fournisseur == $scope.article.info_generales.id_fournisseur_favori) {
                        fav_fournisseur_found = true;
                    }
                }
                if (!fav_fournisseur_found) {
                    $scope.article.info_generales.id_fournisseur_favori = $scope.article.tarifs_achats.tarifs[0].id_fournisseur;
                }
            } else {
                $scope.article.info_generales.id_fournisseur_favori = 0;
            }
        };

        $scope.updateValeurAchat = function () {
            var total_pa_net_euro = 0;
            var total_frais_approche_mt_total = 0;
            var total_cout_achat = 0;

            var fav_cout_achat = 0;
            var max_cout_achat = 0;
            var min_cout_achat = 0;

            var nb_fourns_valides = 0;

            if ($scope.article.gestionAchat) {
                if ($scope.article.tarifs_achats.tarifs.length > 0) {
                    for (var i = 0; i < $scope.article.tarifs_achats.tarifs.length; i++) {
                        var tarif_four = $scope.article.tarifs_achats.tarifs[i];

                        if (!tarif_four.pa_brut || tarif_four.pa_brut == "0") {
                            continue;
                        }
                        nb_fourns_valides++;

                        total_pa_net_euro += tarif_four.pa_net_euro;
                        total_frais_approche_mt_total += tarif_four.frais_approche_mt_total;
                        total_cout_achat += tarif_four.cout_achat;
                        if (tarif_four.codec_remise_arriere) {
                            total_cout_achat -= tarif_four.valeur_remise_arriere_final_euro;
                        }

                        if (tarif_four.cout_achat > max_cout_achat) {
                            max_cout_achat = tarif_four.cout_achat;
                        }

                        if (tarif_four.cout_achat < min_cout_achat || min_cout_achat === 0) {
                            min_cout_achat = tarif_four.cout_achat;
                        }

                        if (tarif_four.id_fournisseur == $scope.article.info_generales.id_fournisseur_favori) {
                            fav_cout_achat = tarif_four.cout_achat;
                        }
                    }
                }

                if (nb_fourns_valides > 0) {
                    $scope.article.tarifs_achats.moyen_pa_net_euro = total_pa_net_euro / nb_fourns_valides;
                    $scope.article.tarifs_achats.moyen_frais_approche_mt_total = total_frais_approche_mt_total / nb_fourns_valides;
                    $scope.article.tarifs_achats.moyen_cout_achat = total_cout_achat / nb_fourns_valides;
                } else {
                    $scope.article.tarifs_achats.moyen_pa_net_euro = 0;
                    $scope.article.tarifs_achats.moyen_frais_approche_mt_total = 0;
                    $scope.article.tarifs_achats.moyen_cout_achat = 0;
                }

                if ($scope.article.tarifs_achats.config_valeur_achat === '1') {
                    // 1: cout le + bas
                    $scope.article.tarifs_achats.cout_achat = min_cout_achat;
                } else if ($scope.article.tarifs_achats.config_valeur_achat === '2') {
                    // 2: cout moyen
                    $scope.article.tarifs_achats.cout_achat = $scope.article.tarifs_achats.moyen_cout_achat;
                } else if ($scope.article.tarifs_achats.config_valeur_achat === '3') {
                    // 3: cout le + haut
                    $scope.article.tarifs_achats.cout_achat = max_cout_achat;
                } else if ($scope.article.tarifs_achats.config_valeur_achat === '4') {
                    // 4: cout du fournisseur favori
                    if (Number(fav_cout_achat) === 0) {
                        $scope.article.tarifs_achats.cout_achat = $scope.article.tarifs_achats.moyen_cout_achat;
                    } else {
                        $scope.article.tarifs_achats.cout_achat = fav_cout_achat;
                    }
                }

                if (Number($scope.article.tarifs_achats.cout_achat) === 0) {
                    $scope.article.tarifs_achats.cout_achat = $scope.article.tarifs_achats.cout_achat_initial;
                }
            }

            $scope.updateValeurStock();
        };

        $scope.updateValeurStock = function () {
            if ($scope.config.use_fournisseur != "0") {
                if (!$scope.article.tarifs_achats.cmp_inited) {
                    if ($scope.article.tarifs_achats.cout_achat != $scope.article.tarifs_achats.cout_achat_initial) {
                        $scope.article.tarifs_achats.cout_moyen_pondere = $scope.article.tarifs_achats.cout_achat;
                    } else {
                        $scope.article.tarifs_achats.cout_moyen_pondere = $scope.article.tarifs_achats.old_cmp;
                    }
                    $scope.article.tarifs_achats.dernier_cout_achat = $scope.article.tarifs_achats.cout_achat;
                }

                if ($scope.article.tarifs_achats.config_valeur_stock === '1') {
                    // 1: Coût moyen pondéré
                    $scope.article.tarifs_achats.vas = Number($scope.article.tarifs_achats.cout_moyen_pondere);
                } else if ($scope.article.tarifs_achats.config_valeur_stock === '2') {
                    // 2: Dernier coût d'achat
                    $scope.article.tarifs_achats.vas = Number($scope.article.tarifs_achats.dernier_cout_achat);
                } else if ($scope.article.tarifs_achats.config_valeur_stock === '3') {
                    // 3: Coût d'achat actuel
                    $scope.article.tarifs_achats.vas = Number($scope.article.tarifs_achats.cout_achat);
                }
            } else {
                $scope.article.tarifs_achats.vas = Number($scope.article.tarifs_achats.cout_achat);
                $scope.article.tarifs_achats.cout_moyen_pondere = Number($scope.article.tarifs_achats.cout_achat);
                $scope.article.tarifs_achats.dernier_cout_achat = Number($scope.article.tarifs_achats.cout_achat);
            }
        };

        $scope.$watch('article.tarifs_achats.vas', function (newVal, oldVal) {
            if (newVal !== oldVal) {
                $scope.article.tarifs_achats.valeur_achat = $scope.article.tarifs_achats.vas;
                $scope.$broadcast("majAllPv");
            }
        });

        $scope.$watch('article.info_generales.id_fournisseur_favori', function (newVal, oldVal) {
            if (newVal !== oldVal) {
                $scope.updateValeurAchat();
            }
        });

        function getLibsCategsArticle(categs) {
            var libs = [];

            var addLib = function (c) {
                if (c.presence_article) {
                    libs.push(c.lib);
                }
                if (c.children.length > 0) {
                    angular.forEach(c.children, addLib);
                }
            }
            angular.forEach(categs, addLib);

            return libs;
        }

        $scope.showCatalogueClient = function (id_catalogue) {
            if ($scope.article.catalogues_clients[id_catalogue].fav) {
                var fav = $scope.article.catalogues_clients[id_catalogue].fav[0];
            } else {
                var fav = -1;
            }

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(112600, "Catalogue client"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "600px", height: "550px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/pp_catalogue_client.phtml",
                        controller: "CatalogueClientController",
                        inputs: {
                            idCatalogue: id_catalogue,
                            articleCategories: $scope.article.catalogues_clients[id_catalogue].categories,
                            usedCategories: $scope.article.catalogues_clients[id_catalogue].libs,
                            categUnique: $scope.article.catalogues_clients[id_catalogue].categ_unique,
                            categPrincipale: fav
                        }
                    },
                    then: function (modal) {
                    },
                    onClose: function (result) {
                        $scope.article.catalogues_clients[id_catalogue].changed = ($scope.article.catalogues_clients[id_catalogue].changed || result.changed);
                        $scope.article.catalogues_clients[id_catalogue].categories = result.categories; // arbre des catégories avec les presence_article
                        $scope.article.catalogues_clients[id_catalogue].libs = result.usedCategories; // liste des catégories utilisées (pour affichage)
                        if (!$scope.article.catalogues_clients[id_catalogue].fav) {
                            $scope.article.catalogues_clients[id_catalogue].fav = [];
                        }
                        if ($scope.article.catalogues_clients[id_catalogue].fav.length) {
                            $scope.article.catalogues_clients[id_catalogue].fav[0].id_catalogue_categ = result.principale;
                        } else {
                            $scope.article.catalogues_clients[id_catalogue].fav.push({ 'id_catalogue_categ': result.principale });
                        }

                        refreshLibsCategsCatalogues();

                        if (result.categ_unique) {
                            $scope.article.catalogues_clients[id_catalogue].categ_unique = result.categ_unique;
                        }

                        if ($scope.article.catalogues_clients[id_catalogue].libs.length > 0) {
                            $scope.article.catalogues_clients[id_catalogue].use = true;
                        } else {
                            $scope.article.catalogues_clients[id_catalogue].use = false;
                        }

                        updateTVA(id_catalogue);
                    }
                }
            });

        };

        const updateTVA = function (id_catalogue) {
            const categ_unique = $scope.article.catalogues_clients[id_catalogue].categ_unique;
            angular.forEach($scope.article.catalogues_clients[id_catalogue].categories, function (categ) {
                if(categ.id_catalogue_categ == categ_unique) {
                    $scope.article.tarifs_ventes.id_tva = categ.art_categ.defaut_id_tva;
                }
            })
        }

        $scope.catalogueChanged = function (id_catalogue) {
            $scope.article.catalogues_clients[id_catalogue].changed = true;
            if ($scope.article.catalogues_clients[id_catalogue].use) {
                $scope.showCatalogueClient(id_catalogue);
            }
        };

        $scope.showRechercheArticle = function () {

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(112601, "Moteur d'articles"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "920px", height: "680px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/pp_recherche_article.phtml",
                        controller: "RechercheMiniController",
                        inputs: {}
                    },
                    then: function (modal) {
                    },
                    onClose: function (result) {
                    }
                }
            });

        };

        $scope.ajouterMarque = function () {

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: _e_html(112602, "Ajouter une marque"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "475px", height: "200px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:phtml/pp_live_create.phtml",
                        controller: "LiveCreateController",
                        inputs: {
                            titleLiveCreate: "",
                            labelLiveCreate: __(112603, "Nom de la marque à créer"),
                            fonctionLiveCreate: "createMarque"
                        }
                    },
                    then: function (modal) {
                    },
                    onClose: function (result) {
                        if (result.id) {
                            $scope.config.marques = result.lst;
                            $scope.article.info_generales.id_marque = result.id;
                        }
                    }
                }
            });

        };

        $scope.addLiaison = function (id_liaison_type) {

            let addedObjectsFlat = $scope.getArticlesTypeLiaison(id_liaison_type);

            LMBArticleService.newMultiselect({
                onClose: function (result) {
                    var liaison = $scope.article.liaisons.find(function (el) {
                        return el.id_liaison_type === id_liaison_type;
                    });

                    angular.forEach(result.articles, function (art) {
                        if (liaison.articles.findIndex(function (a) {
                            return a.ref_article == art.ref_article;
                        }) === -1) {
                            let new_article = { ref_article: art.ref_article, lib_article: art.lib_article };

                            // On rajoute les effets sur l'article
                            let effets = {};
                            angular.forEach(liaison.effets, (effet) => {
                                let ref_effet = effet.ref_art_liaison_effet_type;
                                let defaut_params = JSON.parse(effet.defaut_params);
                                if (ref_effet == "DocVenteOptionRemplacement") {
                                    effets[ref_effet] = { ratio: defaut_params.ratio };
                                } else {
                                    if (typeof defaut_params.qte == "string" && defaut_params.qte.includes("x")) {
                                        let qte = defaut_params.qte.substring(0, defaut_params.qte.length - 1);
                                        effets[ref_effet] = {
                                            qte: parseFloat(qte || 1),
                                            prop: true
                                        };
                                    } else {
                                        effets[ref_effet] = { qte: parseFloat(defaut_params.qte) };
                                    }
                                }
                            });

                            new_article.parsed_params = effets;
                            new_article.params = JSON.stringify(effets);
                            liaison.articles.push(new_article);
                        }
                    });
                    // On supprime les articles qui ont été déselectionnés
                    angular.forEach(result.deleted, function (ref_art) {
                        let options_filtered = liaison.articles.filter(function (art) {
                            return art.ref_article != ref_art;
                        });
                        liaison.articles = options_filtered;
                    })
                },
                addedObjects: addedObjectsFlat,
                searchPrimaryKey: 'ref_article'
            });

        };

        /* AR/ Permet de récupérer la liste des articles liés selon le type de liaison
         * Retourne un array contenant les ref_article des articles liés */
        $scope.getArticlesTypeLiaison = function (id_liaison_type) {
            var articlesLiaisons = _.find($scope.article.liaisons, function (liaison) {
                return liaison.id_liaison_type == id_liaison_type;
            });
            return articlesLiaisons ? _.pluck(articlesLiaisons.articles, 'ref_article') : [];
        }

        $scope.addComposant = function () {

            // On blackliste l'article et son parent
            let blacklist = [$scope.article.info_generales.id_article];
            if (typeof $scope.article.info_generales.id_article_parent != "undefined") {
                blacklist.push($scope.article.info_generales.id_article_parent);
            }

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(112605, "Moteur d'articles"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "920px", height: "680px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/pp_recherche_article_multi_select.phtml",
                        controller: "RechercheMultiArticleController",
                        inputs: {
                            SearchParams: { parents: 0, blacklist: blacklist },
                            ArticlesLies: $scope.getListeComposants()
                        }
                    },
                    then: function (modal) {
                        var modal_element = modal.element[0];
                        var input = modal_element.querySelector("#lib_article");
                        setTimeout(function () {
                            input.focus();
                        }, 100);
                    },
                    onClose: function (result) {
                        if (!result.selected) {
                            return false;
                        }

                        if (result.articles_deleted) {
                            result.articles_deleted.forEach(function (art) {
                                var i = $scope.getListeComposants().indexOf(art);
                                if (i !== -1) {
                                    $scope.article.composants.splice(i, 1);
                                }
                            });
                        }
                        for (var i = 0; i < result.articles.length; i++) {
                            var article = {};
                            article.ref_article_composant = result.articles[i].ref_article;
                            article.lib_article = result.articles[i].lib_article;
                            article.qte = 1;
                            if (!_.contains($scope.getListeComposants(), article.ref_article_composant)) {
                                $scope.article.composants.push(article);
                            }
                        }
                    }
                }
            });

        };

        $scope.getListeComposants = function () {
            return _.pluck($scope.article.composants, 'ref_article_composant');
        };

        $scope.rmComposant = function (composant) {
            var index = $scope.article.composants.indexOf(composant);
            $scope.article.composants.splice(index, 1);

            return true;
        };

        /*
         * COMPOSITIONS AVANCEES
         */
        $scope.addSection = function () {

            LMBTools.confirm({
                title: __(181738, "Ajouter une section"),
                content: "<table class='style-1'>" +
                    "<tr>\
                        <td>"+ __(181375, "Libellé") + "</td>\
                        <td><input type='text' class='input-full' name='lib'>\
                    </td></tr>" +
                    "</table>",
                confirm: __(112341, "Ajouter"),
                onValid: function (confirm, params) {
                    if (confirm) {
                        if (params.lib) {
                            $scope.article.nomenclatures.sections.push({
                                'lib': params.lib,
                                'composants': []
                            });
                        } else {
                            LMBToast.error({
                                'title': "Pas de libellé renseigné",
                                'message': "Veuillez renseigner un libellé pour la section"
                            });
                        }
                    }
                }
            });

        }

        $scope.removeSection = function (section, section_index) {
            if (section.composants && section.composants.length) {
                LMBTools.confirm({
                    content: "Souhaitez-vous vraiment supprimer cette section ?",
                    confirm: __(121999, "Oui"),
                    onValid: function (confirm) {
                        if (confirm) {
                            $scope.article.nomenclatures.sections.splice(section_index, 1);
                        }
                    }
                });
            } else {
                $scope.article.nomenclatures.sections.splice(section_index, 1);
            }
        }

        $scope.addComposantAvance = function (section) {

            let selected_articles = [];
            angular.forEach(section.composants, (composant) => {
                if (typeof composant.id_article != "undefined") {
                    selected_articles.push(composant.id_article);
                }
            });

            LMBArticleService.newMultiselect({
                onClose: function (result) {
                    if (result && result.articles && result.articles.length > 0) {
                        angular.forEach(result.articles, function (article, key) {
                            //var id = parseInt(article.id_article);

                            let exist = _.find(section.composants, function (composant) {
                                return composant.id_article == article.id_article;
                            });

                            if (!exist) {
                                section.composants.push({
                                    'id_categ': null,
                                    'id_article': article.id_article,
                                    'ref_article': article.ref_article,
                                    'lib': article.lib_article,
                                    'supplement': 0
                                });
                            }
                        });

                    }
                },
                addedObjects: selected_articles,
                searchPrimaryKey: 'id_article',
                criterias: {
                    lot: ['0', '5', '6'],
                    stockable: 0
                }
            }, [$scope.article.info_generales.id_article]);
        }

        $scope.removeComposantAvance = function (section, composant_index) {
            section.composants.splice(composant_index, 1);
        }

        $scope.addCategorie = function (section) {

            let selected_categs = [];
            angular.forEach(section.composants, (composant) => {
                if (typeof composant.ref_art_categ != "undefined") {
                    selected_categs.push(composant.ref_art_categ);
                }
            })
            LMBComposants.selectArtCateg({
                onClose: function (result) {
                    angular.forEach(result, (ref_categ) => {
                        let categ = $scope.config.art_categs.find((cat) => {
                            return cat.id == ref_categ;
                        });
                        if (categ) {
                            // On copie l'objet pour ne pas modifier celui dans $scope.config
                            let categ_obj = angular.copy(categ);
                            // On vérifie que la categ ne soit pas déjà dans la liste des composants
                            let already_there = section.composants.find((cat) => {
                                return cat.ref_art_categ == ref_categ;
                            });
                            if (already_there == undefined) {
                                categ_obj.ref_art_categ = categ_obj.id;
                                categ_obj.supplement = 0;
                                delete categ_obj.id;
                                section.composants.push(categ_obj);
                            }
                        }
                    });
                },
                selected: selected_categs
            });
        }

        $scope.changeSupplementCateg = function (ref_art_categ, lib_art_categ) {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: "Plus-values de la catégorie",
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "800px", height: "680px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/pp_plus_values_categ.phtml",
                        controller: "PopupPlusValuesCategCtrl",
                        controllerAs: "vm",
                        inputs: {
                            ref_art_categ: ref_art_categ,
                            lib_art_categ: lib_art_categ,
                            plus_values: $scope.article.nomenclatures.plus_values
                        }
                    },
                    then: function (modal) {
                    },
                    onClose: function (result) {
                        if (result && typeof result.plus_values != "undefined") {
                            $scope.article.nomenclatures.plus_values = angular.extend($scope.article.nomenclatures.plus_values || {}, result.plus_values);
                        }
                    }
                }
            });
        }

        /* RECETTES */
        $scope.addArticleRecette = function () {

            let selected_articles = [];
            if (!$scope.article.recette) {
                $scope.article.recette = {};
            }
            angular.forEach($scope.article.recette.liste_articles, (composant) => {
                if (typeof composant.ref_article != "undefined") {
                    selected_articles.push(composant.ref_article);
                }
            });

            LMBArticleService.newMultiselect({
                onClose: function (result) {
                    if (result && result.articles && result.articles.length > 0) {

                        angular.forEach(result.articles, function (article, key) {
                            //var id = parseInt(article.id_article);

                            let exist = _.find($scope.article.recette.liste_articles, function (composant) {
                                return composant.ref_article == article.ref_article;
                            });

                            if (!exist) {
                                $scope.article.recette.liste_articles.push({
                                    'id_categ': null,
                                    'ref_article': article.ref_article,
                                    'lib': article.lib_article,
                                    'qte': 1,
                                    'valorisation': article.valorisation,
                                    'pa_unitaire_ht': article.prix_achat_ht || 0,
                                    'pa_total_ht': article.prix_achat_ht || 0,
                                    'pourcent_pa_total': 0,
                                    'pourcent_qte_totale': 0,
                                    'avec': false,
                                    'sans': false
                                });
                                $scope.updateTotalRecette();
                            }
                        });

                    }
                },
                addedObjects: selected_articles,
                searchPrimaryKey: 'ref_article',
                criterias: {
                    lot: ['0', '5', '6'],
                    stockable: true
                }
            }, [$scope.article.info_generales.id_article]);
        }

        $scope.removeArticleRecette = function (index) {
            $scope.article.recette.liste_articles.splice(index, 1);
        }

        /**
         * Lorsqu'on modifie la quantité d'un composant, on met à jour son prix total
         */
        $scope.updatePrixAchatTotalComposant = function (composant) {
            if (composant.pa_unitaire_ht && !isNaN(parseFloat(composant.pa_unitaire_ht))) {
                composant.pa_total_ht = parseFloat(composant.qte) * parseFloat(composant.pa_unitaire_ht);
            } else {
                composant.pa_unitaire_ht = 0;
                composant.pa_total_ht = 0;
            }

            $scope.updateTotalRecette();

        }


        $scope.updateTotalRecette = function () {
            // On met à jour la quantité totale et le prix total de la recette
            let qte_totale_recette = 0;
            let prix_unitaire_total_recette = 0;
            let prix_total_recette = 0;

            angular.forEach($scope.article.recette.liste_articles, function (comp) {
                qte_totale_recette += parseFloat(comp.qte);
                prix_unitaire_total_recette += parseFloat(comp.pa_unitaire_ht);
                prix_total_recette += parseFloat(comp.pa_total_ht);

            });

            $scope.article.recette.qte_totale = qte_totale_recette;
            $scope.article.recette.prix_unitaire_total = prix_unitaire_total_recette;
            $scope.article.recette.prix_total = prix_total_recette;

            angular.forEach($scope.article.recette.liste_articles, function (comp) {
                // On met à jour les pourcentages des composants
                let pourcent_pa_total = comp.pa_total_ht / $scope.article.recette.prix_total || 0;
                comp.pourcent_pa_total = parseFloat(pourcent_pa_total * 100).toFixed(2);

                let pourcent_qte_totale = comp.qte / $scope.article.recette.qte_totale;
                comp.pourcent_qte_totale = parseFloat(pourcent_qte_totale * 100).toFixed(2);
            });
        }


        /*
         * Convertit une valeur vers l’unité « de base » en utilisant un codec de conversion.
         * @param val La valeur à convertir
         * @param unit_codec Le codec de conversion (ex. : [VALUE]/1000)
         * @param pow (facultatif) puissance à utiliser : dans le cas d’un volume, il faut mettre le facteur à la puissance 3
         * @param reverse (facultatif) true = convertir dans l’autre sens (de l’unité « de base » vers l’unité passée en paramètre)
         *
         *   poids -> kg
         *   longueur -> cm
         */
        function conversionUnite(val, unit_codec, pow, reverse) {
            if (unit_codec.trim() != "[VALUE]") {
                var facteur = parseFloat(unit_codec.replace(/\[VALUE\][\/*]?/, "")); // efface [VALUE], [VALUE]/ ou [VALUE]*
                if (typeof pow === "number") {
                    facteur = Math.pow(facteur, pow);
                }
                if (unit_codec.indexOf("*") > 0) {
                    facteur = 1 / facteur;
                }
                if (reverse === true) {
                    facteur = 1 / facteur;
                }
                return val * facteur;
            } else {
                return val;
            }
        }

        function convertirVolume(val, unit_codec) {
            let newVolume = val;
            if (unit_codec == "cm") {
                newVolume = val / 1000000;
            } else if (unit_codec == "mm") {
                newVolume = val / 1000000000;
            }
            return newVolume;
        }
        $scope.recalc_colisage = function () {
            $scope.article.colisage.poids_total_colis = 0;
            $scope.article.colisage.volume_total_colis_cm3 = 0;
            $scope.article.colisage.volume_total_colis = 0;
            $scope.article.colisage.nb_colis = 0;
            if ($scope.article.colisage.colis.length > 0) {
                $.each($scope.article.colisage.colis, function (k, colis) {
                    if (colis.dimensions && colis.dimensions.hauteur && colis.dimensions.largeur && colis.dimensions.profondeur) {
                        colis.volume = colis.dimensions.hauteur * colis.dimensions.largeur * colis.dimensions.profondeur;
                        colis.unit_volume = $scope.config.units.longueur[colis.id_dim].abrev;
                        colis.unit_volume = colis.unit_volume === "ml" ? "m" : colis.unit_volume;
                        colis.volume_ml = convertirVolume(colis.volume, colis.unit_volume);
                        colis.dim = colis.dimensions.hauteur + "x" + colis.dimensions.largeur + "x" + colis.dimensions.profondeur;

                        $scope.article.colisage.volume_total_colis += colis.volume_ml;
                    }
                    colis.idDim = colis.id_dim;
                    colis.idPoids = colis.id_poids;

                    if (colis.poids) {
                        $scope.article.colisage.poids_total_colis += parseFloat(conversionUnite(colis.poids, $scope.config.units.poids[colis.id_poids].codec_conversion));
                    } else {
                        colis.poids = 0;
                    }
                    if (colis.dimensions || colis.poids) {
                        $scope.article.colisage.nb_colis += 1;
                    }
                });
                $scope.article.colisage.unit_volume_total = "m";
            }
        };

        $scope.getDimensionDecimalFormat = function (idDimension) {
            let selectedUnit = $scope.config.units.longueur[idDimension];
            return ["metre", "centimetre"].includes(selectedUnit?.ref) ? "dec: 0" : "";
        }

        $scope.reformatDimensions = function (colis, idx) {
            let decimalValue = $scope.getDimensionDecimalFormat(colis.id_dim);
            $(`#dimensions${idx}`).find("input[type='text']").each((i, el) => {
                if ($(el).hasClass("lmbNumberConverted")) {
                    el.remove();
                } else {
                    el.setAttribute("lmb-number", decimalValue);
                    window.formattedNumbersConfig.formattedNumbers(el);
                }
            });
            $scope.recalc_colisage();
        }

        $scope.rmColis = function (colis) {
            var i = $scope.article.colisage.colis.indexOf(colis);
            $scope.article.colisage.colis.splice(i, 1);
            $scope.recalc_colisage();
        }

        $scope.ajouterColis = function () {
            var colisVide = {};

            if ($scope.article.colisage.colis.length > 0) {
                colisVide.id_poids = $scope.article.colisage.colis[$scope.article.colisage.colis.length - 1].id_poids;
                colisVide.id_dim = $scope.article.colisage.colis[$scope.article.colisage.colis.length - 1].id_dim;
            } else {
                colisVide.id_poids = parseInt(Object.keys($scope.config.units.poids)[0]);
                colisVide.id_dim = parseInt(Object.keys($scope.config.units.longueur)[1]);
            }
            $scope.article.colisage.colis.push(colisVide);
            $scope.recalc_colisage();
        }

        $scope.getTauxDevise = function (id) {
            if ($scope.config.devise[id] != undefined) {
                return $scope.config.devise[id].taux_change;
            } else {
                return 1;
            }
        };


        $scope.addRulesLiaisonArticle = function (id_liaison_type, article) {
            var tmp_liaison_type = $scope.article.liaisons.filter(function (el) {
                return el.id_liaison_type === id_liaison_type;
            });
            tmp_liaison_type = tmp_liaison_type[0];
            var qteArticle = 1;
            var qteArticleLinked = 1;
            if (article.rules !== null && article.rules !== undefined && article.rules !== "") {
                if (id_liaison_type.includes("is")) {
                    qteArticle = article.rules.substring(article.rules.indexOf("(") + 1, article.rules.indexOf(","));
                    qteArticleLinked = article.rules.substring(article.rules.indexOf(",") + 1, article.rules.indexOf(")"));
                } else if (id_liaison_type.includes("has")) {
                    qteArticleLinked = article.rules.substring(article.rules.indexOf("(") + 1, article.rules.indexOf(","));
                    qteArticle = article.rules.substring(article.rules.indexOf(",") + 1, article.rules.indexOf(")"));
                }
            }

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(120935, "Options de Liaison"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "600px", height: "680px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/pp_option_liaison_form.phtml",
                        controller: "OptionsLiaisonCtrl",
                        inputs: {
                            article: $scope.article,
                            articleLinked: article,
                            qteArticle: qteArticle,
                            qteArticleLinked: qteArticleLinked,
                            id_liaison_type: id_liaison_type
                        }
                    },
                    then: function (modal) {
                    },
                    onClose: function (result) {
                        $scope.article.liaisons_modif = true;
                    }
                }
            });
        };

        $scope.rmLiaisonArticle = function (id_liaison_type, ref_article) {
            $scope.article.liaisons_modif = true;

            var tmp_liaison_type = $scope.article.liaisons.filter(function (el) {
                return el.id_liaison_type === id_liaison_type;
            });

            tmp_liaison_type = tmp_liaison_type[0];

            var tmp_articles = tmp_liaison_type.articles.filter(function (el) {
                return el.ref_article !== ref_article;
            });

            if (tmp_articles.length === 0) {
                $scope.rmLiaisonTypeArticle(id_liaison_type);
            } else {
                tmp_liaison_type.articles = tmp_articles;
            }
        };


        $scope.rmLiaisonTypeArticle = function (id_liaison_type) {
            $scope.article.liaisons_modif = true;

            var tmp = $scope.article.liaisons.filter(function (el) {
                return el.id_liaison_type !== id_liaison_type;
            });

            $scope.article.liaisons = tmp;
        };

        $scope.addLiaisonTypeArticle = function (id_liaison_type) {
            $scope.article.liaisons_modif = true;

            var tmp = $scope.article.liaisons.filter(function (el) {
                return el.id_liaison_type === id_liaison_type;
            });

            if (tmp.length > 0) {
                return false;
            }

            var newLiaison = {};
            newLiaison.articles = [];
            newLiaison.id_liaison_type = id_liaison_type;
            newLiaison.lib_liaison_type = $scope.config.liaisons_types[id_liaison_type].lib;
            newLiaison.effets = $scope.config.liaisons_types[id_liaison_type].effets;
            // @TODO DELETE
            // angular.forEach(newLiaison.effets, (effet) => {
            //     $scope.parseParamsEffet(effet);
            // });

            $scope.article.liaisons.push(newLiaison);
        };

        // AR
        // Ajout des fonctions permettant d'enléver des options du select si elles sont déjà sélectionnées
        $scope.liaisons_actives = [];

        $scope.activerLiaisons = function () {
            $scope.article.liaisons.forEach(function (liaison_type) {
                $scope.liaisons_actives.push(liaison_type.id_liaison_type);
            });
        }

        $scope.checkLiaison = function (liaison) {
            return $scope.liaisons_actives.indexOf(liaison) != -1;
        }

        $scope.activerTypeLiaison = function (liaison_type) {
            $scope.liaisons_actives.push(liaison_type);
            $scope.addLiaisonTypeArticle(liaison_type);
        }

        $scope.desactiverTypeLiaison = function (liaison_type) {
            var tmp_liaisions_actives = $scope.liaisons_actives.filter(function (el) {
                return el != liaison_type;
            })
            $scope.liaisons_actives = tmp_liaisions_actives;
            $scope.rmLiaisonTypeArticle(liaison_type);
        }

        // @TODO MAKE WORK
        $scope.displayQuantiteEffet = function (effet) {
            let defaut_params = JSON.parse(effet.defaut_params);
            if (effet.ref_art_liaison_effet_type == "DocVenteOptionRemplacement") {
                let from = defaut_params.ratio.from;
                let txt_present = defaut_params.ratio.from >= 2 ? __(181360, "présents") : __(650282, "présent");
                let to = defaut_params.ratio.to;
                let txt_article = defaut_params.ratio.to >= 2 ? __(510781, "articles") : __(100491, "article");
                let txt_pour = __(610073, "pour");
                return `${to} ${txt_article} ${txt_pour} ${from} ${txt_present}`
            } else {
                if (defaut_params.qte.includes("x")) {
                    let qte = defaut_params.qte.substring(0, defaut_params.qte.length - 1);
                    let txt = __(650281, "fois la quantité initiale");
                    return `${qte} ${txt}`;
                } else {
                    let txt_article = defaut_params.qte >= 2 ? __(510781, "articles") : __(100491, "article");
                    return `${defaut_params.qte} ${txt_article}`;
                }
            }
        }

        $scope.getLibEffetLiaison = function (liaison, ref_effet) {
            let effet = liaison.effets.find((eff) => {
                return eff.ref_art_liaison_effet_type == ref_effet;
            });
            if (effet) {
                return effet.lib;
            }
            return "Nom inconnu";
        }

        // @TODO DELETE
        $scope.parseParamsEffet = function (effet) {
            let defaut_params = JSON.parse(effet.defaut_params);
            let parsed_params = {};
            if (effet.ref_art_liaison_effet_type == "DocVenteOptionRemplacement") {
                let from = defaut_params.ratio.from;
                let to = defaut_params.ratio.to;
                parsed_params.ratio = { from, to };
            } else {
                if (defaut_params.qte.includes("x")) {
                    let qte = parseFloat(defaut_params.qte.substring(0, defaut_params.qte.length - 1) || 1);
                    parsed_params.qte = qte;
                    parsed_params.prop = true;
                } else {
                    parsed_params.qte = parseFloat(defaut_params.qte);
                }
            }
            effet.parsed_params = parsed_params;
        }

        $scope.parseParamsLiaisonArticle = function (liaison_article) {
            let parsed_params = JSON.parse(liaison_article.params);
            for (let key in parsed_params) {
                let effet = parsed_params[key];
                if (key != "DocVenteOptionRemplacement") {
                    if (typeof effet.qte == "string" && effet.qte.includes("x")) {
                        let qte = parseFloat(effet.qte.substring(0, effet.qte.length - 1) || 1);
                        effet.qte = qte;
                        effet.prop = true;
                    } else {
                        effet.qte = parseFloat(effet.qte);
                    }
                }
            }
            liaison_article.parsed_params = parsed_params;
        }


        // AR
        $scope.toggleCaracSimple = function (carac) {
            if (carac.selected) {
                $scope.addCaracSimple(carac.id_carac);
            } else {
                $scope.rmCaracSimple(carac.id_carac);
            }
        }

        $scope.toggleCaracPersonnalisation = function (carac) {
            if (carac.selected) {
                $scope.addCaracPersonnalisation(carac.id_carac);
            } else {
                $scope.rmCaracPersonnalisation(carac.id_carac);
            }
        }
        $scope.toggleCaracDeclinaison = function (carac) {
            if (carac.selected) {
                $scope.addCaracDeclinaison(carac.id_carac);
            } else {
                $scope.rmCaracDeclinaison(carac.id_carac);
            }
        }

        $scope.addCaracSimple = function (id_carac) {

            if (id_carac === '0') {
                return false;
            }

            $scope.select_carac_simple = '0';

            var tmp = $scope.article.caracs.filter(function (el) {
                return el.id_carac === id_carac;
            });
            if (tmp.length > 0) {
                return false;
            }

            if ($scope.article.info_generales.ref_article === '') {
                // création d'article
                var tmp2 = $scope.article.declinaisons.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.declinaisons = tmp2;
                var tmp3 = $scope.article.personnalisations.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.personnalisations = tmp3;
            } else {
                // mise à jour d'article: on bloque la modification des déclinaisons
                var tmp2 = $scope.article.declinaisons.filter(function (el) {
                    return el.id_carac === id_carac;
                });
                if (tmp2.length > 0) {
                    return false;
                }
                var tmp3 = $scope.article.personnalisations.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.personnalisations = tmp3;
            }

            var config = $scope.config.caracs.all.filter(function (el) {
                return el.id_carac === id_carac;
            });

            var newCarac = config[0];
            newCarac.valeur = newCarac.defaut_value;
            $scope.article.caracs.push(newCarac);
        };

        $scope.addCaracPersonnalisation = function (id_carac) {
            if (id_carac === '0') {
                return false;
            }

            $scope.select_carac_personnalisation = '0';

            var tmp = $scope.article.personnalisations.filter(function (el) {
                return el.id_carac === id_carac;
            });
            if (tmp.length > 0) {
                return false;
            }

            if ($scope.article.info_generales.ref_article === '') {
                // création d'article
                var tmp2 = $scope.article.declinaisons.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.declinaisons = tmp2;
                var tmp3 = $scope.article.caracs.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.caracs = tmp3;
            } else {
                // mise à jour d'article: on bloque la modification des déclinaisons
                var tmp2 = $scope.article.declinaisons.filter(function (el) {
                    return el.id_carac === id_carac;
                });
                if (tmp2.length > 0) {
                    return false;
                }
                var tmp3 = $scope.article.caracs.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.caracs = tmp3;
            }

            var config = $scope.config.caracs.all.filter(function (el) {
                return el.id_carac === id_carac;
            });

            var newCarac = config[0];
            /*newCarac.valeur = [];
             newCarac.options = [];
             angular.forEach(newCarac.defaut_value.split(";"), function(val) {
             newCarac.valeur.push(val);
             newCarac.options.push({text: val, value: val});
             });*/
            newCarac.valeur = newCarac.defaut_value;
            $scope.article.personnalisations.push(newCarac);
        };

        $scope.addCaracDeclinaison = function (id_carac) {
            if (id_carac === '0') {
                return false;
            }

            $scope.select_carac_declinaison = '0';

            var tmp = $scope.article.declinaisons.filter(function (el) {
                return el.id_carac === id_carac;
            });
            if (tmp.length > 0) {
                return false;
            }

            if ($scope.article.info_generales.ref_article === '') {
                // création d'article
                var tmp2 = $scope.article.caracs.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.caracs = tmp2;
                var tmp3 = $scope.article.personnalisations.filter(function (el) {
                    return el.id_carac !== id_carac;
                });
                $scope.article.personnalisations = tmp3;
            }

            var config = $scope.config.caracs.all.filter(function (el) {
                return el.id_carac === id_carac;
            });

            var newCarac = config[0];
            newCarac.valeur = [];
            newCarac.options = [];
            // Pour les caracs qui ne sont pas de type sélection on récupère la valeur par défaut
            if (['selection_simple', 'selection_multiple'].indexOf(newCarac.ref_carac_type) === -1) {
                angular.forEach(newCarac.defaut_value.split(";"), function (val) {
                    newCarac.valeur.push(val);
                    newCarac.options.push({ text: val, value: val });
                });
            }
            // Pour les caracs de type sélection, on récupère les valeurs disponibles
            else {
                angular.forEach(newCarac.parametres_type.valeurs, (val) => {
                    newCarac.valeur.push(val[0]);
                    newCarac.options.push({ text: val[1], value: val[0] });
                });
            }

            $scope.article.declinaisons.push(newCarac);

            // We reset the initial stock value
            if ($scope.article.declinaisons.length > 0) {
                angular.forEach($scope.article.stocks, function (stock) {
                    stock.init = 0;
                });
            }
        };

        $scope.rmCaracSimple = function (id_carac) {
            var tmp = $scope.article.caracs.filter(function (el) {
                return el.id_carac !== id_carac;
            });

            $scope.article.caracs = tmp;
        };

        $scope.rmCaracDeclinaison = function (id_carac) {
            var tmp = $scope.article.declinaisons.filter(function (el) {
                return el.id_carac !== id_carac;
            });

            $scope.article.declinaisons = tmp;
        };

        $scope.rmCaracPersonnalisation = function (id_carac) {
            var tmp = $scope.article.personnalisations.filter(function (el) {
                return el.id_carac !== id_carac;
            });

            $scope.article.personnalisations = tmp;
        };

        $scope.toggleCaracs = function () {
            if ($scope.config.caracs && $scope.config.caracs.simple) {
                angular.forEach($scope.config.caracs.simple, function (carac) {
                    if (carac.selected) {
                        carac.selected = false;
                        $scope.rmCaracSimple(carac.id_carac);
                    }
                });
            }
        };

        /**
         * Get the list of available distribution fee (i.e those not selected yet) for current supplier and for selling
         * @param {object} tarif_grille
         * @param {number} id_frais_type
         * @returns {Array}
         */
        $scope.getSellingDistributionFeeList = function (tarif_grille, id_frais_type) {
            var findItem,
                list = [];

            _.each($scope.config.distribution_frais, function (item) {
                findItem = _.findWhere(tarif_grille.frais, { id_frais_type: item.id_frais_type });
                if (!findItem || (id_frais_type && findItem && findItem.id_frais_type == id_frais_type)) {
                    list.push(item);
                }
            });

            return list;
        };

        /**
         * Add a distribution fee to the price list of current supplier and for selling
         * @param {object} tarif_grille
         */
        $scope.addSellingDistributionFee = function (tarif_grille) {
            var feeList = $scope.getSellingDistributionFeeList(tarif_grille);
            if (feeList.length) {
                var distributionFeeModel = feeList[0];
                tarif_grille.frais.push({
                    id_article_frais: 0,
                    formule_calcul: '',
                    id_devise: $scope.config.devise.default.id_devise,
                    taux_change: 1,
                    id_frais_type: distributionFeeModel.id_frais_type,
                    id_fournisseur: 0,
                    lib: distributionFeeModel.lib,
                    valeur: 0,
                    valeur_final_euro: 0,
                    operateur: 'valeur'
                });
            }
        };

        $scope.alertes = {};

        $scope.sortableOptionsCaracs = {
            axis: 'y',
            handle: '.sortable-icon',
            update: function (e, ui) {
                if (ui.item.sortable.dropindex < ui.item.sortable.index) {
                    angular.forEach($scope.article.caracs, function (carac) {
                        if (parseInt(carac.ordre) >= ui.item.sortable.dropindex && parseInt(carac.ordre) < ui.item.sortable.index) {
                            carac.ordre = (parseInt(carac.ordre) + 1).toString();
                        }
                    });
                } else {
                    angular.forEach($scope.article.caracs, function (carac) {
                        if (parseInt(carac.ordre) <= ui.item.sortable.dropindex && parseInt(carac.ordre) > ui.item.sortable.index) {
                            carac.ordre = (parseInt(carac.ordre) - 1).toString();
                        }
                    });
                }
                ui.item.sortable.model.ordre = ui.item.sortable.dropindex.toString();
            }
        };

        $scope.sortableOptionsPers = {
            axis: 'y',
            handle: '.sortable-icon',
            update: function (e, ui) {
                if (ui.item.sortable.dropindex < ui.item.sortable.index) {
                    angular.forEach($scope.article.personnalisations, function (carac) {
                        if (parseInt(carac.ordre) >= ui.item.sortable.dropindex && parseInt(carac.ordre) < ui.item.sortable.index) {
                            carac.ordre = (parseInt(carac.ordre) + 1).toString();
                        }
                    });
                } else {
                    angular.forEach($scope.article.personnalisations, function (carac) {
                        if (parseInt(carac.ordre) <= ui.item.sortable.dropindex && parseInt(carac.ordre) > ui.item.sortable.index) {
                            carac.ordre = (parseInt(carac.ordre) - 1).toString();
                        }
                    });
                }
                ui.item.sortable.model.ordre = ui.item.sortable.dropindex.toString();
            }
        };

        $scope.sortableOptionsDecli = {
            axis: 'y',
            handle: '.sortable-icon',
            update: function (e, ui) {
                if (ui.item.sortable.dropindex < ui.item.sortable.index) {
                    angular.forEach($scope.article.declinaisons, function (carac) {
                        if (parseInt(carac.ordre) >= ui.item.sortable.dropindex && parseInt(carac.ordre) < ui.item.sortable.index) {
                            carac.ordre = (parseInt(carac.ordre) + 1).toString();
                        }
                    });
                } else {
                    angular.forEach($scope.article.declinaisons, function (carac) {
                        if (parseInt(carac.ordre) <= ui.item.sortable.dropindex && parseInt(carac.ordre) > ui.item.sortable.index) {
                            carac.ordre = (parseInt(carac.ordre) - 1).toString();
                        }
                    });
                }
                ui.item.sortable.model.ordre = ui.item.sortable.dropindex.toString();
            }
        };

        $scope.testMinValueReengagement = function () {
            if ($scope.article.infos_abos.duree > $scope.article.infos_abos.duree_reengagement) {
                $scope.article.infos_abos.duree_reengagement = $scope.article.infos_abos.duree;
            }
        };
    }]);

    module.controller('ImagesCtrl', ['$rootScope', '$scope', 'FileUploader', 'LMBAjaxService', '$timeout', function ($rootScope, $scope, FileUploader, LMBAjaxService, $timeout) {
        $scope.uploading = false;
        $scope.directUpload = false;

        $scope.sortableOptions = {
            axis: 'y',
            handle: '.button-sortable',
            cursor: 'move',
            cancel: '',
            helper: function (event, ui) {
                ui.children().each(function () {
                    jQuery(this).width(jQuery(this).width());
                });
                return ui;
            },
            update: function (e, ui) {
                $timeout(function () {
                    var ordres = [];
                    angular.forEach($scope.article.images.list, function (image, key) {
                        var img = { id_image: image.id_image, ordre: $scope.article.images.list.indexOf(image) };
                        ordres.push(img);
                    });

                    LMBAjaxService.post('page.php/Article/Standard/Standard/updateOrdresImages', {
                        ref_article: $scope.article.info_generales.ref_article,
                        ordres: ordres
                    });

                }, 500);
            }
        };

        var uploader = $scope.uploader = new FileUploader({
            url: 'upload.php'
        });

        uploader.filters.push({
            name: 'imageFilter',
            fn: function (item, options) {
                var type = '|' + item.type.slice(item.type.lastIndexOf('/') + 1) + '|';
                return '|jpg|png|jpeg|bmp|gif|webp|svg|avif|'.indexOf(type) !== -1;
            }
        });

        $scope.init = function (directUpload) {
            $scope.directUpload = directUpload;
        };

        $scope.uploadURL = function () {
            if (!$scope.urlImageUpload) {
                let content = __(521938, "Impossible d'importer l'image depuis l'URL renseignée. Vous devez renseigner le champ avec une URL d'image valide.");
                LMBTools.alert({
                    content: content
                });
                $scope.uploading = false;
                return;
            }
            $scope.uploading = true;
            var url = 'page.php' + '/Components/Standard/Datas/getFileFromUrl';
            LMBAjaxService.post(url, { url: $scope.urlImageUpload }, false, {
                transformRequest: angular.identity,
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                responseType: 'blob'
            }).then(function success(response) {
                var blob = response.data;
                var file = new File([blob], "uploadurl_" + uploader.queue.length + "." + blob.type.split("/")[1], {
                    lastModified: new Date(0),
                    type: blob.type
                });
                uploader.addToQueue(file, false, false);

                if ($scope.directUpload) {
                    $scope.article.images.push(uploader.queue);
                } else {
                    $scope.article.new_images = uploader.queue;
                }
            }, function failure(response) {
                $scope.uploading = false;
                LMBTools.alert({
                    title: __(112606, "Erreur"),
                    content: __(112607, "L’URL indiquée n’a pas pu être téléchargée :<br />")
                        + __(112608, "Erreur ") + response.status + " (" + response.statusText + ")"
                });
            });
        };

        uploader.onAfterAddingAll = function (addedFileItems) {
            $scope.article.images.queue = this.queue;
            $scope.uploading = false;
        };

        uploader.removeFromQueue = function (value) {
            var index = this.getIndexOfItem(value);
            var item = this.queue[index];
            if (item.isUploading)
                item.cancel();
            this.queue.splice(index, 1);
            item._destroy();
            this.progress = this._getTotalProgress();
            $scope.article.images.queue = this.queue;
        };

        $scope.rmImage = function (image) {
            LMBTools.confirm({
                content: __(112609, "Êtes-vous sûr de vouloir supprimer cette image ?"),
                confirm: __(112610, "Supprimer"),
                onValid: function (confirm) {
                    if (confirm) {

                        if ($scope.directUpload) {

                            LMBAjaxService.post('page.php/Article/Standard/Standard/delImage', {
                                ref_article: $scope.article.info_generales.ref_article,
                                id_image: image.id_image
                            });
                        }

                        var index = $scope.article.images.list.indexOf(image);
                        $scope.article.images.list.splice(index, 1);

                        $rootScope.safeApply();
                    }
                }
            });
        };

        $scope.addImageUrl = function (url) {
            var elm = $j('.jFiler-input-icon i');
            var icon = elm.attr('class');
            elm.attr('class', 'spinner');

            LMBAjaxService.post('page.php/Article/Standard/Standard/saveImageUrl', {
                ref_article: $scope.article.info_generales.ref_article,
                url: url
            })
                .then(function success(response) {

                    $scope.refreshImages();
                    elm.attr('class', icon);
                    $j('#jfiler-add-url').val('');

                }, function failure(response) {
                    $scope.uploading = false;
                    LMBTools.alert({
                        title: __(112611, "Erreur"),
                        content: __(112613, "L’URL indiquée n’a pas pu être téléchargée :<br />")
                            + __(112612, "Erreur ") + response.status + " (" + response.statusText + ")"
                    });
                });
        };

        $scope.refreshImages = function () {
            LMBAjaxService.post('page.php/Article/Standard/Standard/refreshImages', { ref_article: $scope.article.info_generales.ref_article })
                .then(function success(response) {
                    $scope.article.images = response.data.datas;
                });
        };

        $scope.updateTitre = function (image) {
            LMBAjaxService.post('page.php/Article/Standard/Standard/updateTitreImage', {
                id_image: image.id_image,
                titre: image.titre
            }).then(function (res) {
                if (res.error) {
                    LMBToast.error({
                        title: 'Image',
                        message: 'Une erreur est survenue lors de la mise à jour du titre de l\'image.'
                    });
                }
            });
        };

        $scope.updateDescription = function (image) {
            LMBAjaxService.post('page.php/Article/Standard/Standard/updateDescriptionImage', {
                id_image: image.id_image,
                description: image.description
            }).then(function (res) {
                if (res.error) {
                    LMBToast.error({
                        title: 'Image',
                        message: 'Une erreur est survenue lors de la mise à jour de la description de l\'image.'
                    });
                }
            });
        };

    }]);

    module.controller('ImagesCtrl', ['$rootScope', '$scope', 'FileUploader', 'LMBAjaxService', '$timeout', function ($rootScope, $scope, FileUploader, LMBAjaxService, $timeout) {
        $scope.uploading = false;
        $scope.directUpload = false;

        $scope.sortableOptions = {
            axis: 'y',
            handle: '.button-sortable',
            cursor: 'move',
            cancel: '',
            helper: function (event, ui) {
                ui.children().each(function () {
                    jQuery(this).width(jQuery(this).width());
                });
                return ui;
            },
            update: function (e, ui) {
                $timeout(function () {
                    var ordres = [];
                    angular.forEach($scope.article.images.list, function (image, key) {
                        var img = { id_image: image.id_image, ordre: $scope.article.images.list.indexOf(image) };
                        ordres.push(img);
                    });

                    LMBAjaxService.post('page.php/Article/Standard/Standard/updateOrdresImages', {
                        ref_article: $scope.article.info_generales.ref_article,
                        ordres: ordres
                    });

                }, 500);
            }
        };

        var uploader = $scope.uploader = new FileUploader({
            url: 'upload.php'
        });

        uploader.filters.push({
            name: 'imageFilter',
            fn: function (item, options) {
                var type = '|' + item.type.slice(item.type.lastIndexOf('/') + 1) + '|';
                return '|jpg|png|jpeg|bmp|gif|webp|avif|svg|'.indexOf(type) !== -1;
            }
        });

        $scope.init = function (directUpload) {
            $scope.directUpload = directUpload;
        };

        $scope.uploadURL = function () {
            if (!$scope.urlImageUpload) {
                let content = __(521938, "Impossible d'importer l'image depuis l'URL renseignée. Vous devez renseigner le champ avec une URL d'image valide.");
                LMBTools.alert({
                    content: content
                });
                $scope.uploading = false;
                return;
            }
            $scope.uploading = true;
            var url = 'page.php' + '/Components/Standard/Datas/getFileFromUrl';

            LMBAjaxService.post(url, { url: $scope.urlImageUpload }, false, {
                transformRequest: angular.identity,
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                responseType: 'blob'
            }).then(function success(response) {
                var blob = response.data;
                var file = new File([blob], "uploadurl_" + uploader.queue.length + "." + blob.type.split("/")[1], {
                    lastModified: new Date(0),
                    type: blob.type
                });
                uploader.addToQueue(file, false, false);

                if ($scope.directUpload) {
                    $scope.article.images.push(uploader.queue);
                } else {
                    $scope.article.new_images = uploader.queue;
                }
            }, function failure(response) {
                $scope.uploading = false;
                LMBTools.alert({
                    title: __(112606, "Erreur"),
                    content: __(112607, "L’URL indiquée n’a pas pu être téléchargée :<br />")
                        + __(112608, "Erreur ") + response.status + " (" + response.statusText + ")"
                });
            });
        };

        uploader.onAfterAddingAll = function (addedFileItems) {
            $scope.article.images.queue = this.queue;
            $scope.uploading = false;
        };

        uploader.removeFromQueue = function (value) {
            var index = this.getIndexOfItem(value);
            var item = this.queue[index];
            if (item.isUploading)
                item.cancel();
            this.queue.splice(index, 1);
            item._destroy();
            this.progress = this._getTotalProgress();
            $scope.article.images.queue = this.queue;
        };

        $scope.rmImage = function (image) {
            LMBTools.confirm({
                content: __(112609, "Êtes-vous sûr de vouloir supprimer cette image ?"),
                confirm: __(112610, "Supprimer"),
                onValid: function (confirm) {
                    if (confirm) {

                        if ($scope.directUpload) {

                            LMBAjaxService.post('page.php/Article/Standard/Standard/delImage', {
                                ref_article: $scope.article.info_generales.ref_article,
                                id_image: image.id_image
                            });
                        }

                        var index = $scope.article.images.list.indexOf(image);
                        $scope.article.images.list.splice(index, 1);

                        $rootScope.safeApply();
                    }
                }
            });
        };

        $scope.addImageUrl = function (url) {
            var elm = $j('.jFiler-input-icon i');
            var icon = elm.attr('class');
            elm.attr('class', 'spinner');

            LMBAjaxService.post('page.php/Article/Standard/Standard/saveImageUrl', {
                ref_article: $scope.article.info_generales.ref_article,
                url: url
            })
                .then(function success(response) {

                    $scope.refreshImages();
                    elm.attr('class', icon);
                    $j('#jfiler-add-url').val('');

                }, function failure(response) {
                    $scope.uploading = false;
                    LMBTools.alert({
                        title: __(112611, "Erreur"),
                        content: __(112613, "L’URL indiquée n’a pas pu être téléchargée :<br />")
                            + __(112612, "Erreur ") + response.status + " (" + response.statusText + ")"
                    });
                });
        };

        $scope.refreshImages = function () {
            LMBAjaxService.post('page.php/Article/Standard/Standard/refreshImages', { ref_article: $scope.article.info_generales.ref_article })
                .then(function success(response) {
                    $scope.article.images = response.data.datas;
                });
        };

        $scope.updateTitre = function (image) {
            LMBAjaxService.post('page.php/Article/Standard/Standard/updateTitreImage', {
                id_image: image.id_image,
                titre: image.titre
            }).then(function (res) {
                if (res.error) {
                    LMBToast.error({
                        title: 'Image',
                        message: 'Une erreur est survenue lors de la mise à jour du titre de l\'image.'
                    });
                }
            });
        };

        $scope.updateDescription = function (image) {
            LMBAjaxService.post('page.php/Article/Standard/Standard/updateDescriptionImage', {
                id_image: image.id_image,
                description: image.description
            }).then(function (res) {
                if (res.error) {
                    LMBToast.error({
                        title: 'Image',
                        message: 'Une erreur est survenue lors de la mise à jour de la description de l\'image.'
                    });
                }
            });
        };

    }]);


    module.controller('OptionsLiaisonCtrl', ['$scope', 'close', 'article', 'articleLinked', 'qteArticle', 'qteArticleLinked', 'id_liaison_type', function ($scope, close, article, articleLinked, qteArticle, qteArticleLinked, id_liaison_type) {

        $scope.article = article;
        $scope.articleLinked = articleLinked;
        if (articleLinked.rules !== null && articleLinked.rules !== undefined && articleLinked.rules !== "") {
            $scope.rule = articleLinked.rules.substring(articleLinked.rules.indexOf("#") + 1, articleLinked.rules.indexOf("("));
        }
        $scope.qteArticle = qteArticle;
        $scope.qteArticleLinked = qteArticleLinked;

        $scope.calculRatio = function () {
            if (id_liaison_type.include("is")) {
                $scope.articleLinked.rules = "#RATIO(" + $scope.qteArticle + "," + $scope.qteArticleLinked + ")";
            } else if (id_liaison_type.include("has")) {
                $scope.articleLinked.rules = "#RATIO(" + $scope.qteArticleLinked + "," + $scope.qteArticle + ")";
            }
        };

        $scope.close = function () {
            $scope.display = false;
            close({}, 100);
        };
    }]);

    /*
     * Controller de la page de Nomenclature (VIEILLE MAQUETTE)
     */
    module.controller('NomenclatureCtrl', ['$scope', 'LMBModalService', function ($scope, LMBModalService) {
        let vm = this;

        // Variables
        vm.composition = {
            'niveaux': [
                // Données bidons
                {
                    'id': 0,
                    'lib': "Entrées",
                    'liste_articles': [
                        {
                            'ref_article': "A-00001",
                            'lib_article': "Salade César",
                            'prix_article': 6,
                            'plus_value': 0
                        },
                        {
                            'ref_article': "A-00004",
                            'lib_article': "Salade de chèvre chaud",
                            'prix_article': 5,
                            'plus_value': 0
                        }
                    ]
                },
                {
                    'id': 1,
                    'lib': "Plats",
                    'liste_articles': [
                        {
                            'ref_article': "A-00012",
                            'lib_article': "Pates Carbonara",
                            'prix_article': 12,
                            'plus_value': 0
                        },
                        {
                            'ref_article': "A-00018",
                            'lib_article': "Magret de canard au miel",
                            'prix_article': 14,
                            'plus_value': 0
                        }
                    ]
                },
                {
                    'id': 2,
                    'lib': "Desserts",
                    'liste_articles': [
                        {
                            'ref_article': "A-00013",
                            'lib_article': "Coupe colonel",
                            'prix_article': 4,
                            'plus_value': 0
                        },
                        {
                            'ref_article': "A-00007",
                            'lib_article': "Gauffre au chocolat ( maison )",
                            'prix_article': 3,
                            'plus_value': 0
                        }
                    ]
                }
            ],
            'regle_calcul': "PV=PPC",
            'prix_vente': 0
        };
        vm.editing_niveau = null;
        vm.type_composant = "fixe";
        vm.choix_niveau = null;
        vm.regle_calcul = "PV=PPC";
        vm.prix_fixe = 0;

        // Functions
        vm.init = init;
        vm.addNiveau = addNiveau;
        vm.addArticle = addArticle;
        vm.getListeComposants = getListeComposants;
        vm.updatePrixComposition = updatePrixComposition;
        vm.calcPrixComposition = calcPrixComposition;
        vm.calcTotalPlusValue = calcTotalPlusValue;
        vm.deleteNiveau = deleteNiveau;
        vm.deleteArticle = deleteArticle;

        function init() {
            vm.composition.prix_vente = vm.calcPrixComposition();
        }

        function addNiveau() {
            let nom_niveau = (vm.nom_niveau) ? vm.nom_niveau : "Non défini";

            vm.composition.niveaux.push({
                'id': vm.composition.niveaux.length,
                'lib': nom_niveau,
                'liste_articles': []
            });
            vm.nom_niveau = "";
        }


        function addArticle(id_niveau) {
            if (id_niveau === null) {
                LMBTools.alert("Veuillez choisir le niveau où ajouter les articles.");
                return;
            }

            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(112605, "Moteur d'articles"),
                    drag: true,
                    resizable: true,
                    overlay: true,
                    style: { width: "920px", height: "680px" }
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:Article/Standard/pp_recherche_article_multi_select.phtml",
                        controller: "RechercheMultiArticleController",
                        inputs: {
                            SearchParams: { parents: 0 },
                            ArticlesLies: vm.getListeComposants(id_niveau)
                        }
                    },
                    then: function (modal) {
                        var modal_element = modal.element[0];
                        var input = modal_element.querySelector("#lib_article");
                        setTimeout(function () {
                            input.focus();
                        }, 100);
                    },
                    onClose: function (result) {
                        let niveau = _.find(vm.composition.niveaux, niveau => (niveau.id == id_niveau));
                        if (niveau && result.articles && result.articles.length) {
                            angular.forEach(result.articles, (article) => {
                                let key_tarif = _.find(Object.keys(article), key => {
                                    return key.match('prix_vente*');
                                });
                                let prix_vente = (key_tarif && typeof article[key_tarif] != "undefined") ? article[key_tarif] : 0;

                                niveau.liste_articles.push({
                                    'ref_article': article.ref_article,
                                    'lib_article': article.lib_article,
                                    'prix_article': prix_vente,
                                    'plus_value': 0
                                });
                            });
                        }
                        vm.composition.prix_vente = vm.calcPrixComposition();
                        return;

                    }
                }
            });

        }

        function getListeComposants(id_niveau) {
            let niveau = _.find(vm.composition.niveaux, niveau => (niveau.id == id_niveau));
            if (niveau && typeof niveau.liste_articles != "undefined") {
                return _.pluck(niveau.liste_articles, 'ref_article');
            } else {
                return [];
            }
        }

        function updatePrixComposition() {
            vm.composition.regle_calcul = vm.regle_calcul;
            if (vm.composition.regle_calcul == "Prix fixe") {
                vm.composition.prix_vente = vm.prix_fixe;
            } else {
                vm.composition.prix_vente = vm.calcPrixComposition();
            }
        }

        function calcPrixComposition() {
            // On n'a pas besoin de calculer le total du prix des articles si la compositon a un prix fixe
            if (vm.composition.regle_calcul == "Prix fixe") {
                return vm.composition.prix_vente;
            }
            let prix_total = 0;
            angular.forEach(vm.composition.niveaux, (niveau) => {
                angular.forEach(niveau.liste_articles, (article) => {
                    prix_total += (article.prix_article) ? parseInt(article.prix_article) : 0;
                });
            });
            prix_total += parseInt(vm.calcTotalPlusValue());
            return prix_total;
        }

        function calcTotalPlusValue() {
            let plus_value_totale = 0;
            angular.forEach(vm.composition.niveaux, (niveau) => {
                angular.forEach(niveau.liste_articles, (article) => {
                    plus_value_totale += (article.plus_value) ? parseInt(article.plus_value) : 0;
                });
            });
            return plus_value_totale;
        }

        function deleteNiveau(id_niveau) {
            vm.composition.niveaux = _.filter(vm.composition.niveaux, niveau => (niveau.id != id_niveau));
            angular.forEach(vm.composition.niveaux, (niveau, index) => {
                niveau.id = index;
            });
        }

        function deleteArticle(index_niveau, ref_article) {
            if (typeof vm.composition.niveaux[index_niveau] != "undefined") {
                let liste_filtree = _.filter(vm.composition.niveaux[index_niveau].liste_articles, article => (article.ref_article != ref_article));
                vm.composition.niveaux[index_niveau].liste_articles = liste_filtree;
            }
            vm.composition.prix_vente = vm.calcPrixComposition();
        }
    }]);

})();

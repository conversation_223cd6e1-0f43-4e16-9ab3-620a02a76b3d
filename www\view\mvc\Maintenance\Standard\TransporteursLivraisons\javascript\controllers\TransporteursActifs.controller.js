'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .controller('TransporteursActifsController', TransporteursActifsController);

    TransporteursActifsController.$inject = ['$scope','TransporteursLivraisonsApi','transporteurs'];

    function TransporteursActifsController($scope, TransporteursLivraisonsApi , transporteurs) {
        $scope.transporteurs = transporteurs.liste;

        $scope.toggleActifModule = function (id_module , actif) {
            TransporteursLivraisonsApi.toggleActifModule({id_module , actif}).then(function () {
                LMBToast.success({
                    title: "Succès",
                    message: "Champ actif du module changé avec succès"
                });
            })
        }

        $scope.selectAll = function () {
            $scope.transporteurs.forEach(transporteur => {
                transporteur.selected = true;
            })
        }

        $scope.unselectAll = function () {
            $scope.transporteurs.forEach(transporteur => {
                transporteur.selected = false;
            })
        }

        $scope.invertSelect = function () {
            $scope.transporteurs.forEach(transporteur => {
                transporteur.selected = !transporteur.selected;
            })
        }

        $scope.actionSelectedModule = function (action) {
            if (action!=="") {
                let selectedList = [];
                $scope.transporteurs.forEach(transporteur => {
                    if (transporteur.selected) {
                        selectedList.push(transporteur.id_transport_module);
                        transporteur.actif=action;
                    }
                })
                if (selectedList.length) {
                    TransporteursLivraisonsApi.toggleMultipleActifModule({selectedList, action}).then(
                        LMBToast.success({
                            title: "Succès",
                            message: "Champ actif des modules changés avec succès"
                        })
                    );
                }
                $scope.action = "";
            }
        }
    }

})();
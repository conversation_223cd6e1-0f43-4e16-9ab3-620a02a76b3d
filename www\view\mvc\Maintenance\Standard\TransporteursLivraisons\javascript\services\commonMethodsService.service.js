'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .factory('commonMethodsService', commonMethodsService);

    function commonMethodsService() {
        return {
            transporteursListeCommun: function (scope , transporteursActifs, TransporteursLivraisonsApi) {
                scope.transporteurs = transporteursActifs.liste;
                scope.pays = transporteursActifs.infosPays;
                scope.langages = transporteursActifs.langages;

                scope.listeEtats = {};

                /**
                 * Récupère la liste des états liés au pays sélectionné pour, si ils existent pour les ajouter à la liste
                 * lié au nom du champ ayant fait l'update permettant donc d'afficher le champ des codes états liés, sinon
                 * supprime toute mention de liste liée au nom dans la liste.
                 * @param nom nom du champ pays ayant fait la sélection
                 * @param codePays code du pays sélectionné
                 */
                scope.updateChampEtat = function (nom, codePays) {
                    nom = nom.split('.').join('');
                    if (codePays && scope.pays.listeInverseePourEtat[codePays]) {
                        TransporteursLivraisonsApi.getListeEtats({idPays: scope.pays.listeInverseePourEtat[codePays].id_pays}).then(function (response) {
                            Object.defineProperty(scope.listeEtats, nom, {value: response.liste, writable: true});
                        })
                    }
                    else {
                        scope.listeEtats[nom] = undefined;
                    }
                }
            },
            transporteurCommun: function (scope) {
                /**
                 * Fonction pour reset les constantes dont la valeur a été définie en dehors de ce qui est attendu
                 * @param data les données de la ligne avec la constant
                 */
                scope.resetConstant = function (data) {
                    data.default = data.supposed;
                }

                /**
                 * Fonction à activer sur les champs avec ng-blur pour appliquer les filtres potentiels sur la valeur
                 * @param data les données de la ligne avec le filtre
                 */
                scope.filter = function (data) {
                    if (data.filters && data.default) {
                        Object.keys(data.filters).forEach(function (filterKey) {
                            if (data.filters[filterKey] === "upcase") data.default = data.default.toUpperCase();
                            else if (data.filters[filterKey] === "replace") {
                                for (let i = 0; i < data.filters.replace.old.length; i++) {
                                    data.default = data.default.replaceAll(data.filters.replace.old[i], data.filters.replace.new[i]);
                                }
                            }
                        })
                    }
                }
            },
            /**
             * Fonction à activer avec le save pour vérifier les données de configs communes modules / modes
             * @param configChecked les données de config en entier
             * @param keepEmpty true si on doit sauvegarder dans la config les valeurs vides de la config standard
             * @return boolean|{} booléen si on rencontre une erreur, un objet contenant la sauvegarde au bon format sinon
             */
            checkConfig: function (configChecked, keepEmpty = false) {
                let configReturned = {};
                let error = false;

                Object.keys(configChecked.standard).forEach(function (configKey) {
                    let obj = configChecked.standard[configKey];

                    if (obj.hasOwnProperty('default') && obj.default !== "") {
                        //Si default ne respecte pas la regex, lance une erreur
                        if (!new RegExp(obj.regex).test(obj.default)) {
                            LMBToast.error({
                                title: "Elément ne respectant pas la regex",
                                message: "L'élément \"" + configKey + " présenté sous le nom " + (obj.name ? obj.name : key)
                                    + "\" ne respecte pas la condition \"" + (obj.regexValidate ? obj.regexValidate : obj.regex)
                                    + "\" votre validation a donc échoué."
                            });
                            error = true;
                        }
                        //Sinon on défini la propriété dans l'objet
                        else Object.defineProperty(configReturned , configKey , {value: obj.default , enumerable: true})
                    }
                    else if (keepEmpty) Object.defineProperty(configReturned , configKey , {value: '' , enumerable: true})
                })
                //Si nous avons rencontré une erreur avant nous ne continuons pas
                if (!error) {
                    //On parcours chaque valeur non standard de la config de la structure
                    configChecked.hors_structure.forEach(function (configPerso) {
                        //Si l'élément possède un nom on le sauvegarde quoiqu'il arrive
                        if (configPerso[0] !== "") {
                            if (!Object.hasOwn(configReturned,configPerso[0])) Object.defineProperty(configReturned , configPerso[0],{value: configPerso[1],enumerable: true});
                            else {
                                error = true;
                                LMBToast.error({
                                    title: "Elément personnalisé en doublon : " + configPerso[0],
                                    message: "Nom de variable défini plus d'une fois (en éléments personnalisés ou en structure personnalisée). Par titre on ne peut enregistrer qu'une seule valeur, veuillez supprimer l'une de vos définitions."
                                });
                            }
                        }
                        //Si l'élément ne possède pas de nom mais possède bien une valeur on se mets en erreur
                        else if (configPerso[1] !== "") {
                            error = true;
                            LMBToast.error({
                                title: "Elément personnalisé au titre vide",
                                message: "Un nom est obligatoire pour chaque valeur personnalisée définie. Veuillez soit lui" +
                                    " définir un nom soit supprimer la valeur associée au titre vide."
                            });
                        }
                    })
                }
                return (error ? error : configReturned);
            }
        }
    }
})();
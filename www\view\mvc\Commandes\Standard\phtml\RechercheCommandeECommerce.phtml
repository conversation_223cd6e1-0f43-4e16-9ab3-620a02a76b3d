<style>
    .type_doc {
        display: none;
    }
</style>
<div class="lmb-theme">

    
    <div class="container component-search-engine" ng-app="lmb">
        <div lmb-comp-search-engine-engine engine-type="docVente" options='{ "auto_search" : true, "resultOptions": {"loadingRows": 15, "order": { "orderBy": ["date_doc"], "orderDirection": "DESC" }, "paginationOptions": { "arrayLimits": ["20","50","100","200","500"] } }, "searchOptions": { "id_type_doc": ["<?= $this->view->id_type_doc ?>"] } }'></div>

    </div>
</div>

<script>
    (function($) {

        var $container = $('.container.component-search-engine');
        angular.bootstrap($container[0], [$container.attr('ng-app')]);

    })(jQuery);

    
    breadcrumbScheme.list = [
        { label: __(710561, "Commandes") },
        { label:  __(580016, "Recherche d'un document") }
    ];

</script>
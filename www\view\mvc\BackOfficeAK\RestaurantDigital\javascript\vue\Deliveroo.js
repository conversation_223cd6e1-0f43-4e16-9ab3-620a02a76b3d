let DeliverooController = {
    components: {},
    data() {
        return {
            // Vraies variables
            API_BASE: 'page.php/BackOfficeAK/Standard/RestaurantDigital',
            module: {
                ref_module: "",
                params: {
                    actif: false,
                    client_id: null,
                    client_secret: null,
                    shared_secret: null,
                    location_ids: {}
                },
            },
            liste_magasins: [],
            locations_edited: false,
        }
    },
    mounted() {
        this.init();
    },

    methods: {
        // Fonctions appelées par les update des composants Angular
        init() {
            const url = `${this.API_BASE}/load`
            const that = this

            LMBTools.post({
                url,
                data: {},
                dataType: 'json',
                success: function (response) {
                    if(that._hasError(response)) {
                        return;
                    }

                    that._setData(response)
                    that.liste_magasins = response.liste_magasins || []
                }
            })
        },
        toggleModule() {
            const data = {
                actif: this.module.params.actif,
                ref_module: 'deliveroo'
            };
            const url = `${this.API_BASE}/toggleModule`
            const that = this

            LMBTools.post({
                url,
                data,
                dataType: 'json',
                success: function (response) {
                    if(that._hasError(response)) {
                        that.module.params.actif = that.module.params.actif == 1 ? 0 : 1;
                    } else {
                        that._setData(response)
                    }
                },
                error: function(response) {
                    that.module.params.actif = that.module.params.actif == 1 ? 0 : 1;
                }
            });
        },
        saveModuleConfig() {
            const module = {...this.module}
            const data = {
                module,
                locations_edited: this.locations_edited
            }
            const url = `${this.API_BASE}/saveModuleConfig`
            const that = this

            LMBTools.post({
                url,
                data,
                dataType: 'json',
                success: function (response) {
                    if(that._hasError(response)) {
                        if(response.module) {
                            that._setData(response)
                        } else {
                            that.module = module;
                        }
                    } else {
                        that._setData(response)

                        LMBToast.success({
                            title: __(182843,"Configuration sauvegardée")
                        });
                    }
                }
            });
        },
        // Private functions
        _setData(response) {
            this.module = response.module
            this.module.params = this.module.params || {}
            this.module.params.location_ids = this.module.params.location_ids || {}
        },
        _hasError(response = {}) {
            if(typeof response.status == "undefined" || response?.status != "ok") {
                if(response.error && response.error_message) {
                    LMBToast.error({
                        title: response.error,
                        message: response.error_message
                    });
                } else if(response.error) {
                    LMBToast.error({
                        title: __(340842,"Problème technique"),
                        message: response.error
                    });
                } else {
                    LMBToast.errorTechnique();
                }
                return true;
            }
            return false;
        },
    }

};

export { DeliverooController }
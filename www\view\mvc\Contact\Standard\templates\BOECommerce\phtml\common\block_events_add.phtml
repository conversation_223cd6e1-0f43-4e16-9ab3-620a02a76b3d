<div id="bloc-events-add" class="hidden">

    <div class="portlet">
        <div class="portlet-header">
            <h1><?php _e_html(110707,"Ajouter un événement"); ?></h1>
        </div>
        <div class="portlet-body">

            <div class="row">
                <div class="col-1-2">
                    <table class="style-1 auto-layout">
                        <tr>
                            <td class="input-xsmall"><?php _e_html(110708,"Type"); ?></td>
                            <td>
                                <select ng-model="new_event.id_comm_event_type" class="input-full" qa_id="170330">
                                    <option ng-repeat="type in instance.event_types"
                                            value="{{ type.id_comm_event_type }}">
                                        {{ type.lib_comm_event_type }}
                                    </option>
                                </select>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-1-2">
                    <table class="style-1 auto-layout">
                        <tr>
                            <td class="input-small"><?php _e_html(110709,"Date"); ?></td>
                            <td>
                                <input type="text" lmb-date ng-model="new_event.date_event" qa_id="170331">
                            </td>
                        </tr>
                        <tr>
                            <td class="input-small"><?php _e_html(110710,"Heure"); ?></td>
                            <td>
                                <input type="text" ng-model="new_event.heure_event" qa_id="170332">
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="col-1-1">
                <table class="style-1">
                    <tr>
                        <td class="input-xsmall"><?php _e_html(180582,"Durée"); ?></td>
                        <td>
                            <input type="text" ng-model="new_event.duree.jours" lmb-number="type:entierPosOrZero" class="input-xxsmall"> jours,
                            <input type="text" ng-model="new_event.duree.heures" lmb-number="type:hour" class="input-xxsmall"> heures et
                            <input type="text" max="60" ng-model="new_event.duree.minutes" lmb-number="type:minute" class="input-xxsmall"> minutes
                        </td>
                    </tr>
                </table>
            </div>
            <div class="clear"></div>

            <table class="style-1 auto-layout">
                <tr>
                    <td class="valign-top input-xsmall"><?php _e_html(110711,"Texte"); ?></td>
                    <td>
                        <lmb-textarea ng-model="new_event.texte" valeur="new_event.texte" rows="8" max-rows="15" class="input-full" qa_id="170333"></lmb-textarea>
                    </td>
                </tr>
            </table>

            <div class="portlet-footer">
                <input type="submit" value="<?php _e(110712,"Enregistrer"); ?>" class="btn btn-tertiary" ng-click="saveEvent(new_event)" qa_id="170334"/>
            </div>

        </div>
    </div>

</div>

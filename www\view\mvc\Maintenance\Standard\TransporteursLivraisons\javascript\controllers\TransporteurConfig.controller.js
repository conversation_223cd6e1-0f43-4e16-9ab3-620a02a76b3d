'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .controller('TransporteurConfigController', TransporteurConfigController);

    TransporteurConfigController.$inject = ['$scope', 'TransporteursLivraisonsApi', 'transporteur', 'idModule','commonMethodsService'];

    function TransporteurConfigController($scope, TransporteursLivraisonsApi, transporteur, idModule, commonMethodsService) {
        /**
         * Attribution de toutes les informations du transporteur chargées au préalable par le routeur
         */
        $scope.transporteur = transporteur;

        /**
         * Fonction appelée au clic sur "valider", vérifie la validité par checkConfig() puis si pas de booléen / d'erreur
         * fait l'appel ajax d'enregistrement en mettant la modale appropriée.
         */
        $scope.saveConfig = function () {
            let config = commonMethodsService.checkConfig($scope.transporteur.config,true);

            //Si nous avons rencontré une erreur avant nous ne continuons pas
            if (typeof config !== "boolean") {
                TransporteursLivraisonsApi.saveConfigModule({
                    idConfig: transporteur.config.idConfig ?? null,
                    config,
                    idModule,
                    libConfig: $scope.transporteur.config.libConfig
                }).then(function (response) {
                    //On récupère l'idConfig de la réponse car si notre config n'existait pas et a donc été crée,
                    // on doit pouvoir sauvegarder dessus par la suite sans recharger la page
                    $scope.transporteur.config.idConfig = response.idConfigModule;
                    LMBToast.success({
                        title: "Succès",
                        message: "Configuration du module changée avec succès"
                    });
                });
            }

        }

        /**
         * Fonction pour ajouter une ligne de config personnalisée vide pour la structure donnée
         */
        $scope.ajouter = function () {
            $scope.transporteur.config.hors_structure.push(["", ""]);
        }

        // Attribution au scope des méthodes resetConstant() et filter()
        commonMethodsService.transporteurCommun($scope);
    }

})();
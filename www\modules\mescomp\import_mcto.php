<?php
	require_once ("_dir.inc.php");
	require_once ($DIR."_session.inc.php");
	
	if(!is_dir($DIR."fichiers/import_mcto/")) 			{ mkdir($DIR."fichiers/import_mcto/"); 			}
	if(!is_dir($DIR."fichiers/import_mcto/incoming/")) 	{ mkdir($DIR."fichiers/import_mcto/incoming/"); }
	
	$erreurs 			= array();
	
	if(!empty($_REQUEST['officeid'])) { $officeid 	= $_REQUEST['officeid']; }else{ $officeid 	= ""; $erreurs[] = "3001"; }
	if(!empty($_REQUEST['mcto'])) 	  { $mcto 		= $_REQUEST['mcto']; 	 }else{ $mcto 		= ""; $erreurs[] = "3002"; }
	if(!empty($_REQUEST['tourop']))   { $tourop 	= $_REQUEST['tourop']; 	 }else{ $tourop 	= ""; $erreurs[] = "3003"; }
	if(!empty($_REQUEST['réseau']))   { $reseau 	= $_REQUEST['réseau']; 	 }else{ $reseau 	= ""; $erreurs[] = "3004"; }
	if(!empty($_REQUEST['Filename'])) { $filename 	= $_REQUEST['Filename']; }else{ $filename 	= ""; $erreurs[] = "3005"; }
	
	$datas 				= array();
	$datas['officeid'] 	= $officeid;
	$datas['mcto']		= $mcto;
	$datas['tourop']	= $tourop;
	$datas['reseau']	= $reseau;
	$datas['filename'] 	= $filename;

	if(empty($erreurs)) {

		$file_name 			= uniqid()."_".date("Y-m-d_h-s")."_valid.xml";
		file_put_contents($DIR."fichiers/import_mcto/incoming/".$file_name, json_encode($datas));
		
		$reponse 			= <<<XML
<Values version = "2.0">
<value code = "0">
<value message = "OK">
</Values>
XML;
		
	}else{
	
		$datas['erreurs'] 	= $erreurs;
	
		$file_name 			= uniqid()."_".date("Y-m-d_h-s")."_error.xml";
		file_put_contents($DIR."fichiers/import_mcto/incoming/".$file_name, json_encode($datas));
	
		$reponse 			= <<<XML
<Values version = "2.0">
<value code = "1">
<value message = "FAILED">
</Values>
XML;
	
	}
	
	die($reponse);

?>
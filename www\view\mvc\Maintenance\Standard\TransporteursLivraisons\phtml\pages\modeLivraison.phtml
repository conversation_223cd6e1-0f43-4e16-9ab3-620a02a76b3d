<form ng-submit="sauvegarder()">
    <div class="portlet-body">
        <div class="row">
            <table class="style-1 auto-layout">
                <tbody>
                <tr>
                    <td colspan="100%" style="border:0" class="text-left">
                        <h2>Informations générales</h2>
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="actif">Actif</label>
                    </td>
                    <td>
                        <input type="checkbox" class="checkbox-switch toggle" ng-model="infoModeDeLivraison.actif"
                               id="actif" ng-true-value="'1'" ng-false-value="'0'">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="libArticle">Libellé de l'article lié {{ infoModeDeLivraison.ref_article }}</label>
                    </td>
                    <td>
                        <input type="text" id="libArticle" ng-model="infoModeDeLivraison.lib_article" class="input-full">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="delaiNumber"><PERSON><PERSON><PERSON> de livraison</label>
                    </td>
                    <td>
                        <input id="delaiNumber" type="text" lmb-number="type:entierPosOrZero"
                               ng-model="infoModeDeLivraison.delai_livraison_duree">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="delaiText">Délai de livraison (texte)</label>
                    </td>
                    <td>
                        <input id="delaiText" type="text" ng-model="infoModeDeLivraison.delai_livraison_texte"
                               placeholder="ex: 2 à 3 jours" class="input-full">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="refInterne">Référence interne</label>
                    </td>
                    <td>
                        <input id="refInterne" type="text" ng-model="infoModeDeLivraison.ref_interne"
                               class="input-full">
                    </td>
                </tr>
                <tr>
                    <td>
                        Type de livraison
                    </td>
                    <td>
                        <label>
                            <input name="livraisonType" type="radio" ng-model="infoModeDeLivraison.type"
                                   value="livraison"> Livraison à domicile
                        </label>
                        <label>
                            <input name="livraisonType" type="radio" ng-model="infoModeDeLivraison.type"
                                   value="enlevement"> Enlèvement en magasin
                        </label>
                    </td>
                </tr>
                <tr ng-if="infoModeDeLivraison.type==='enlevement'">
                    <td>
                        <?php _e(520494,"Magasins"); ?> :
                    </td>
                    <td>
                        <div class="dropdown dropdown-checkbox input-full" onclick="" id="dropdown_magasins"
                             data-lmb-magasins="{ 'value':{{infoModeDeLivraison.parametres.magasins}}}"
                             onchange="">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="100%" style="border:0" class="text-left">
                        <h2>Prix de vente</h2>
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="franco">Appliquer la règle de Franco du Client</label>
                    </td>
                    <td>
                        <input id="franco"  type="checkbox" class="checkbox-switch toggle"
                               ng-model="infoModeDeLivraison.use_franco_client" ng-true-value="'1'" ng-false-value="'0'">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="modeVente"><?php echo langage::write('regle_calcul_prix_vente_mode_livraison'); ?></label>
                    </td>
                    <td>
                        <select id="modeVente" ng-model="containerVente.base" ng-change="creerInfosVente()" ng-init="creerInfosVente()">
                            <option ng-repeat="type in infos.livraisonsModes" value="{{type.id}}" ng-selected="containerVente.base===type.id">{{type.lib}}</option>
                        </select>
                    </td>
                </tr>
                <tr ng-if="containerVente.base===infos.livraisonsModes['forfait'].id">
                    <td>
                        <?php _e_html(521583,"Montant forfaitaire"); ?>
                    </td>
                    <td>
                        <label>
                            <input type="text" lmb-currency ng-model="containerVente.montant">
                        </label>
                        <label>
                            <input type="radio" name="venteForfaitTaxe" ng-model="containerVente.app_tarifs" value="<?php echo formule_tarif::PU_HT ?>">HT
                        </label>
                        <label>
                            <input type="radio" name="venteForfaitTaxe" ng-model="containerVente.app_tarifs" value="<?php echo formule_tarif::PU_TTC ?>">TTC
                        </label>
                    </td>
                </tr>
                <tr ng-if="containerVente.base===infos.livraisonsModes['reviens'].id">
                    <td>
                        <?php echo langage::write('selon_prix_revient');?>
                    </td>
                    <td>
                        <label>
                            <input type="radio" name="venteCoutReviens" ng-model="containerVente.type" value="marge">
                            <?php echo langage::write('appliquer_marge'); ?>
                            <input type="text" lmb-number ng-model="containerVenteTempo.marge">
                        </label>
                        |
                        <label>
                            <input type="radio" name="venteCoutReviens" ng-model="containerVente.type" value="coeff">
                            <?php echo langage::write('appliquer_coeff'); ?>
                            <input type="text" lmb-number ng-model="containerVenteTempo.coeff">

                        </label>
                        |
                        <label>
                            <input type="radio" name="venteCoutReviens" ng-model="containerVente.type" value="identique">
                            <?php echo langage::write('refacturer_identique'); ?>

                        </label>
                    </td>
                </tr>
                <tr ng-if-start="containerVente.base===infos.livraisonsModes['poids'].id || containerVente.base===infos.livraisonsModes['poids_dest'].id">
                    <td>
                        <?php echo langage::write('au_dela')?> {{ auDela }} kg :
                    </td>
                    <td>
                        <label><input ng-model="containerVente.forfait" type="text"> €</label> + <label><input ng-model="containerVente.kilos_supp" type="text">€/kg</label>
                    </td>
                </tr>
                <tr ng-if-end>
                    <td>
                        <label for="poidsTaxe">
                            <?php echo langage::write('expression_montant'); ?>
                        </label>
                    </td>
                    <td>
                        <select ng-model="containerVente.app_tarifs" id="poidsTaxe">
                            <option value="<?php echo formule_tarif::PU_TTC ?>">TTC</option>
                            <option value="<?php echo formule_tarif::PU_HT ?>">HT</option>
                        </select>
                    </td>
                </tr>
                <tr ng-if="containerVente.base===infos.livraisonsModes['prix'].id || containerVente.base===infos.livraisonsModes['prix_dest'].id">
                    <td>
                        <?php _e_html(340137,"Montant de la commande"); ?>
                    </td>
                    <td>
                        <label><input type="radio" name="ventePrixTaxe" ng-model="containerVente.app_tarifs" value="<?php echo formule_tarif::PU_HT ?>">HT</label>
                        <label><input type="radio" name="ventePrixTaxe" ng-model="containerVente.app_tarifs" value="<?php echo formule_tarif::PU_TTC ?>">TTC</label>
                    </td>
                </tr>
                <tr ng-if="containerVente.base!==infos.livraisonsModes['forfait'].id && containerVente.base!==infos.livraisonsModes['reviens'].id">
                    <td colspan="100%" style="border:0">
                        <div class="tableur_vente" ng-init="creerTableurVente()"></div>
                    </td>
                </tr>
                <tr>
                    <td colspan="100%" style="border:0" class="text-left">
                        <h2>Prix de revient</h2>
                    </td>
                </tr>
                <tr>
                    <td colspan="100%" style="border:0">
                        <div class="tableur_achats"></div>
                    </td>
                </tr>
                <tr>
                    <td colspan="100%" style="border:0" class="text-left">
                        <h2>Zones de livraison</h2>
                    </td>
                </tr>
                <tr>
                    <td colspan="100%" class="text-left">
                        <label>
                            <select name="id_zonage" ng-model="infoModeDeLivraison.id_zonage" ng-change="creerTableurReviens() || creerInfosVente()">
                                <option value="" ng-selected="!infoModeDeLivraison.id_zonage"><?php echo langage::write("non_defini"); ?></option>
                                <option ng-repeat="zonage in infos.zonage" value="{{zonage.id}}" ng-selected="infoModeDeLivraison.id_zonage===zonage.id">{{zonage.lib}}</option>
                            </select>
                        </label>
                    </td>
                </tr>
                <tr>
                    <td colspan="100%" style="border:0" class="text-left">
                        <h2>Restrictions</h2>
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="poids_supp"><?php echo langage::write('interdire_poids_supp'); ?></label>
                    </td>
                    <td>
                        <input type="text" id="poids_supp" ng-model="infoModeDeLivraison.codec_restrictions.poids_max" lmb-number="dec:3">
                    </td>
                </tr>
                <tr>
                    <td>
                        <label for="poids_inf"><?php echo langage::write('interdire_poids_inf'); ?></label>
                    </td>
                    <td>
                        <input type="text" id="poids_inf" ng-model="infoModeDeLivraison.codec_restrictions.poids_min" lmb-number="dec:3">
                    </td>
                </tr>
                <tr>
                    <td style="border:0" class="text-left">
                        <h2 class="bold">Paramètres spécifiques</h2>
                    </td>
                </tr>
                <?php
                    include("./view/mvc/Maintenance/Standard/TransporteursLivraisons/phtml/pages/templateConfig.phtml");
                ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="portlet-footer">
        <button type="submit" class="btn btn-primary">Valider</button>
    </div>
</form>
<?php
require ("_dir.inc.php");
require ($DIR."_session.inc.php");

$query = "SELECT duree, preavis, ref_article FROM articles_modele_service_abo";
$res = $bdd->query($query);

$abos = array();
if (is_object($res)) {
    while ($abo = $res->fetchObject()) {
        $abos[] = $abo;
    }
}

foreach($abos as $a){
    $duree = ($a->duree/(60*60*24))."j";
    $preavis = ($a->preavis/(60*60*24))."j";
    $query = "UPDATE articles_modele_service_abo SET duree='".$duree."', preavis='".$preavis."'
                    WHERE ref_article = '".$a->ref_article."'";
    $bdd->exec($query);
}

$query = "SELECT * FROM articles_abonnes_livraisons";
$res = $bdd->query($query);

$livr = array();
if (is_object($res)) {
    while ($line = $res->fetchObject()) {
        $livr[] = $line;
    }
}

$query = "INSERT INTO abonnes_events (id_abo,ref_doc,id_doc_line,date_event,type_event,event,ref_user) VALUES ";
$values = array();
foreach($livr as $lv){
    $values[] = "('$lv->id_abo','$lv->ref_doc','$lv->id_doc_line','$lv->date_renouvellement','import','Renouvellement basé sur ancienne gestion des abonnements, jusqu\'au $lv->date_echeance','".session_manager::get("user")->getRef_user()."')";
}
$query .= implode(",",$values);
$bdd->exec($query);

$query="DROP TABLE articles_abonnes_livraisons";
$bdd->exec($query);
?>

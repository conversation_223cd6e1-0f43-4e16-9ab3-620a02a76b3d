<?php
/**
 * Script de test pour vérifier que toutes les catégories d'articles 
 * apparaissent dans l'export du requêteur, même avec des valeurs à zéro
 */

// Inclure les fichiers nécessaires
require_once('www/core/_requeteur.class.php');

// Simuler une requête avec séparation par catégorie d'article uniquement
$du = "2025-01-01";
$au = "2025-12-31";
$modele = null;
$separations = [requeteur::SEPARATION_ARTICLE_CATEG]; // Uniquement catégorie d'article
$champs = [requeteur::CA, requeteur::CA_TTC, requeteur::CA_POTENTIEL]; // CA HT, CA TTC, CA potentiel
$decoupage = requeteur::DECOUPAGE_ANNEE;
$lignes_ss_total = false;
$separations_datas = [];

try {
    // Créer une instance du requêteur
    $requeteur = new requeteur($du, $au, $modele, $separations, $champs, $decoupage, $lignes_ss_total, $separations_datas);
    
    // Récupérer les données
    $resultset = $requeteur->getDatas();
    
    if ($resultset) {
        echo "Test réussi ! Voici les catégories trouvées :\n\n";
        
        $count = 0;
        while ($row = $resultset->fetchObject()) {
            $count++;
            echo "Catégorie {$count}: {$row->ref_art_categ}\n";
            echo "  - CA HT: " . ($row->{'2025_ca_ht'} ?? 0) . "\n";
            echo "  - CA TTC: " . ($row->{'2025_ca_ttc'} ?? 0) . "\n";
            echo "  - CA Potentiel: " . ($row->{'2025_ca_potentiel'} ?? 0) . "\n";
            echo "\n";
        }
        
        echo "Total des catégories trouvées: {$count}\n";
        
        // Vérifier qu'on a bien toutes les catégories
        $all_categories = art_categ::getAllCateg(true);
        echo "Total des catégories dans la base: " . count($all_categories) . "\n";
        
        if ($count >= count($all_categories)) {
            echo "✅ SUCCESS: Toutes les catégories sont présentes dans l'export !\n";
        } else {
            echo "❌ ERREUR: Il manque des catégories dans l'export.\n";
        }
        
    } else {
        echo "❌ ERREUR: Impossible de récupérer les données du requêteur.\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERREUR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

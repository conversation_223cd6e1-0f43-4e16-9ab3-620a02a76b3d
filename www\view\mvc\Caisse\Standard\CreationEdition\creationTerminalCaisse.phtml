<!-- JS -->
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular/composants/pagination/pagination.directive.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular/composants/form-wizard/form-wizard.directive.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/Article/Standard/javascript/angular/services.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Caisse/Standard/CreationEdition/javascript/_javascript_loader.php");
?>

<link rel="stylesheet" href="<?php echo $DIR; ?>view/mvc/Caisse/Standard/CreationEdition/css/style.css?rev=<?php echo lmbconfig::getSysVersion() ?>">

<div id="page-caisse-new" class="lmb-theme" ng-app="CaisseApp" ng-controller="CaisseController" ng-init="init({id_term: <?php echo $this->view->id_term ?? 'null'; ?>, logiciel: '<?php echo $this->view->logiciel ?? 'rovercash'; ?>'})" ng-cloak>
    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading"><?php _e_html(122195,"Caisse"); ?></div>
                <h1 ng-if="!data.terminal.id_caisse_terminal">
                    <?php _e(114019,'Création d’un terminal de caisse'); ?>
                </h1>
                <h1 ng-if="data.terminal.id_caisse_terminal">
                    <?php _e(114020,'Modification d’un terminal de caisse'); ?>
                </h1>
            </div>
            <div class="jumbotron-actions">
                <a href="#page.php/Caisse/Standard/Terminal/Home" class="btn btn-white light">
                    <i class="fa fa-chevron-left"></i>
                    <?php _e_html(114021,"Retour à la liste des caisses"); ?>
                </a>
            </div>
        </div>
    </header>

    <div ng-class="{'loader': !isLoaded}" ng-show="!isLoaded"></div>

    <div class="container" ng-show="isLoaded">
        <form>
            <div id="parametres-generaux" class="portlet">
                <div class="portlet-header">
                    <h1 ng-if="!data.terminal.id_caisse_terminal"><?php _e(122189,'Création d\'une nouvelle caisse'); ?></h1>
                    <h1 ng-if="data.terminal.id_caisse_terminal"><?php _e_html(343011,"Terminal point de vente"); ?></h1>
                    <div class="portlet-action">
                        <span ng-if="data.terminal.id_caisse_terminal">
                            <button type="button"
                                    class="btn btn-secondary right"
                                    ng-click="save()">
                                <?php _e_html(114057,"Enregistrer les changements"); ?>
                            </button>
                            <button type="button"
                                    class="btn btn-tertiary right"
                                    style="margin-right: 8px;"
                                    ng-click="archive()" disabled>
                                Archiver ce terminal
                            </button>
                        </span>
                        <button type="button"
                                class="btn btn-secondary right"
                                ng-if="!data.terminal.id_caisse_terminal"
                                ng-click="save()">
                            <?php _e_html(114059,"Créer le terminal de caisse"); ?>
                        </button>
                    </div>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="col-1-2">
                            <table class="style-1">
                                <tr>
                                    <td><?php _e_html(122190,"Nom de la caisse : "); ?></td>
                                    <td><input type="text" class="input-medium" required ng-model="data.terminal.lib"></td>
                                </tr>
                                <tr>
                                    <td><?php _e_html(122191,"Nom abrégé : "); ?></td>
                                    <td><input type="text" class="input-xsmall" ng-model="data.terminal.abrev"></td>
                                </tr>
                                <tr>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        <?php _e_html(122192,"Grille de tarif : "); ?>
                                    </td>
                                    <td>
                                        <select class="input-full"
                                                ng-model="data.terminal.id_tarif"
                                                ng-options="tarif.id as tarif.lib for (id,tarif) in data.tarifs">
                                        </select>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-1-2">
                            <table class="style-1">
                                <tr>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        <?php _e_html(320061,"Caisse de destination des fonds : "); ?>
                                    </td>
                                    <td>
                                        <div class="input-group input-full">
                                            <select class="input-full"
                                                    ng-model="data.terminal.id_caisse_centrale"
                                                    ng-options="id as c.lib for (id, c) in data.caisses">
                                            </select>
                                            <button type="button"
                                                    class="input-action"
                                                    ng-click="creationCaisseRapide()">
                                                <i class="fa fa-plus" />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <?php _e_html(320062,"Terminal de paiement : "); ?>
                                    </td>
                                    <td>
                                        <div class="input-group input-full">
                                            <select class="input-full"
                                                    ng-model="data.caisses[data.terminal.id_caisse_centrale].tpe"
                                                    ng-options="id as t.lib for (id, t) in data.terminaux_paiement">
                                            </select>
                                            <button type="button" class="input-action" ng-click="creationTpeRapide()">
                                                <i class="fa fa-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <br/>
                <div class="portlet-header">
                    <h1><?php _e_html(114025,"Utilisateurs"); ?></h1>
                </div>
                <div class="portlet-body">
                    <table class="style-1 auto-layout">
                        <tr class="text-center">
                            <td><?php _e_html(114052,"Ajouter un utilisateur"); ?></td>
                            <td class="left">
                                <span class="input-group">
                                    <select class="input-large"
                                            ng-model="$parent.currentUser"
                                            ng-options="id as u.nom for (id, u) in data.users">
                                        <option value=""><?php _e_html(122199,"Sélectionner un utilisateur"); ?></option>
                                    </select>
                                    <button type="button"
                                            class="input-action"
                                            ng-click="addUser()">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><?php _e_html(122198,"Utilisateurs autorisés"); ?></td>
                            <td>
                                <table class="style-2" style="max-width: 1200px">
                                    <thead>
                                    <tr>
                                        <th><?php _e_html(114053,"Pseudo"); ?></th>
                                        <th><?php _e_html(114054,"Nom complet"); ?></th>
                                        <th><?php _e_html(114055,"Type de droit"); ?></th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="(id, user) in data.permissions">
                                        <td>{{ user.lib }}</td>
                                        <td>{{ user.nom }}</td>
                                        <td>{{ user.droit }}</td>
                                        <td class="text-right" width='40px'>
                                            <button type="button"
                                                    class="btn btn-red light rounded"
                                                    ng-click="removeUser(id)"
                                                    ng-show="user.droit_id != 3">
                                                <i class="fa fa-remove"></i> <?php _e_html(114056,"Supprimer"); ?>
                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
                <br/>
                <div class="portlet-header">
                    <h1><?php _e_html(410090,"Catalogue"); ?></h1>
                </div>
                <br/>
                <div class="portlet-body">
                    <div class="row">
                        <table class="col-1-2 style-1">
                            <tr>
                                <td><?php _e_html(320059,"Centre de profit : "); ?></td>
                                <td>
                                    <div class="input-group input-full">
                                        <select class="input-full"
                                                ng-model="data.terminal.id_magasin"
                                                ng-options="magasin.id_magasin as magasin.lib_magasin for magasin in data.magasins">
                                        </select>
                                        <button type="button" ng-click="creationCentreProfitRapide()" class="input-action"><i class="fa fa-plus"></i></button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span ng-if="getObjectLength(data.catalogues) > 0">
                                        <?php _e_html(320060,"Catalogue client : "); ?>
                                    </span>
                                </td>
                                <td>
                                    <select class="input-full"
                                            ng-model="data.terminal.id_catalogue_client"
                                            ng-options="id as c.lib for (id, c) in data.catalogues"
                                            ng-if="getObjectLength(data.catalogues) > 0"
                                            ng-disabled="data.terminal.id_caisse_terminal">
                                    </select>
                                </td>
                            </tr>
                        </table>
                        <table class="col-1-2 style-1">
                            <tr>
                                <td>
                                    <?php _e_html(122193,"Stock principal : "); ?>
                                </td>
                                <td>
                                    <div class="input-group input-full">
                                        <select class="input-full" ng-options="opt.id as opt.lib for (id,opt) in data.stocks" ng-model="data.terminal.id_stock"></select>
                                        <button type="button" class="input-action" ng-click="creationStockRapide()">
                                            <i class="fa fa-plus"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <span ng-if="data.secondary_stocks.length > 1">
                                        <?php _e_html(122194,"Autres stocks affichés : "); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="dropdown dropdown-checkbox input-full"
                                         ng-if="data.secondary_stocks.length > 1">
                                        <div class="dropdown-button"></div>
                                        <ul class="dropdown-menu">
                                            <li ng-repeat="c in data.secondary_stocks | filter:FilterFunction">
                                                <input type="checkbox"
                                                       class="input-large"
                                                       id="{{c.id_stock}}"
                                                       ng-value="c.id_stock"
                                                       ng-model="c.selected">
                                                <label>{{c.lib}}</label>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="row" style="margin-top: 30px;">
                        <table class="style-1">
                            <tr>
                                <td>Liste des articles favoris:</td>
                                <td>
                                    <button type="button" class="btn btn-primary" ng-click="moteurArticles()">
                                        <i class="fa fa-add"></i> <?php _e_html(114039,"Ajouter des articles"); ?>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td></td>
                                <td>
                                    <div class="card-list">
                                        <div class="card-article" ng-repeat="(id, article) in data.articles">
                                            <div class="card-remove">
                                                <a ng-click="removeArticle(id)">
                                                    <i class="fa fa-remove fa-lg"></i>
                                                </a>
                                            </div>
                                            <div class="card-image">
                                                <a href="#/page.php/Article/Standard/Visualisation/home:{{ id }}" target="_blank">
                                                    <img ng-if="article.img != null" ng-src="{{ article.img }}">
                                                    <i ng-if="article.img == null" class="lm-picture" style="font-size: 42px;"></i>
                                                </a>
                                            </div>
                                            <div class="card-price">
                                                <div class="badge">
                                                    <span lmb-currency="suffixe:sigle; dec:<?php echo lmbconfig::getInstance()->get('CAT_nb_decimales')?>">{{ article.pu }}</span>
                                                </div>
                                            </div>
                                            <div class="card-content">
                                                <div class="card-title">
                                                    <a href="#/page.php/Article/Standard/Visualisation/home:{{ id }}" target="_blank">
                                                        {{ article.lib }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <br/>
                <div class="portlet-header">
                    <h1>Annuaire</h1>
                </div>
                <br/>
                <div class="portlet-body">
                    <div class="row">
                        <table class="style-1 col-1-2">
                            <tr>
                                <td>Catégories de clients:</td>
                                <td>
                                    <div ng-if="data.categs_clients" class="dropdown dropdown-checkbox input-full">
                                        <div ng-if="data.categs_clients" class="dropdown-button" ng-bind="'Toutes'"></div>
                                        <ul ng-if="data.categs_clients" class="dropdown-menu">
                                            <li ng-repeat="categ in data.categs_clients">
                                                <input type="checkbox"
                                                       class="input-large"
                                                       ng-value="categ.id_client_categ"
                                                       ng-model="categ.selected">
                                                <label class="input-large">{{categ.lib_client_categ}}</label>
                                            </li>
                                        </ul>
                                    </div>
                                    <span ng-if="!data.categs_clients">
                                        Pas de catégories clients
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td>Centres de profits des clients:</td>
                                <td>
                                    <div ng-if="data.magasins" class="dropdown dropdown-checkbox input-full">
                                        <div class="dropdown-button" ng-bind="'Tous'"></div>
                                        <ul class="dropdown-menu">
                                            <li ng-repeat="magasin in data.magasins">
                                                <input type="checkbox"
                                                       class="input-large"
                                                       ng-value="magasin.id_magasin"
                                                       ng-model="magasin.selected">
                                                <label class="input-large">{{magasin.lib_magasin}}</label>
                                            </li>
                                        </ul>
                                    </div>
                                    <span ng-if="!data.magasins">
                                        Pas de centres de profit
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>

    (function ($) {
        LMBTools.require({
            traductions: [[114019, 114021]],
            onReady: function(){
                angular.bootstrap($("#page-caisse-new")[0], ["CaisseApp"]);
            }
        });

    })(jQuery);
</script>

<?php

use LMBCore\Article\Modele\materiel;
use LMBCore\Contacts\Adresse;

class export_cmde_cli_expedition {

    private $params;

    public function __construct($params) {
        $this->params = $params;
    }

    public function getParams() {
        return $this->params;
    }

    private function getStructure() {
        $structure = [];

        $structure["LIGNE01"] = [
            "A" => ["complex_name" => "Date de la commande"],
            "B" => ["complex_name" => "Référence de commande"],
            "C" => ["complex_name" => "Libellé de l’article"],
            "D" => ["complex_name" => "Quantité commandé"],
            "E" => ["complex_name" => "Prix de vente TTC"],
            "F" => ["complex_name" => "Poids de l'Article (en kg)"],
            "G" => ["complex_name" => "EAN de l'Article"],
            "H" => ["complex_name" => "Pays d'origine de l'Article (code ISO)"],
            "I" => ["complex_name" => "Référence Interne"],
            "J" => ["complex_name" => "Prénom"],
            "K" => ["complex_name" => "Nom"],
            "L" => ["complex_name" => "Société (si concerné)"],
            "M" => ["complex_name" => "Email lié à l'adresse de livraison"],
            "N" => ["complex_name" => "Téléphone lié à l'adresse de livraison"],
            "O" => ["complex_name" => "Adresse  de livraison Ligne 1"],
            "P" => ["complex_name" => "Adresse livraison (ligne 2)"],
            "Q" => ["complex_name" => "Complément d'Adresse"],
            "R" => ["complex_name" => "Code Postal livraison "],
            "S" => ["complex_name" => "Ville livraison "],
            "T" => ["complex_name" => "Etat / Région"],
            "U" => ["complex_name" => "Pays (Code ISO)"]
        ];

        $structure["LIGNE02"] = [];
        foreach ($structure['LIGNE01'] as $key => $colonne) {
            $structure["LIGNE02"][$key] = [];
        }

        return $structure;
    }

    public function export($titre) {
        if (empty($this->params['ref_doc'])) {
            die("Aucune commande à traiter");
        }

        $fichier = new tools_fichier_plat($titre . ".csv", $this->getStructure(), "csv");
        $fichier->encoding = "UTF-8";
        $fichier->force_upper = false;
        $fichier->filtredata = false;

        $fichier->write_head_line('LIGNE01');

        $lst_docs = explode(";", $this->params['ref_doc']);
        foreach ($lst_docs as $ref_doc) {
            $cdc = document::getTypeInstance($ref_doc);
            $contact = $cdc->getContact();

            $email_livraison = "";
            $tel_livraison = "";
            if ($cdc->getId_coord_livraison()) {
                $coord = new \LMBCore\Contacts\Coordonnee($cdc->getId_coord_livraison());
                $email_livraison = $coord->getEmail() ?? "";
                $tel_livraison = $coord->getTel1() ?? $coord->getTel2() ?? "";
            }
            if (empty($email_livraison)) {
                $email_livraison = $cdc->getEmail_livraison();
            }
            if (empty($tel_livraison)) {
                $coords = $contact->getCoordonnees();
                foreach ($coords as $co) {
                    $tel_livraison = $co->getTel1() ?? $co->getTel2() ?? "";
                    if (!empty($tel_livraison)) break;
                }
            }

            $docs_lines = $cdc->getContenu();
            foreach ($docs_lines as $doc_line) {
                if(in_array($doc_line->type_of_line ,["information","soustotal"])) continue;
                $art_ref_interne = article::getRefInterne($doc_line->ref_article);
                $article = article::getInstance($doc_line->ref_article);
                $params_deb = $article->GestionComptabilite->get_params_deb();
                $pays_origine = $params_deb ? $params_deb->article_pays_origine : "";


                $datas = [];

                $datas["LIGNE02"]["A"] = $cdc->getDate_creation();
                $datas["LIGNE02"]["B"] = $ref_doc;
                $datas["LIGNE02"]["C"] = $doc_line->lib_article;
                $datas["LIGNE02"]["D"] = $doc_line->qte;
                $datas["LIGNE02"]["E"] = $doc_line->pu_ttc ?? 0;
                $datas["LIGNE02"]["F"] = $article->getModele() == 'materiel' ? $article->getPoids() : 'N/A';
                $datas["LIGNE02"]["G"] = implode(', ', array_map(fn($c) => $c->code_barre, $article->getCodes_barres()));
                $datas["LIGNE02"]["H"] = Adresse::getCodePays($pays_origine,true);
                $datas["LIGNE02"]["I"] = $art_ref_interne ?: $doc_line->ref_article;
                $datas["LIGNE02"]["J"] = $contact->getPrenom();
                $datas["LIGNE02"]["K"] = $contact->getNom();
                $datas["LIGNE02"]["L"] = $contact->getRaison_sociale();
                $datas["LIGNE02"]["M"] = $email_livraison;
                $datas["LIGNE02"]["N"] = $tel_livraison;
                $datas["LIGNE02"]["O"] = $cdc->getText_adresse_livraison(1);
                $datas["LIGNE02"]["P"] = $cdc->getText_adresse_livraison(2);
                $datas["LIGNE02"]["Q"] = $cdc->getText_adresse_livraison(3);
                $datas["LIGNE02"]["R"] = $cdc->getCode_postal_livraison();
                $datas["LIGNE02"]["S"] = $cdc->getVille_livraison();
                $datas["LIGNE02"]["T"] = $cdc->getEtat_livraison();
                $datas["LIGNE02"]["U"] = Adresse::getCodePays($cdc->getId_pays_livraison(),true);

                $fichier->write_lines($datas);
            }
        }

        $fichier->telecharger_fichier();
        if (file_exists($fichier->file)) {
            @unlink($fichier->file);
        }
    }
}

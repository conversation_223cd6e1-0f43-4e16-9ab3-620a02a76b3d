<ul class="choix_fax" style="width:100%">
<?php
	$is_ok = false;
	if (is_array($liste_fax) && !empty($liste_fax)){
		foreach ($liste_fax as $key => $fax) {
			if ($fax->fax == "" ) {continue;}
			$is_ok = true;
			?>
			<li class="complete_ville" id="fax_<?php echo $key;?>"><?php echo $fax->fax;?></li>
			<?php 
		}
	}
	if(empty($is_ok)) {
		echo langage::write("aucun_fax_defini"); 
	}
	
?>
</ul>

<script type="text/javascript">
<?php 
if (is_array($liste_fax) && !empty($liste_fax)){
	foreach ($liste_fax as $key => $fax) {
		if ($fax->fax == "" ) {continue;} ?>
			Event.observe("fax_<?php echo $key;?>", "mouseout",  function(){changeclassname ("fax_<?php echo $key;?>", "complete_ville");}, false);

			Event.observe("fax_<?php echo $key;?>", "mouseover",  function(){changeclassname ("fax_<?php echo $key;?>", "complete_ville_hover");}, false);
			Event.observe("fax_<?php echo $key;?>", "click",  function(){
				LMBModal.getInstance("popup_envoyer_fax").close();
				var AppelAjax = new Ajax.Request(
					"documents_contact_fax_send_doc.php", 
					{parameters: {ref_doc: "<?php echo $document->getRef_doc()?>", destinataires: "<?php echo $fax->fax;?>", titre: "", message: ""},
					evalScripts:true, 
					onLoading:S_loading, onException: function () {S_failure();}, 
					onComplete: function(requester) {
										H_loading(); 
										requester.responseText.evalScripts();

									}
					}
				);
			}, false);
			<?php
	}
}

if(empty($is_ok)) {
?>
	$j(".choix_fax").click(function() {
				LMBModal.getInstance("popup_envoyer_fax").close();
	});
<?php
}
?>

//on masque le chargement
H_loading();

</script>
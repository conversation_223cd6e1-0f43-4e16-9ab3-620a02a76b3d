app.controller('GridController', ["$scope", "OpportuniteService", "LMBModalService", function ($scope, OpportuniteService, LMBModalService) {

    var callbackGetAllDatas = function (datas) {
        $scope.datas = datas;

        $scope.sortableOptionsList = [];

        angular.forEach($scope.datas.pipelines, function (pipe) {
            for (var i = 0; i < pipe.type.steps.length; i++) {
                var options = {
                    placeholder: "card ui-sortable-placeholder",
                    connectWith: ".card-container",
                    activate: function () {
                    },
                    beforeStop: function () {
                    },
                    change: function () {
                    },
                    create: function () {
                    },
                    deactivate: function () {
                    },
                    out: function () {
                    },
                    over: function () {
                    },
                    receive: function () {
                    },
                    remove: function () {
                    },
                    sort: function (e, ui) {
                        var item = ui.item[0];

                        if (typeof left_pos === 'undefined') {
                            left_pos = e.pageX;
                        }
                        if (e.pageX >= left_pos) {
                            $j(item).addClass("rot-right");
                            $j(item).removeClass("rot-left");
                        } else {
                            $j(item).addClass("rot-left");
                            $j(item).removeClass("rot-right");
                        }
                        left_pos = e.pageX;
                    },
                    start: function (ee, ui) {
                        $j(ui.item[0]).addClass('tilt');
                    },
                    stop: function (ee, ui) {
                        if(ui.item.sortable.droptarget !== undefined) {
                            id_statut = $j(ui.item.sortable.droptarget[0]).attr('data-id-statut');
                            $j(ui.item[0]).removeClass('tilt');
                            $j(ui.item[0]).removeClass('rot-right');
                            $j(ui.item[0]).removeClass('rot-left');
                            OpportuniteService.updateTree(id_statut, ui.item.sortable.model.id_card);
                        }else{
                            $j(ui.item[0]).removeClass('tilt');
                            $j(ui.item[0]).removeClass('rot-right');
                            $j(ui.item[0]).removeClass('rot-left');
                        }
                    },
                    update: function (event, ui) {
                    }
                };
                $scope.sortableOptionsList.push(options);
            }

        });
    };

    OpportuniteService.getAllDatas().then(callbackGetAllDatas);


    $scope.getTotalAmount = function (index_step, idType) {
        var total = 0.0;
        var step = $scope.datas.pipelines[idType].type.steps[index_step];

        angular.forEach(step.cards, function (card, key) {
            total += parseFloat(card.amount);
        });

        return total;
    };

    $scope.getAvancementPourcentage = function (index_step, idType) {
        var total = $scope.getTotalAmount(index_step, idType);

        var result = parseInt(100 - (100 * (($scope.datas.pipelines[idType].objectif - total) / $scope.datas.pipelines[idType].objectif)));

        return result;
    };

    $scope.manageEvent = function (id_card) {
        var pp_manage_event = new LMBModal("pp_manage_event", {
            titre: 'Saisir ou planifier une action',
            drag: true,
            resizable: true,
            overlay: true,
            style: {width: "900px", height: "700px"}
        });
        pp_manage_event.request('page.php/Opportunite/Standard/Standard/ManageEvent', {id_card: id_card}, {type: "get"});
        pp_manage_event.open();
    };

    $scope.gestionAffaire = function (id_card, etat) {
        OpportuniteService.changeState(id_card, etat).then(function (result) {
            if (result.data.resultat) {
                $scope.deleteCard(id_card);
                if (etat === 1) {
                    LMBToast.success({
                        title: "Affaire gagnée !",
                        message: "L'action s'est déroulée avec succès !"
                    });
                } else {
                    LMBToast.success({
                        title: "Affaire perdue !",
                        message: "L'action s'est déroulée avec succès !"
                    });
                }
            }
            else {
                LMBTools.alert({content: "Un problème technique a empeché le bon deroulement de l'action."});
            }
        });
    };

    $scope.deleteCard = function (id_card) {

        angular.forEach($scope.datas.pipelines, function (pipe, keyPipe) {
            angular.forEach(pipe.type.steps, function (steps, keyStep) {
                angular.forEach(steps.cards, function (card, keyCard) {
                    if (card.id_card == id_card) {
                        $scope.datas.pipelines[keyPipe].type.steps[keyStep].cards.splice(keyCard, 1)
                    }
                });
            });
        });

    };

    $scope.newOpp = function (typeOpp,statut) {
        LMBModalService.showModal({
            LMBModalConfig: {
                titre: __(140259, "Création rapide d'opportunité"),
                drag: true,
                resizable: false,
                overlay: true,
                style: LMBModal.SIZE_LARGE
            },
            angularModal: {
                config: {
                    templateUrl: "page.php/Opportunite/Standard/Standard/displayNewOpp",
                    controller: "OpportuniteAddController",
                    inputs: {
                        fromScope: $scope,
                        typeOpp: typeOpp,
                        delais: $scope.datas.pipelines[typeOpp].type.delais,
                        statutOpp: statut
                    }
                },
                then: function (modal) {
                },
                onClose: function (result) {

                }
            }
        });
    };

    //appel les données des opportunités pour les utiliser dans le pipe
    loadCards = function (type) {
        OpportuniteService.getAllDatas(type).then(function (result) {
            if (result) {
                $scope.datas = result;
            }
            else {
                LMBTools.alert({content: "Un problème technique a empeché le bon deroulement de l'action."});
            }
        });
    };


    loadCards(1);
}])
    .controller('OpportuniteAddController', ["$scope", "OpportuniteService", "typeOpp", "delais", "statutOpp", "close", function ($scope, OpportuniteService, typeOpp, delais, statutOpp, close) {

        $scope.opp = {};
        $scope.delais = delais;

        $scope.quickNewOpp = function () {

            $scope.opp.etat = "encours";
            var type = {id_opportunite_type:typeOpp}
            $scope.opp.type = type;
            $scope.opp.previous = 'encours';
            $scope.opp.id_opportunite_statut = statutOpp;
            $scope.opp.noredirect = true;

            if(!$scope.contactSelected.isEmpty()) {
                // __Récupération des données sur le client sélectionné
                $scope.opp.nom_complet = $scope.contactSelected.nom_complet;
                $scope.opp.phone = $scope.contactSelected.phone;
                $scope.opp.email = $scope.contactSelected.email;
                $scope.opp.id_type = $scope.contactSelected.id_type;
                $scope.opp.id_categorie = $scope.contactSelected.id_categorie;
                $scope.opp.id_contact = $scope.contactSelected.id;
            }
            else {
                // __Récupération des données du formulaire
                $scope.opp.nom_complet = $scope.search.form.nom_complet || '';
                $scope.opp.phone = $scope.search.form.phone || '';
                $scope.opp.email = $scope.search.form.email || '';
                $scope.opp.id_type = $scope.search.form.id_type || '';
                $scope.opp.id_categorie = $scope.search.form.id_categorie || '';
                $scope.opp.id_contact = null;
            }

            OpportuniteService.save($scope.opp).then(function () {
                close();
                loadCards(typeOpp);
            });
        };
    }]);
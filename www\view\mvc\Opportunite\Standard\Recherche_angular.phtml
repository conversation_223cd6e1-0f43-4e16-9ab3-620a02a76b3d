<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/upload/upload.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>
<script src="<?= $DIR ?>view/mvc/javascript/angular/composants/pagination/pagination.directive.js?rev=<?= lmbconfig::getSysVersion() ?>"></script>

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Opportunite/Standard/javascript/_javascript_loader.php");
?>

<div id="page-opportunite-search" class="lmb-theme">

    <div ng-app="OpportuniteRecherche" ng-controller="RechercheController" ng-init='init(<?= json_encode($this->view->params); ?>)'>

        <header class="jumbotron">
            <div class="container">
                <div class="jumbotron-title">
                    <div class="jumbotron-heading"><?php _e_html(140110,"Ventes"); ?></div>
                    <h1><?php _e_html(140111,"Recherche des opportunités"); ?></h1>
                </div>
                <div class="jumbotron-actions">
                    <a qa_id="740588" href="#page.php/Opportunite/Standard/Standard/CreationEdition" class="btn btn-white">
                        <i class="fa fa-plus"></i> <?php _e_html(140112,"Nouvelle opportunité"); ?>
                    </a>
                    <div class="dropdown">
                        <button class="btn btn-white light icon-right dropdown-button" type="button">
                            <?php _e_html(140233,"Menu"); ?>
                            <i class="fa fa-chevron-down"></i>
                        </button>

                        <ul class="dropdown-menu">
                            <li ng-click="searchMode = 'simple'">
                                <?php _e_html(140234,"Recherche"); ?>
                            </li>
                            <li ng-click="searchMode = 'avancee'">
                                <?php _e_html(140235,"Recherche avancée"); ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </header>

        <div class="container">

            <div class="portlet-tab style-2">

                <ul id="type_opp">
                    <li ng-repeat="(key,type) in searchParams.listes.types" ng-class="(key===searchParams.listes.keys[0])?'selected':''">

                        <a ng-click="changeTab(type.value)" data-lmb-tab='#tab-{{type.value}}'>{{type.lib}}</a>

                    </li>
                </ul>

            </div>

                <div ng-repeat="(key,type) in searchParams.listes.types" id="tab-{{type.value}}" ng-class="(key!==searchParams.listes.keys[0])?'hidden':''" class="portlet" ng-if="key===tab">
                    <div class="portlet-header">
                        <h1><?php _e_html(140113,"Recherche"); ?></h1>
                    </div>

                    <form id="form_recherche_opportunite_avancee" ng-submit="search()" class="ng-pristine ng-valid">

                        <div class="portlet-body row">
                            <div class="col-1-2">

                                <table class="style-1">
                                    <tr ng-repeat="(key,critere) in searchCriteres">
                                        <td ng-if="critere.name === 'id_opportunite_type'" style="display: none;"><input type="hidden" value="{{type.value}}" name="id_opportunite_type" ng-model="critere.value"></td>
                                        <td ng-if="critere.name === 'id_contact_client'"><?php _e_html(140114,"Client"); ?></td>
                                        <td ng-if="critere.name === 'id_contact_client'">
                                            <div class="input-group input-group-justified input-large">
                                                <input type="text" name="id_contact_client" class="input-contact-v2" ng-model="critere.value">
                                            </div>
                                        </td>
                                        <td ng-if="critere.name === 'id_opportunite_statut'"><?php _e_html(140115,"Statut"); ?></td>
                                        <td ng-if="critere.name === 'id_opportunite_statut'">
                                            <select name="id_statut" id="id_opportunite_statut" class="input-large" ng-model="critere.value">
                                                <option value=""></option>
                                                <option ng-repeat="statut in searchParams.listes.status[1]" ng-if="statut.id_opportunite_statut !== '? object:null ?'" value="{{statut.id_opportunite_statut}}">{{statut.lib}}</option>
                                            </select>
                                        </td>
                                        <td ng-if="critere.name === 'etat'"><?php _e_html(140116,"Etat"); ?></td>
                                        <td ng-if="critere.name === 'etat'">
                                            <select qa_id="740640" name="etat" id="etat" class="input-large" ng-model="critere.value">
                                                <option qa_id="740641" value=""></option>
                                                <option ng-repeat="(key,etat) in searchParams.listes.etats" ng-if="key !== '? object:null ?'" value="{{key}}" ng-selected="key==='encours'">{{etat}}</option>
                                            </select>
                                        </td>
                                    </tr>
                                </table>

                            </div>
                            <div class="col-1-2">

                                <table class="style-1">

                                    <tr>
                                        <td><?php _e_html(140117,"Création"); ?></td>
                                        <td>
                                            <div class="input-group">
                                                <label for="date_debut_crea" class="highlight"><?php _e_html(140120,"Du"); ?></label>
                                                <input type="text" name="date_debut_crea" class="date_debut_crea" lmb-date ng-model="date_debut_crea"/>
                                                <label for="date_fin_crea" class="highlight"><?php _e_html(140122,"au"); ?></label>
                                                <input type="text" name="date_fin_crea" class="date_fin_crea" lmb-date ng-model="date_fin_crea"/>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><?php _e_html(140118,"Echéance"); ?></td>
                                        <td>
                                            <div class="input-group">
                                                <label for="date_debut_echeance" class="highlight"><?php _e_html(140121,"Du"); ?></label>
                                                <input type="text" name="date_debut_echeance" class="date_debut_echeance" lmb-date ng-model="date_debut_echeance"/>
                                                <label for="date_fin_echeance" class="highlight"><?php _e_html(140123,"au"); ?></label>
                                                <input type="text" name="date_fin_echeance" class="date_fin_echeance" lmb-date ng-model="date_fin_echeance"/>
                                            </div>
                                        </td>
                                    </tr>
                                    <!--<tr ng-repeat="(key, carac) in searchParams.listes.caracs">
                                        <td></td>
                                        <td>
                                            <carac config="{declinaison:false}" carac="carac" action="search"></carac>
                                        </td>
                                    </tr>-->
                                </table>
                                <!--<div ng-repeat="(key, carac) in searchParams.listes.caracs">
                                    <carac config="{declinaison:false}" carac="carac" action="search"></carac>
                                </div>-->

                            </div>
                        </div>
                        <div class="portlet-footer">
                            <input type="hidden" name="id_form" value="form_recherche_opportunite_avancee">

                            <button qa_id="740583" type="submit" class="btn btn-secondary"><?php _e_html(140119,"Rechercher"); ?></button>
                        </div>

                    </form>
                </div>

            <div id="opportunite_result" ng-if="opps.length">

                <table class="style-2">
                    <thead>
                    <tr style="height: 40px;">
                        <th style="width:50px;" class="text-left"></th>
                        <th ng-repeat="result in searchResults" ng-if="result.actif && result.name != 'id_contact_client'" ng-class="result.align ? 'text-'+result.align : 'text-left'">
                            {{result.name == 'image' || result.name == 'email' ? '' : result.lib_short}}
                        </th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="opp in opps">
                        <td class="text-center">
                            <input type="checkbox" class="checkbox_selection">
                        </td>
                        <td ng-repeat="result in searchResults" ng-if="result.actif && result.name != 'id_contact_client'" ng-class="result.align ? 'text-'+result.align : 'text-left'" ng-switch="result.name">
                            <span ng-switch-when="date_creation"><span lmb-date>{{opp.date_creation ? opp.date_creation : ''}}</span></span>
                            <span ng-switch-when="nom_complet" class="bold">
                                <a ng-if="opp.id_contact_client" ng-href="#page.php/Contact/Standard/Profil/Client:{{opp.id_contact_client}}" target="_blank">{{opp.nom_complet ? opp.nom_complet : ''}}</a>
                                <span ng-if="!opp.id_contact_client">{{opp.nom_complet ? opp.nom_complet : ''}}</span>
                            </span>
                            <span qa_id="740614" ng-switch-when="lib">{{opp.lib ? opp.lib : ''}}</span>
                            <span ng-switch-when="lib_statut">{{opp.lib_statut ? opp.lib_statut : ''}}</span>
                            <span ng-switch-when="budget"><span lmb-currency>{{opp.budget ? opp.budget : ''}}</span> <?php echo devise::getDefaut()->getSigle();?></span>
                        </td>
                        <td class="text-right">
                            <!--<a ng-if="contact.email" href="mailto:{{contact.email}}" class="btn btn-default rounded icon" data-lmb-infobulle="<?php _e(111500,"Envoyer un mail"); ?>">
                                <i class="fa fa-envelope-o"></i>
                            </a>-->
                            <a qa_id="740613" ng-href="#page.php/Opportunite/Standard/Standard/Visualisation:{{opp.id_opportunite}}" target="_blank" class="btn btn-primary rounded">
                                <i class="fa fa-search"></i>
                                <?php _e_html(140231,"Voir"); ?>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="100%">
                            <div id="search-contacts-actions"></div>
                        </td>
                    </tr>
                    </tbody>

                </table>

                <pagination-form form="form_recherche_opportunite_avancee"></pagination-form>

            </div>

            <div ng-if="opps && !opps.length" class="portlet text-center" style="border: 1px solid lightgrey; padding: 20px;">
                <?php _e_html(140232,"Aucune opportunité de ce type ne correspond à vos critères."); ?>
            </div>

        </div>

    </div>

</div>
<script>

    (function($) {

        var app = $("*[ng-app]");
        angular.bootstrap(app[0], [app.attr('ng-app')]);

    })(jQuery);

</script>
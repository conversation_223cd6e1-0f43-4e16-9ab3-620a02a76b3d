'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .controller('LivraisonsController', LivraisonsController);

    LivraisonsController.$inject = ['$scope', 'TransporteursLivraisonsApi', 'transporteurs','infos', '$state','commonMethodsService'];

    function LivraisonsController($scope, TransporteursLivraisonsApi, transporteursActifs , infos, $state, commonMethodsService) {
        $scope.infos = infos;
        // Attribution au scope des valeurs de transporteurs, pays, langages et de la méthode updateChampEtat()
        commonMethodsService.transporteursListeCommun($scope,transporteursActifs,TransporteursLivraisonsApi);

        /**
         * Sélection automatique du premier élément
         **/
        if ($scope.transporteurs.length && $state.current.name === 'livraisons') {
            LMBToast.info({
                title: "Nom des variables en BD",
                message: "Survollez les points d'interrogation à côté de chaque nom de variable spécifique pour connaître son équivalent réel en BD pour le mode. Certains champs peuvent aussi être survollés pour obtenir des infos supplémentaires."
            });

            LMBNavigation.ignoreNextHashChange();
            $state.go('.liste', {idModule: transporteursActifs.liste[0].id_transport_module});
        }
    }

})();
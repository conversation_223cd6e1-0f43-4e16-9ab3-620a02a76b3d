<?php
ini_set("display_errors", 1);
error_reporting(E_ALL);
function rmdir_recursive($dir)
{
    $dir_content = scandir($dir);
    if($dir_content !== FALSE){
        foreach ($dir_content as $entry)
        {
            if(!in_array($entry, array('.','..'))){
                $entry = $dir . '/' . $entry;
                if(!is_dir($entry)){
                    unlink($entry);
                }
                else{
                    rmdir_recursive($entry);
                }
            }
        }
    }
    rmdir($dir);
}
function secure_unlink($file){

    if (is_file($file)){
        unlink ($file);
        return true;
    }
    return false;
}
set_time_limit(0);
$maj_log_file_name = $DIR."echange_lmb/maj_lmb_".$new_version."_log.txt";
$maj_log_file = fopen($maj_log_file_name, "a");
fwrite($maj_log_file,"********************************** ".date("d/m/Y - H:i")." **********************************\n");
fwrite($maj_log_file," Debut de Maj LMB vers ".$new_version."\n");
$tmp_files_dir = $DIR."echange_lmb/maj_lmb_".$new_version."/";
$GLOBALS['_ALERTES'] = array();
$GLOBALS['_INFOS']['maj_actions'][] = "<i>Ajout de tâches administrateur</i>";
$taches_admin = array();
$query_maj_sql = array();
$query_maj_sql_ne = array();
$pdo = new PDO("mysql:host=$bdd_hote;dbname=$bdd_base", $bdd_user, $bdd_pass, array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8;"));
$pdo->setAttribute (PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
$pdo->setAttribute (PDO::ATTR_EMULATE_PREPARES, true);
$pdo->setAttribute (PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
$query = "DROP PROCEDURE IF EXISTS `lmb_addcolumn`";
$pdo->exec($query);
$query = "create procedure lmb_addcolumn(IN tbl TEXT, IN colone TEXT, IN alt TEXT)
begin
    IF NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
            AND convert(COLUMN_NAME USING utf8) = convert(colone USING utf8)
            AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
        SET @Sql = alt;
        PREPARE STMT FROM @Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    END IF;
end";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `lmb_dropcolumn`";
$pdo->exec($query);
$query = "CREATE PROCEDURE lmb_dropcolumn(IN tbl TEXT, IN col TEXT)
BEGIN
    IF EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
            AND convert(COLUMN_NAME USING utf8) = convert(col USING utf8)
            AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
        SET @Sql := concat( 'ALTER TABLE ', convert(tbl USING utf8), ' DROP COLUMN ', convert(col USING utf8) );
        PREPARE STMT FROM @Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    END IF;
END";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `lmb_renamecolumn`";
$pdo->exec($query);
$query = "create procedure lmb_renamecolumn(IN tbl TEXT, IN oldcolone TEXT, IN newcolone TEXT, IN alt TEXT)
    begin
        IF NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
                AND convert(COLUMN_NAME USING utf8) = convert(newcolone USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci)
            AND EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
                AND convert(COLUMN_NAME USING utf8) = convert(oldcolone USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
            SET @Sql = alt;
            PREPARE STMT FROM @Sql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END IF;
    end";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `lmb_renametable`";
$pdo->exec($query);
$query = "create procedure lmb_renametable(IN oldtbl TEXT, IN newtbl TEXT, IN alt TEXT)
    begin
        IF NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(newtbl USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci)
            AND EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(oldtbl USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
            SET @Sql = alt;
            PREPARE STMT FROM @Sql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END IF;
    end";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `DropFK_Name`";
$pdo->exec($query);
$query = "CREATE PROCEDURE DropFK_Name (
IN parm_table_name VARCHAR(100),
IN parm_key_name VARCHAR(100)
)
BEGIN
-- Verify the foreign key exists
IF EXISTS (SELECT NULL FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_SCHEMA = DATABASE() AND CONSTRAINT_NAME = convert(parm_key_name USING utf8) COLLATE utf8_unicode_ci) THEN
-- Turn the parameters into local variables
set @ParmTable = parm_table_name ;
set @ParmKey = parm_key_name ;
-- Create the full statement to execute
set @StatementToExecute = concat('ALTER TABLE ',@ParmTable,' DROP FOREIGN KEY ',@ParmKey);
-- Prepare and execute the statement that was built
prepare DynamicStatement from @StatementToExecute ;
execute DynamicStatement ;
-- Cleanup the prepared statement
deallocate prepare DynamicStatement ;
END IF;
END";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `DropFK`";
$pdo->exec($query);
$query = "CREATE PROCEDURE DropFK ( IN table_name VARCHAR(100), IN column_name VARCHAR(100), IN referenced_table VARCHAR(100), IN referenced_column VARCHAR(100) )
    BEGIN
        DECLARE done INT DEFAULT 0;
        DECLARE key_table_name VARCHAR(100);
        DECLARE key_name VARCHAR(100);
        DECLARE curseur1 CURSOR FOR SELECT k.TABLE_NAME,k.CONSTRAINT_NAME
                                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS k
                                        INNER JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS c
                                        ON k.CONSTRAINT_SCHEMA = c.CONSTRAINT_SCHEMA AND k.CONSTRAINT_NAME = c.CONSTRAINT_NAME
                                        WHERE c.CONSTRAINT_TYPE = 'FOREIGN KEY'
                                        AND k.CONSTRAINT_SCHEMA = DATABASE()
                                        AND k.TABLE_NAME = convert(table_name USING utf8) COLLATE utf8_unicode_ci
                                        AND k.COLUMN_NAME = convert(column_name USING utf8) COLLATE utf8_unicode_ci
                                        AND k.REFERENCED_TABLE_NAME = convert(referenced_table USING utf8) COLLATE utf8_unicode_ci
                                        AND k.REFERENCED_COLUMN_NAME = convert(referenced_column USING utf8) COLLATE utf8_unicode_ci;

        DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

        OPEN curseur1;
        REPEAT
                FETCH curseur1 INTO key_table_name,key_name;
                IF done = 0 THEN
                        SET @SQL := concat('ALTER TABLE ',key_table_name,' DROP FOREIGN KEY ',key_name);
                        PREPARE stmt FROM @SQL;
                        EXECUTE stmt;
                        DEALLOCATE PREPARE stmt;
                END IF;
        UNTIL done
        END REPEAT;
        CLOSE curseur1;
    END";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `AddFK`";
$pdo->exec($query);
$query = "CREATE PROCEDURE AddFK ( IN parm_table_name VARCHAR(100), IN parm_key_name VARCHAR(100), IN alt TEXT)
  BEGIN
    IF NOT EXISTS (SELECT NULL
                    FROM information_schema.TABLE_CONSTRAINTS
                    WHERE CONSTRAINT_SCHEMA = DATABASE()
                        AND CONSTRAINT_NAME = convert(parm_key_name USING utf8) COLLATE utf8_unicode_ci
                        AND TABLE_NAME = convert(parm_table_name USING utf8) COLLATE utf8_unicode_ci) THEN
      SET @SQL := alt;
      PREPARE stmt FROM @SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
    END IF;
  END";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `DropIndex`";
$pdo->exec($query);
$query = "CREATE PROCEDURE DropIndex ( IN table_name VARCHAR(100), IN columns_names VARCHAR(100) )
	BEGIN
		DECLARE done INT DEFAULT 0;
		DECLARE index_table_name VARCHAR(100);
		DECLARE index_name VARCHAR(100);
		DECLARE columns_concat VARCHAR(100);
		DECLARE curseur1 CURSOR FOR SELECT k.TABLE_NAME,k.INDEX_NAME, GROUP_CONCAT(k.COLUMN_NAME ORDER BY k.COLUMN_NAME ASC) AS columns
										FROM INFORMATION_SCHEMA.STATISTICS AS k
										WHERE k.TABLE_SCHEMA = DATABASE()
										AND k.TABLE_NAME = convert(table_name USING utf8) COLLATE utf8_unicode_ci
										AND k.INDEX_NAME != 'PRIMARY'
										GROUP BY k.TABLE_NAME,k.INDEX_NAME
										HAVING columns = convert(columns_names USING utf8) COLLATE utf8_unicode_ci;

        DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

        OPEN curseur1;
        REPEAT
                FETCH curseur1 INTO index_table_name,index_name,columns_concat;
                IF done = 0 THEN
						SET @SQL := concat('ALTER TABLE ',index_table_name,' DROP INDEX ',index_name);
                        SELECT @SQL;
						PREPARE stmt FROM @SQL;
						EXECUTE stmt;
						DEALLOCATE PREPARE stmt;
                END IF;
        UNTIL done
        END REPEAT;
        CLOSE curseur1;
	END";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `AddIndex`";
$pdo->exec($query);
$query = "CREATE PROCEDURE AddIndex ( IN nom_table VARCHAR(100), IN nom_index VARCHAR(100), IN liste_colonnes VARCHAR(100) )
  BEGIN

    DECLARE existant INT DEFAULT 1;

    SELECT count(1) INTO existant FROM (
        SELECT 
            k.TABLE_NAME,
            k.INDEX_NAME, 
            GROUP_CONCAT(k.COLUMN_NAME ORDER BY k.COLUMN_NAME ASC) AS columns
        FROM INFORMATION_SCHEMA.STATISTICS AS k
        WHERE k.TABLE_SCHEMA = DATABASE()
              AND k.TABLE_NAME = convert(nom_table USING utf8) COLLATE utf8_unicode_ci
              AND INDEX_NAME = convert(nom_index USING utf8)
        GROUP BY k.TABLE_NAME,k.INDEX_NAME
        HAVING columns = convert(liste_colonnes USING utf8) COLLATE utf8_unicode_ci
    ) as indexes;


    IF existant = 0 THEN

      SET @SQL := concat('ALTER TABLE ',nom_table,' ADD INDEX ',nom_index,' (',liste_colonnes,')');
      SELECT @SQL;
      PREPARE stmt FROM @SQL;
      EXECUTE stmt;
      DEALLOCATE PREPARE stmt;
    END IF;

  END";
$pdo->exec($query);

lmbconfig::$silent_mode=true;
// *!*!*!*!*!*!*!*!*!* A EXECUTER AVANT LE BASE UPDATE 1 *!*!*!*!*!*!*!*!*!*\\

// *!*!*!*!*!*!*!*!*!* BASE UPDATE *!*!*!*!*!*!*!*!*!*\\
$query_maj_sql = maj_serveur::_parse_sql_file($tmp_files_dir."maj.sql");
try{
    $pdo->beginTransaction();

    foreach ($query_maj_sql as $q) {
        $current_query = $q;
        $pdo->query(trim($q));
    }
    $pdo->commit();
}
catch(Exception $e){
    $pdo->rollback();
    fwrite($maj_log_file,"Erreur lors de l'execution des requetes SQL 1:\n");
    fwrite($maj_log_file,"Erreur N°:".$e->getCode()."\n");
    fwrite($maj_log_file,"Erreur :".$e->getMessage()."\n");
    fwrite($maj_log_file,"Fichier :".$e->getFile()."\n");
    fwrite($maj_log_file,"Ligne :".$e->getLine()."\n");
    fwrite($maj_log_file,"Trace :".$current_query."\n");
    alerte_dev("Erreur lors de la mise à jour de la base de données.");
}
lmbconfig::$silent_mode=true;
// *!*!*!*!*!*!*!*!*!* A EXECUTER APRES LE BASE UPDATE 1 *!*!*!*!*!*!*!*!*!*\\


$rovercash_gl_gax_bin = [
    ["type" => "EmployeProvi", "value" => "c013"],
    ["type" => "EmployeCadre", "value" => "c015"],
    ["type" => "EmployeCadreSup", "value" => "c016"],
    ["type" => "Employe", "value" => "c013"],
    ["type" => "EmployeRetraite", "value" => "c017"],
    ["type" => "Employe", "value" => "c013"],
    ["type" => "VIP", "value" => "c034"],
    ["type" => "Employe", "value" => "c013"],
    ["type" => "EmployeCadreSup", "value" => "c016"],
    ["type" => "Employe", "value" => "c013"],
    ["type" => "EmployeDemonst", "value" => "c013"],
    ["type" => "EmployeInterim", "value" => "c013"],
    ["type" => "Casino", "value" => "c001"],
    ["type" => "Monoprix", "value" => "c004"],
    ["type" => "FidMarkGL", "value" => "c005"],
    ["type" => "FidMarkBHV", "value" => "c010"],
    ["type" => "Casino", "value" => "c001"],
    ["type" => "Euromaster", "value" => "c002"],
    ["type" => "Shell", "value" => "c003"],
    ["type" => "Modpass20", "value" => "c011"],
    ["type" => "Modpass30", "value" => "c012"],
    ["type" => "RelationDirection", "value" => "c026"],
    ["type" => "EmployÈCadre", "value" => "c027"],
    ["type" => "RelationDirection", "value" => "c026"],
    ["type" => "EmployeCadre", "value" => "c027"],
    ["type" => "Artisans", "value" => "c030"],
    ["type" => "Caissiere", "value" => "c031"],
    ["type" => "EmployeRetraite", "value" => "c028"],
    ["type" => "Provisoire", "value" => "c032"],
    ["type" => "BeauxArts", "value" => "c035"],
    ["type" => "Demonstrateur", "value" => "c029"],
    ["type" => "Presse", "value" => "c033"],
    ["type" => "Caissier", "value" => "c031"],
    ["type" => "EmployeRetraite", "value" => "c028"],
    ["type" => "Provisoire", "value" => "c032"],
    ["type" => "Demonstrateur", "value" => "c029"],
    ["type" => "Presse", "value" => "c036"],
];

if (!lmbconfig::getInstance()->is_exists("ROVERCASH_GL_GAX_BIN")) {
    lmbconfig::getInstance()->_createGroup("ROVERCASH", "ROVERCASH");
    lmbconfig::getInstance()->_createGroup("GL", "GL", "ROVERCASH");
    lmbconfig::getInstance()->_create("ROVERCASH_GL_GAX_BIN", "Identification client GAX BIN", "GL", json_encode($rovercash_gl_gax_bin));
}

$bdd = PDO_etendu::getInstance();
$id_carac_gl_type_client = $bdd->query("SELECT id_carac FROM caracs WHERE ref_carac = 'gl_type_client'")->fetchColumn();
if (!empty($id_carac_gl_type_client)) {
    $ordre = $bdd->query("SELECT MAX(ordre)+1 FROM caracs_allowed_values WHERE id_carac = " . $bdd->quote($id_carac_gl_type_client))->fetchColumn();
    foreach ($rovercash_gl_gax_bin as $item) {
        $type = $item["type"];
        $query = "INSERT IGNORE INTO `caracs_allowed_values`(`id_carac`, `value`, `lib_value`, `ordre`) VALUES 
                  (".$bdd->quote($id_carac_gl_type_client).", ".$bdd->quote($type).", ".$bdd->quote($type).", ".$bdd->quote($ordre).")";
        if ($bdd->exec($query)) {
            $ordre++;
        }
    }
}


// Vente gestion modif ligne article
if (!lmbconfig::getInstance()->is_exists("VE_gestion_regles_edition_ligne")) {
    lmbconfig::getInstance()->_create("VE_gestion_regles_edition_ligne", "Gérer les règles d’édition des lignes de vente", "VE", 0);
}
// FIN Vente gestion modif ligne article

function _handle_reglement_modes_param() {
    $modes_rembours_trop_percu_esp = [
        \LMBCore\Reglement\ReglementMode::getCacheInstanceByRef(\LMBCore\Reglement\Types\ReglementTypeEspecesSortant::REF_MODE_RENDU_MONNAIE)->getId(),
        \LMBCore\Reglement\ReglementMode::getCacheInstanceByRef(\LMBCore\Reglement\Types\ReglementTypeEspecesSortant::REF_MODE_NON_RENDU_MONNAIE)->getId(),
    ];

    $modes_rembours_trop_percu_autre = [
        \LMBCore\Reglement\ReglementMode::getCacheInstanceByRef(\LMBCore\Reglement\Types\ReglementTypeCreationAvoirClientSortant::REF_MODE_SYSTEM)->getId(),
        \LMBCore\Reglement\ReglementMode::getCacheInstanceByRef(\LMBCore\Reglement\Types\ReglementTypeEspecesSortant::REF_MODE_NON_RENDU_MONNAIE)->getId(),
    ];

    $reglements_modes = \LMBCore\Reglement\ReglementMode::getAll(\LMBCore\Reglement\ReglementType::DIRECTION_ENTRANT, false, "ordre", true);
    foreach ($reglements_modes as $reglement_mode) {
        $params = $reglement_mode->getParams();

        if ($reglement_mode->getId_reglement_type() == 1) {
            // espèces
            $params["modes_rembours_trop_percu"] = $modes_rembours_trop_percu_esp;

            $params["devises_autorisees"] = [
                "ALL"
            ];
        } else {
            $params["modes_rembours_trop_percu"] = $modes_rembours_trop_percu_autre;
        }

        $params["modes_rembours_retour"] = [
            \LMBCore\Reglement\ReglementMode::getCacheInstanceByRef(\LMBCore\Reglement\Types\ReglementTypeCreationAvoirClientSortant::REF_MODE_SYSTEM)->getId(),
        ];

        $reglement_mode->setParams($params);
        $reglement_mode->save();
    }

    $modes_esp_sortant = \LMBCore\Reglement\ReglementMode::getList(["reglements_modes.id_reglement_type = '7'"]);
    foreach ($modes_esp_sortant as $reglement_mode) {
        $params = $reglement_mode->getParams();

        $params["devises_autorisees"] = [
            "ALL"
        ];

        $reglement_mode->setParams($params);
        $reglement_mode->save();
    }
}

_handle_reglement_modes_param();

// *!*!*!*!*!*!*!*!*!**!*!*!*!*!*!*!*!*!*\\
fwrite($maj_log_file," Fin de Maj LMB vers ".$new_version."\n");
fclose($maj_log_file);
if (function_exists("opcache_reset")) opcache_reset();

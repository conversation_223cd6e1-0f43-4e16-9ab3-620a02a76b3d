'use strict';

var StandardServices = angular.module('catalogue.StandardServices', ["GenericServices", 'lmb', 'ui.router']);

/* Services */
StandardServices
    .factory('RestaurantService', function (LMBAjaxService) {
        return {
            loadSyntheseData: function() {
                var url = 'page.php/Compta/Standard/Restaurant/loadSyntheseData';
                return LMBAjaxService.post(url, {}, (result) => result.data);
            },
            loadVentes: function () {
                var url = 'page.php/Compta/Standard/Restaurant/loadVentes';
                var datas = {};
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data.data;
                });
            },
            loadVentesV2: function () {
                var url = 'page.php/Compta/Standard/Restaurant/loadVentes';
                var datas = {};
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data.data;
                });
            },
            searchVentes: function (criteres,pagination) {
                var url = 'page.php/Compta/Standard/Restaurant/ventesResult';
                var datas = {criteres: criteres, pagination: pagination};
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data;
                });
            },
            searchVentesDeliveroo: function (criteres, pagination) {
                const url = 'page.php/Compta/Standard/Restaurant/ventesDeliverooResult';
                const datas = { criteres, pagination };
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data;
                });
            },
            searchReglements: function (criteres, id_magasin, pagination) {
                var url = 'page.php/Compta/Standard/Restaurant/reglementsResult';
                var datas = {criteres: criteres, id_magasin: id_magasin, pagination: pagination};
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data;
                });
            },
            details: function (details,criteres) {
                var url = 'page.php/Compta/Standard/Restaurant/getDetails';
                var datas = {details: details,criteres: criteres};
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data;
                });
            },
            liste_modeles: function (type) {
                var url = 'page.php/Compta/Standard/Restaurant/listeModeles';
                var datas = {type: type};
                return LMBAjaxService.post(url, datas, function (result) {
                    return result.data;
                });
            },
			getVentesCaisse: function(data) {
				var url = 'page.php/Compta/Standard/Restaurant/getVentesCaisse';
				return LMBAjaxService.post(url, data, (result) => result.data);
			},
			getCloturesCaisse: function(data) {
				var url = 'page.php/Compta/Standard/Restaurant/getCloturesCaisse';
				return LMBAjaxService.post(url, data, (result) => result.data);
			},
			getVentesVentes: function(data) {
				var url = 'page.php/Compta/Standard/Restaurant/getVentesVentes';
				return LMBAjaxService.post(url, data, (result) => result.data);
			},
			getVentesReglements: function(data) {
				var url = 'page.php/Compta/Standard/Restaurant/getVentesReglements';
				return LMBAjaxService.post(url, data, (result) => result.data);
			},
			getDetailsTicket: function(data) {
				var url = 'page.php/Compta/Standard/Restaurant/getDetailsTicket';
				return LMBAjaxService.post(url, data, (result) => result.data);
			},
			getDetailsDocument: function(data) {
				var url = 'page.php/Compta/Standard/Restaurant/getDetailsDocument';
				return LMBAjaxService.post(url, data, (result) => result.data);
			}
        }
    })
;

<?php
require ("_dir.inc.php");
require ($DIR . "_session.inc.php");

?>

<div class="lmb-theme">


    <table class="style-1 auto-layout">
        <tr>
            <td>Id développeur</td>
            <td>
                <input type="number" name="id_dev" min="10" id='id_dev_input' hidden />
                <p class="id_dev hidden"></p>
            </td>
            <td>
                <button type="button" class="btn btn-tertiary hidden" id='id_dev_button'>Enregistrer mon id</button>
                <button type="button" class="btn btn-primary hidden" id='qa_id_button'>Générer mon qa_id</button>
            </td>
        </tr>
    </table>
    <div class="resultat hidden">
        <table class="style-1">
            <tr>
                <td class="valign-top">Attribut :</td>
                <td> 
                    <pre><code class="php" id='attribut'></code></pre>
                </td>
                <td>
                    <textarea style="opacity: 0;" id="copy_on_click"></textarea>
                </td>
            </tr>
        </table>

    </div>

</div>

<script src="../ressources/javascript/highlightjs/highlight.pack.js"></script>
<script type='text/javascript'>

    LMBTools.get({
        url: '../page.php/Components/Standard/Test/getIdDev',
        success: function (result) {
            if (result.qa_id !== null) {
                $j("#id_dev_input").hide();
                $j(".id_dev").show();
                $j(".id_dev").text(result.qa_id.substring(0, 2));
                $j("#qa_id_button").show();
            } else {
                $j("#id_dev_button").show();
                $j("#id_dev_input").show();
            }
        }
    });

    $j("#id_dev_button").click(function () {
        LMBTools.confirm({
            content: "Confirmez-vous cette action ?",
            onValid: function () {

                var id_dev = $j("#id_dev_input").val();
                LMBTools.post({
                    url: '../page.php/Components/Standard/Test/createFile',
                    data: {
                        id: id_dev
                    },
                    success: function (result) {
                        LMBToast.success({
                            title: 'Id_dev',
                            message: 'Votre id_dev a bien été enregistré dans config/qa_id.test'
                        });
                        $j("#id_dev_button").hide();
                        $j("#id_dev_input").hide();
                        $j(".id_dev").show();
                        $j(".id_dev").text(result.qa_id.substring(0, 2));
                        $j("#qa_id_button").show();
                    }
                });
            }
        });
    });

    $j("#qa_id_button").click(function () {
        LMBTools.get({
            url: '../page.php/Components/Standard/Test/updateQaId',
            success: function (result) {
                var qa_id = result.qa_id;
                $j("#attribut").text('qa_id="'+qa_id+'"');
                qa_id = parseInt(qa_id) + 1;
                $j("#copy_on_click").text('qa_id="'+qa_id.toString()+'"');
            }
        });
        $j("#copy_on_click").select();
        try {
            var successful = document.execCommand('copy');
            var msg = successful ? 'successful' : 'unsuccessful';
            console.log('Copying text command was ' + msg);
        } catch (err) {
            console.log('Oops, unable to copy');
        }
        $j(".resultat").show();
    });
</script>
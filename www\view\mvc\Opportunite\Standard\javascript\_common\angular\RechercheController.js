'use strict';

(function() {

    var module = angular.module('OpportuniteRecherche', ['standardDirectives', 'lmb', 'internationalisation.Directives', 'opportunite.StandardServices','GenericServices']);

    module.controller('RechercheController', ['$scope', 'RechercheService', '$timeout', 'LMBModalService', '$parse',
        function($scope, RechercheService, $timeout, LMBModalService, $parse) {

            $scope.init = function(params) {
                $scope.searchParams = params;
                $scope.tab = $scope.searchParams.listes.keys[0];
                $scope.date_debut_crea = null;
                $scope.date_fin_crea = null;
                $scope.date_debut_echeance = null;
                $scope.date_fin_echeance = null;
                $scope.searchMode = 'simple';
                $scope.searchCriteresDefault = [
                    {name: 'id_contact_client', lib: 'Client', type: 'text', simple: true, avancee: true, value: null},
                    {name: 'id_opportunite_statut', lib: 'Statut', type: 'text', simple: true, avancee: true, value: null},
                    {name: 'id_opportunite_type', lib: 'id_opportunite_type', type: 'hidden', simple: true, avancee: true, value: null},
                    {name: 'etat', lib: 'Etat', type: 'select', simple: true, avancee: true, value: 'encours'},
                    {name: 'date_debut_crea', lib: 'Création', type: 'periode', simple: true, avancee: true, value: null},
                    {name: 'date_fin_crea', lib: '', type: '', simple: true, avancee: true, value: null},
                    {name: 'date_debut_echeance', lib: 'Echéance', type: 'periode', simple: true, avancee: true, value: null},
                    {name: 'date_fin_echeance', lib: '', type: '', simple: true, avancee: true, value: null}
                ];
                $scope.searchResults = [
                    {name: 'date_creation', lib: 'Date', lib_short: 'Date', align: 'left', actif: true},
                    {name: 'id_contact_client', lib: 'ID Client', lib_short: 'ID Client', align: 'left', actif: true},
                    {name: 'nom_complet', lib: 'Client', lib_short: 'Client', align: 'left', actif: true},
                    {name: 'lib', lib: 'Libellé', lib_short: 'Libellé', align: 'left', actif: true},
                    //{name: 'lib_type', lib: 'Type', lib_short: 'Type', align: 'left', actif: true},
                    {name: 'lib_statut', lib: 'Statut', lib_short: 'Statut', align: 'left', actif: true},
                    {name: 'budget', lib: 'Budget', lib_short: 'Budget', align: 'right', actif: true}
                ];
                $scope.paginationData = {};
                $scope.resetCriteres();
            };

            $scope.changeTab = function (tab) {
                $scope.tab = parseInt(tab)-1;
                $scope.resetCriteres();
                $scope.loadCaracs(tab);
                $scope.resetPage();
            };

            $scope.loadCaracs = function (id_opportunite_type) {
                RechercheService.loadCaracs(id_opportunite_type).then(function (data) {
                    $scope.searchParams.listes.status[1] = data.oppDetails.allStatus;
                    $scope.searchParams.listes.caracs = data.oppDetails.allCaracs;
                })
            };

            $scope.search = function() {
                $scope.rechercheEnCours = true;
                var criteres = {};
                $scope.searchCriteres.forEach(function(critere){
                    if (critere[$scope.searchMode] && critere.value) {
                        if (typeof critere.value == 'object') {
                            var values = [];
                            Object.keys(critere.value).forEach(function (key) {
                                if (critere.value[key]) {
                                    values.push(key);
                                }
                            });
                            if (values.length) {
                                criteres[critere.name] = {value: values};
                            }
                        } else {
                            criteres[critere.name] = critere.value;
                        }
                    }
                    if(critere.name == 'date_debut_crea'){
                        criteres[critere.name] = $j('.date_debut_crea').val();
                        $scope.date_debut_crea = $j('.date_debut_crea').val();
                    }
                    if(critere.name == 'date_fin_crea'){
                        criteres[critere.name] = $j('.date_fin_crea').val();
                        $scope.date_fin_crea = $j('.date_fin_crea').val();
                    }
                    if(critere.name == 'date_debut_echeance'){
                        criteres[critere.name] = $j('.date_debut_echeance').val();
                        $scope.date_debut_echeance = $j('.date_debut_echeance').val();
                    }
                    if(critere.name == 'date_fin_echeance'){
                        criteres[critere.name] = $j('.date_fin_echeance').val();
                        $scope.date_fin_echeance = $j('.date_fin_echeance').val();
                    }
                    if(critere.name == 'id_opportunite_type'){
                        criteres[critere.name] = $scope.tab+1;
                    }
                });
                var results = $scope.searchResults
                    .filter(function(result){
                        return result.actif
                    })
                    .map(function(result){
                        return result.name;
                    });
                if (angular.isDefined($scope.paginationData.form_recherche_opportunite_avancee)) {
                    var pagination = {
                        page_to_show: $scope.paginationData.form_recherche_opportunite_avancee.page_to_show,
                        nb_par_pages: $scope.paginationData.form_recherche_opportunite_avancee.nb_par_pages
                    };
                }
                RechercheService.search(criteres,results,pagination).then(function(data) {
                    $scope.opps = data.opps;

                    $scope.searchResults.forEach(function(critere){
                        critere.loaded = critere.actif;
                    });
                    $scope.paginationData.form_recherche_opportunite_avancee = angular.extend($scope.paginationData.form_recherche_opportunite_avancee || {}, data.paginationData);

                    $scope.rechercheEnCours = false;
                });
            };

            $scope.resetCriteres = function() {
                $scope.searchCriteres = angular.copy($scope.searchCriteresDefault);
                $scope.opps = null;
            };

            $scope.resetPage = function() {
                if (angular.isDefined($scope.paginationData.form_recherche_opportunite_avancee)) {
                    $scope.paginationData.form_recherche_opportunite_avancee.page_to_show = 1;
                }
            };

            $scope.updateResult = function(result) {
                if (result.actif && !result.loaded) {
                    $scope.search();
                }
            };
        }]);

})();
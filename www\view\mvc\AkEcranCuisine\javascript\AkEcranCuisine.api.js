'use strict';
(function () {
    angular
        .module('AkEcranCuisineManager')
        .factory('AkEcranCuisineApi', AkEcranCuisineApi);

        AkEcranCuisineApi.$inject = ['LMBAjaxService'];
    function AkEcranCuisineApi(LMBAjaxService) {
        const apiBase = "page.php/AkEcranCuisine/Standard/Standard/";
        let post = function (action, data = {}, useApiBase = true) {
            return LMBAjaxService.post((useApiBase ? apiBase : '') + action, data, function (response) {
                return response.data;
            });
        };
        return {
            getList: (data) => post('getList', data),
            create: (data) => post('create', data),
            update: (data) => post('update', data),
            desync: (data) => post('desync', data),
            delete: (data) => post('delete', data)
        }
    }
})();

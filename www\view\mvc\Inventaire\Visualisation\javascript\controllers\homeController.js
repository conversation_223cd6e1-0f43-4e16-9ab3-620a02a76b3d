(function(angular) {
    'use strict';

InventoryManager.controller('HomeController', [
    '$scope',
    '$rootScope',
    '$state',
    '$stateParams',
    '$timeout',
    '$q',
    'InventaireService',
    'InventaireDataService',
    'LMBArticleService',
    'LMBSelectOneService',
    'LMBModalService',
    'LMBProcessService',
function(
    $scope,
    $rootScope,
    $state,
    $stateParams,
    $timeout,
    $q,
    InventaireService,
    InventaireDataService,
    LMBArticleService,
    LMBSelectOneService,
    LMBModalService,
    LMBProcessService
) {
    /** VARIABLES **/

    $scope.criteresSynthese = {
        error: true,
        okay: true,
        zero: false,
        unknown: true
    };

    $scope.actionForRemaining = null;
    $scope.inventorizedArticleIds = null;

    $scope.listCriteres = [];
    $scope.indexSelected = 0;
    $scope.nomCriteres = {
        categories: "Catégories",
        marques: "Marques",
        en_stock: "Etat du stock",
        mouv_depuis: "Mouvement de stock depuis le",
        fournisseurs: "Fournisseurs"
    };

    $scope.nomCriteresValeurs = {
        en_stock: 'en stock',
        en_erreur_stock: 'en erreur de stock',
        pas_en_stock: 'pas en stock'
    };

    $scope.listMarques = [];

    $scope.counterArt = 0;
    $scope.counterOnLoading = false;
    $scope.refreshAfterLoading = false;

    $scope.loadingSynthesis = false;

    $scope.listsArticles = {
        search: [],
        white: [],
        black: []
    };

    $scope.listArticlesPartiels = [];
    $scope.zones = [];
    $scope.options = {
        tabs: {
            main: 0,
            comment: 1,
            historty: 2
        },
        selectedPage : 0
    }
    let editor = null;

    $scope.inventaireStatus = InventaireDataService.getInventaireStatus();
    $scope.history = {};
    $scope.editDateInventaire = false;
    $scope.permissions = [];
    $scope.lastStockId = null;
    /** FUNCTIONS **/

    LMBLangage.preloadTags([510591, 113570, 520003, 340426]);

    $scope.init = function(idInventaire, tracking) {
        LMBLangage.preloadTags([113568,113579,113582,510590,113584,120971,113580,113581]);

        $scope.trackingEnabled = tracking;
        $scope.id_inventaire = idInventaire;
        $scope.inventaire = {
            id_inventaire: idInventaire,
            commentaire: '',
        };
        $scope.paginationData = {};
        editor = LMBTheme.initTinyEditor('inventaire_commentaire', 300);
        if($state.current.name === "") {
            InventaireService.getData(idInventaire).then(function(data) {
                $scope.inventaire = angular.extend($scope.inventaire, data.inventaire);
                $scope.inventaire.stocks = [$scope.inventaire.id_stock];
                $scope.listCriteres = angular.extend($scope.listCriteres, $scope.inventaire.criteres);
                $scope.listMarques = data.inventaire.marques;
                $scope.permissions = data.permissions;
                $scope.zones = $scope.inventaire.zones;
                InventaireDataService.setZones($scope.zones);
                if($scope.isNewPage() || !parseInt($scope.inventaire.id_inventaire) ) {
                    InventaireService.getCaracs().then(function (data) {
                        $scope.caracsDisponibles = data.caracs;
                        $scope.selectList(0);
                        $scope.refresh();
                    });
                } else if ($scope.isStatus($scope.inventaireStatus.annule)) {
                    $state.go('options', {idInventaire: idInventaire});
                } else if(!$scope.isStatus($scope.inventaireStatus.tofinalise) && !$scope.isStatus($scope.inventaireStatus.done)) {
                    // Je sais pas à quoi ça sert mais ça fait bugger la navigation quand on utilise le refresh de LMB
                    LMBNavigation.ignoreNextHashChange();
                    $scope.redirectSaisie();
                } else {
                    $state.go('synthesis', {idInventaire: idInventaire});
                }
            });
        }
    };

    $scope.getInventaireLib = function () {
        var inventaire = $scope.inventaire;
        if (!inventaire.id_inventaire) {
            return __(340424, "Nouvel inventaire");
        } else if (inventaire.lib) {
            return inventaire.lib+' n°'+inventaire.id_inventaire;
        } else {
            return __(340425,'Inventaire n°{id}',{id:inventaire.id_inventaire});
        }
    };

    /**
     * Popup de création d'article rapide
     * Permet la création d'un article à partir d'un "article inconnu" créé en mode scan (code barre inconnu +
     * demande de libellé)
     */
    $scope.openQuickArticleCreation = function(line, nextLines) {
        nextLines = nextLines || [];

        var pop = new LMBModal("pop" + (new Date()).getTime(), {
            titre: __(710211,"Création rapide d'article"), overlay: true, removeOnClose: true, resizable: true,
            style: {
                width: "650px",
                height: "690px"
            },
            callback: function(result) {
                if(result.statut === true) {
                    var data = {
                        id_inventaire: $scope.inventaire.id_inventaire,
                        id_inv_line: line.id_inv_line,
                        ref_article: result.datas.ref_article
                    };

                    InventaireService.linkLineToNewArticle(data).then(function(res) {
                        line.id_article = res.id_article;
                        line.ref_article = result.datas.ref_article;

                        if(nextLines.length > 0) {
                            var next = nextLines.shift();
                            $scope.openQuickArticleCreation(next, nextLines);
                        }
                    });
                }
            }
        });
        pop.request('catalogue_article_fiche_quick.php', {
            lib_article: line.lib_article,
            code_barre: line.code_barre,
            only_products: true
        }, {
            type: "POST"
        });
        pop.open();
    };

    /**
     * Popup d'association d'un "article inconnu" avec un article présent dans le catalogue.
     */
    $scope.openArticleLinking = function(line, nextLines) {
        nextLines = nextLines || [];

        LMBSelectOneService.show({
            type: 'article',
            templateUrl: 'page.php/Components/Standard/Template/View:javascript/angular/templates/template_article_select.phtml',
            searchUrl: 'page.php/Article/Standard/SearchEngine/searchSimple',
            searchReturnVariable: 'articles',
            searchPrimaryKey: 'id_article',
            searchCriteria: {
                variantes: {
                    value: [1, null]
                },
                modele: {
                    value: 'materiel'
                }
            },
            onClose: function(result) {
                if(result.selected) {
                    var data = {
                        id_inventaire: $scope.inventaire.id_inventaire,
                        id_zone: $stateParams.idZone ? $stateParams.idZone : null,
                        id_inv_line: line.id_inv_line,
                        code_barre: line.code_barre,
                        qte_inventaire: line.qte_inventaire,
                        ref_article: result.article.ref_article
                    };

                    InventaireService.linkLineToArticle(data).then(function(res) {
                        if(res.error) {
                            LMBToast.error({
                                title: __(113570, 'Inventaire'),
                                message: res.message,
                                entity: LMBToast.ENTITY_STOCK
                            });
                        } else {
                            line.lib_article = res.article.lib_article;
                            line.qte_before = res.article.qte_before;
                            line.qte_inventaire = res.article.qte_inventaire;
                            line.ref_article = res.article.ref_article;
                            line.id_article = res.article.id_article;
                            line.gestion_sn = res.article.gestion_sn;

                            if(nextLines.length > 0) {
                                var next = nextLines.shift();
                                $scope.openArticleLinking(next, nextLines);
                            } else {
                                $rootScope.$emit("inventoryStarts");
                            }
                        }
                    });
                }
            }
        });
    };

    $scope.openStock = function(line) {
        var pop = new LMBModal("pop" + (new Date()).getTime(), {
            titre: __(120971,"Résumé du stock"),
            drag: true,
            overlay: true,
            removeOnClose: true,
            style: {
                width: "650px",
                height: "690px"
            },
            callback: function(result) {
                if(result.statut === true) {
                    var data = {
                        id_inventaire: $scope.inventaire.id_inventaire,
                        id_inv_line: line.id_inv_line,
                        ref_article: result.datas.ref_article
                    };

                    InventaireService.linkLineToArticle(data).then(function(res) {
                        line.id_article = res.id_article;
                        line.ref_article = result.datas.ref_article;
                    });
                }
            }
        });
        pop.update("catalogue_articles_resume_stock.php?ref_article=" + line.ref_article, true);
        pop.open();
    };

    $scope.openTracking = function(line) {
        LMBModalService.showModal({
            LMBModalConfig: {
                titre: "Informations de traçabilité",
                style: {
                    width: "750px"
                }
            },
            angularModal: {
                config: {
                    templateUrl: 'page.php/Inventaire/Standard/Visualisation/gotoTrackingModal',
                    controller: 'TrackingModalController',
                    inputs: {
                        line: line,
                        id_inventaire: $scope.inventaire.id_inventaire
                    }
                },
                then: function (modal) {

                },
                onClose: function (result) {
                    if(angular.isDefined(result)) {
                    }
                }
            }
        });
    };

    $scope.selectLine = function(line, e) {
        e.preventDefault();
//        if(['INPUT', 'I', 'BUTTON', 'LI'].indexOf(e.target.tagName) === -1) {
            if(line.selected === true) {
                line.selected = false;
            } else {
                if(parseInt(line.gestion_sn) !== 0) {
                    line.selected = true;
                }
                // Focus "Qté inventoriée" field
                angular.element(e.target.closest('tr').querySelector('input.inputQte')).focus();
            }
//        }
    };

    $scope.listeVide = function(liste) {
        var vide = true;
        angular.forEach(liste, function(critere, name) {
            if(critere && 'values' in critere) {
                vide = false;
            }
        });

        return vide;
    };

    $scope.countObject = function(obj) {
        return Object.keys(obj).length;
    };

    $scope.selectList = function(index) {
        if(index >= 0) {
            $scope.indexSelected = index;
        }

        $timeout(function() {
            jQuery('.dropdown-checkbox').trigger('refresh-placeholder');
        }, 0);
    };

    $scope.newList = function() {
        $scope.listCriteres.push({
            categories: {

            },
            marques: {

            },
            en_stock: {
                value: ''
            },
            fournisseurs: {},
        });
        $scope.selectList($scope.listCriteres.length - 1);
    };

    $scope.removeList = function(index) {

        var remove = function() {
            $scope.listCriteres.splice(index, 1);

            if($scope.listCriteres.length === 0) {
                $timeout(function() {
                    $scope.newList();
                    $scope.refresh();
                }, 0);
            }
            else {
                $timeout(function() {
                    $scope.selectList(0);
                    $scope.refresh();
                }, 0);
            }
        };

        if(Object.keys($scope.listCriteres[index]).length > 0) {
            LMBTools.confirm({
                content: _e_html(113568,"Êtes-vous sûr de vouloir supprimer cette liste de critères ?"),
                confirm: __(113569,"Supprimer"),
                onValid: function (confirm) {
                    if (confirm) {
                        $timeout(remove, 0);
                    }
                }
            });
        }
        else {
            remove();
        }
    };

    $scope.refresh = function() {
        if (!$scope.counterOnLoading) {
            $scope.counterOnLoading = true;
            $scope.refreshAfterLoading = false;

            if (!$scope.listCriteres[$scope.indexSelected]?.mouv_depuis?.date) {
                if ($scope.listCriteres[$scope.indexSelected].mouv_depuis) {
                    delete $scope.listCriteres[$scope.indexSelected].mouv_depuis;
                }
            }
            var criteres = angular.copy($scope.listCriteres);

            var data = {
                id_inventaire: $scope.id_inventaire,
                id_stock : $scope.inventaire.id_stock,
                type_inventaire: $scope.inventaire.type_inventaire,
                criteres: JSON.stringify(criteres)
            };

            InventaireService.searchArticles(data).then(function(result) {
                $scope.listsArticles.search = result.fiches;
                if (result.count) {
                    $scope.counterArt = result.count;
                } else {
                    $scope.counterArt = 0;
                }
                $scope.counterOnLoading = false;
                if ($scope.refreshAfterLoading) {
                    $scope.refresh();
                }
            });
        } else {
            $scope.refreshAfterLoading = true;
        }
    };

    $scope.getArticleSnQteZone = function(sn, id_zone){

        var qte = null;

        var zones = JSON.parse(sn.details_zones);
        for (let item of zones) {
            if(item.id_zone == id_zone){
                qte = item.qte;
                break;
            }
        }
        return qte;
    };

    $scope.updateQte = function(line, old_qte_inventaire = 0) {
        if (!$scope.validPermissionUpdateQte()) {
            line.qte_inventaire = old_qte_inventaire;
            alert_no_permission();
            return;
        }

        var data = {
            id_inventaire: $scope.inventaire.id_inventaire,
            id_inv_line: line.id_inv_line,
            id_zone: $stateParams.idZone ? $stateParams.idZone : null,
            qte: Math.abs(line.qte_inventaire)
        };

        InventaireService.updateQte(data).then(function(res) {
            if(res.qte_inventaire) {
                line.qte_inventaire = res.qte_inventaire;
            } else {
                line.qte_inventaire = res.line.qte_inventaire;
            }
        });
    };

    $scope.validPermissionUpdateQte = function () {
        if ($scope.permissions.length <= 0 || !$scope.permissions.can_udpate_stock) {
           return false;
        }
        return true;
    }

    $scope.copieQte = function(line){
        line.qte_inventaire = line.qte_before;

        $scope.updateQte(line);
    };

    $scope.copieQteSN = function(line, sn){
        sn.qte_inventaire = sn.qte_before;

        $scope.updateQteLot(line, sn);
    };


    $scope.checkQte = function(qte_before, qte_inventaire, prefix = "bg") {
        // __Si pas de quantité actuelle, il ne peut pas y avoir d'écart
        if(qte_before === null || qte_before === "" || qte_inventaire === null) {
            return "";
        } else if(parseInt(qte_before) !== parseInt(qte_inventaire)) {
            // __Si la quantité inventoriée est différente de la quantité actuelle, je rajoute la classe "text-error"
            return prefix + "-error";
        } else {
            // __Si la quantité inventoriée est égale à la quantité actuelle, je mets la classe text-success
            return prefix + "-success";
        }
    };

    $scope.isCreatingInventaire = false;

    $scope.updateStock = function () {
        if ($scope.inventaire.id_inventaire) {
            if ($scope.lastStockId !== $scope.inventaire.id_stock) {
                const data = {
                    id_inventaire: $scope.inventaire.id_inventaire,
                    id_stock: $scope.inventaire.id_stock
                };

                InventaireService.updateStock(data).then(function (response) {
                    if (response && response.lib_stock) {
                        $scope.inventaire.lib_stock = response.lib_stock;
                        $scope.lastStockId = response.id_stock;
                    }
                });
                $scope.refresh();
            }
        } else {
            if (!$scope.isCreatingInventaire) {
                $scope.isCreatingInventaire = true;
                $scope.createInventaire().then(function (resp) {
                    $scope.isCreatingInventaire = false;
                }).catch(function (error) {
                    console.error("Erreur lors de la création de l'inventaire:", error);
                    $scope.isCreatingInventaire = false;
                });
                $scope.refresh();
            }
        }
    };

    $scope.$watch('inventaire.stocks', function(newVal, _){
        if(typeof newVal !== "undefined") {
            $scope.inventaire.id_stock = newVal;
        }
    });

    $scope.updateType = function() {
        if ($scope.inventaire.id_inventaire) {
            const data = {
                id_inventaire: $scope.inventaire.id_inventaire,
                type_inventaire: $scope.inventaire.type_inventaire
            };
            InventaireService.updateType(data);

        } else {
            $scope.createInventaire();
        }
    };


    $scope.updateComment = function () {
        editor.post();
        $scope.inventaire.commentaire = editor.t.value;
        
        if ($scope.inventaire.id_inventaire) {
            var data = {
                id_inventaire: $scope.inventaire.id_inventaire,
                commentaire: $scope.inventaire.commentaire,
            };

            InventaireService.updateComment(data).then(function (response) {
            });
        } else {
            $scope.createInventaire();
        }
        $scope.refresh();
    };

    $scope.handledListArticles = function() {
        // Ajout des articles de la whitelist aux résultats de la recherche
        if ($scope.listsArticles.white.length > 0) {
            for (var i = 0; i < $scope.listsArticles.white.length; i++) {
                var present = $scope.listsArticles.search.filter(function(elt) {
                    return elt.id_article == $scope.listsArticles.white[i].id_article;
                });
                if (!present.length) {
                    $scope.listsArticles.search.push($scope.listsArticles.white[i]);
                }
            }
        }

        // Suppression des articles de la blacklist des résultats de la recherche
        if ($scope.listsArticles.black.length > 0) {
            for (var i = 0; i < $scope.listsArticles.black.length; i++) {
                $scope.listsArticles.search = $scope.listsArticles.search.filter(function(elt) {
                    return elt.id_article != $scope.listsArticles.black[i].id_article;
                });
            }
        }

        // Insertion des articles dans l’inventaire
        $scope.listArticlesPartiels = $scope.listsArticles.search.map(function(i) { return i['id_article']; });
    }

    $scope.checkArticlesArrayInDoc = function(event) {
        const data = {
            articles: $scope.listArticlesPartiels
        };
        InventaireService.checkArticlesArrayInDoc(data).then(function(resp) {
            if(resp.docs.length) {
                var message = "";
                for(var i = 0; i < resp.docs.length; i++) {
                    message += "<div style='text-align: left; margin-left: 20%'><a target='_blank' href='#documents_edition.php?ref_doc="+resp.docs[i]+"'>"+resp.docs[i]+"</a></div>";
                    if(i == 9 && resp.docs.length > 10) {
                        message += __(121495,"et {nb_restant} autres documents.", {'nb_restant':resp.docs.length-10});
                        break;
                    }
                }

                LMBTools.confirm({
                    title: __(121494,"Attention !"),
                    content: __(121493,"Certains de ces articles sont présents dans des documents en cours : ") + message,
                    onValid: function(answer) {
                        if(answer) {
                            $scope.confirmStart(data, event);
                        }
                    }
                });
            } else {
                $scope.confirmStart(data, event);
            }
        });
    }

    $scope.start = function(event) {
        
        if ($scope.inventaire.type_inventaire == 'partiel' && ($scope.listsArticles.search.length || $scope.listsArticles.white.length)) {
            $scope.handledListArticles();
            $scope.checkArticlesArrayInDoc(event);
        } else {
            $scope.confirmStart({}, event);
        }
    };

    $scope.confirmStart = function(data={}, event) {
        const zones = InventaireDataService.getZones();
        if (zones.length >= 2) {
            const hasEmptyTitle = zones.find(zone => zone.lib.length <= 0);
            if (hasEmptyTitle) {
                LMBToast.error({
                    title: __(340426, 'Inventaire'),
                    message: __(520003, "Veuillez choisir un libellé pour la nouvelle zone."),
                    entity: LMBToast.ENTITY_STOCK
                });
                return;
            }
        }
        if(event) {
            event.target.innerHTML = '<span class="spinner spinner-white" style="padding-top:3px"></span>';
        }
        if ($scope.inventaire.id_inventaire) {
            data.id_inventaire = $scope.inventaire.id_inventaire;
            data.type_inventaire = $scope.inventaire.type_inventaire

            if ($scope.inventaire.type_inventaire == 'partiel' && $scope.listArticlesPartiels.length) {
                data.articles_partiels = JSON.stringify($scope.listArticlesPartiels);
                if (data.articles) {
                    delete data.articles;
                }
            }
            
            InventaireService.start(data).then(function (res) {
                if (res.id_inventaire) {
                    $scope.inventaire.statut = $scope.inventaireStatus.inprogress;
                    $scope.zones = res.zones;
                    InventaireDataService.setZones(res.zones);
                    $rootScope.$emit("inventoryStarts");
                    LMBNavigation.ignoreNextHashChange();
                    $scope.redirectSaisie();
                }
            });
        } else {
            $scope.createInventaire('start', true);
        }
    };

    $scope.createInventaire = function (type = null, redirect = false) {
        if(!$scope.inventaire.id_inventaire) {
            $rootScope.isApiCreateInventaireCalled = true;
            const data = {
                type: type,
                type_inventaire: $scope.inventaire.type_inventaire,
                id_stock: $scope.inventaire.id_stock,
                zones: InventaireDataService.getZones(),
                statut: $scope.inventaire.statut,
                date_inventaire: $scope.dateInventaire,
                commentaire: $scope.commentaire,
                lib: $scope.inventaire.lib
            };

            if (type === 'start' && $scope.inventaire.type_inventaire == 'partiel' && $scope.listArticlesPartiels.length) {
                data.articles_partiels = $scope.listArticlesPartiels;
                data.criteres = $scope.listCriteres;
            }

            return InventaireService.create(data).then(function (resp) {
                $rootScope.isApiCreateInventaireCalled = false;
                $scope.inventaire.id_inventaire = resp.inventaire.id_inventaire;
                $scope.inventaire = resp.inventaire;
                $scope.zones = resp.inventaire.zones;
                $scope.dateInventaire = resp.inventaire.date_inventaire;
                $scope.dateInventaireOld = resp.inventaire.date_inventaire;
                InventaireDataService.setZones(resp.inventaire.zones);
                if (redirect) {
                    LMBNavigation.redirect('page.php/Inventaire/Standard/Visualisation/index:' + $scope.inventaire.id_inventaire);
                }
                return resp;
            });
        }
    };

    $scope.redirectSaisie = function () {
        if ($scope.zones.length > 0) {
            $state.go('zone.saisie', {idInventaire: $scope.inventaire.id_inventaire, idZone: $scope.zones[0].id_inv_zone});
        } else {
            $state.go('overview.saisie', {idInventaire: $scope.inventaire.id_inventaire, idZone: null});
        }
    }

    $scope.finishSetup = function() {
        $scope.inventaire.statut = $scope.inventaireStatus.todo;
        if ($scope.inventaire.id_inventaire) {
            const data = {
                id_inventaire: $scope.inventaire.id_inventaire,
                statut: $scope.inventaireStatus.todo,
                criteres: $scope.listCriteres
            };
            InventaireService.updateStatut(data).then(function(res) {
                $scope.inventaire.statut = $scope.inventaireStatus.todo;
                $scope.inventaire.lib_statut = res.lib_statut;
                $scope.inventaire.date_validation = res.date_validation;

                LMBToast.success({
                    title: __(520017, 'Inventaire'),
                    message: __(520018,"Paramétrage terminé"),
                    entity: LMBToast.ENTITY_STOCK
                });

                LMBNavigation.redirect('page.php/Inventaire/Standard/Visualisation/index:' + $scope.inventaire.id_inventaire);
            });
        } else {
            $scope.createInventaire(null, true);
        }
    }

    $scope.validPermissionAnnuler = function () {
        if ($scope.permissions.length <= 0 || !$scope.permissions.can_annuler) {
            return false;
        }
        return true;
    }

    $scope.updateStatut = function(statut, event) {
        if(statut === "annule") {
            if (!$scope.validPermissionAnnuler()) {
                alert_no_permission();
                return;
            }
            LMBTools.confirm({
                content: _e_html(113579,"Êtes-vous sûr de vouloir annuler l'inventaire ?"),
                confirm: __(113580,"Annuler l'inventaire"),
                cancel: __(113581,"Retour à l'inventaire"),
                onValid: function (confirm) {
                    if (confirm) {
                        var data = {};
                        data.id_inventaire = $scope.inventaire.id_inventaire;
                        data.statut = statut;

                        InventaireService.updateStatut(data).then(function(e) {
                            $scope.inventaire.statut = statut;
                            $scope.inventaire.lib_statut = e.lib_statut;
                            $scope.inventaire.date_validation = e.date_validation;

                            LMBToast.info({
                                title: __(113570, 'Inventaire'),
                                message: _e_html(113582,"Inventaire annulé"),
                                entity: LMBToast.ENTITY_STOCK
                            });
                        });
                    }
                }
            });
        } else if(statut === "done") {
            InventaireService.countStockDifferences($scope.inventaire.id_inventaire, $scope.actionForRemaining).then(function(result) {
                if (result.counter && result.counter > 0) {
                    LMBTools.confirm({
                        content: _e_html(510590,"Attention, {count} articles ont vu leur stock modifié depuis le début de l’inventaire. La quantité initiale va être mise à jour en conséquence.", {count: result.counter}),
                        confirm: "Valider",
                        onValid: function (confirm) {
                            if (confirm) {
                                $scope.updateStatutDone();
                            }
                        }
                    });
                } else {
                    if(result?.error) {
                        LMBToast.error({
                            title: __(113570, 'Inventaire'),
                            message: 'Une erreur est survenue lors du calcul des différences de stock'
                        });
                        return;
                    }
                    $scope.updateStatutDone();
                }
            });
        } else {
            if(event) {
                event.target.oldText = event.target.innerHTML;
                event.target.innerHTML = '<span class="spinner spinner-white" style="padding-top:3px"></span>';
            }
            var data = {
                id_inventaire: $scope.inventaire.id_inventaire,
                statut: statut
            };

            if(statut === "inprogress") {
                jQuery(".onglet[data-box=articles_inventories]").trigger("click");
            }

            InventaireService.updateStatut(data).then(function(e) {
                if(e.error) {
                    LMBToast.error({
                        title: __(113570, 'Inventaire'),
                        message: 'Votre inventaire multi-zones contient moins de 2 zones. Veuillez en ajouter ou désactiver le mode multi-zone.',
                        entity: LMBToast.ENTITY_STOCK
                    });

                    if(event) {
                        event.target.innerHTML = event.target.oldText;
                    }
                } else {
                    $scope.inventaire.statut = statut;
                    $scope.inventaire.lib_statut = e.lib_statut;
                    $scope.inventaire.date_validation = e.date_validation;

                    if(statut === 'tofinalise') {
                        InventaireService.getData($scope.inventaire.id_inventaire).then(function(data) {
                            $scope.inventaire = angular.extend($scope.inventaire, data.inventaire);
                            $scope.inventaire.stocks = [$scope.inventaire.id_stock];
                        });
                    } else {
                        $scope.redirectInventaireStart();
                    }
                }
            });
        }
    };

    $scope.updateStatutDone = function() {

        var processes = [
            {
                ref: 'validation_inventaire',
                token: 'vi_' + Date.now(),
                lib: __(520049, "Validation de l'inventaire")
            }
        ];

        var data = {
            id_inventaire: $scope.inventaire.id_inventaire,
            actionForRemaining: $scope.actionForRemaining,
            inventorizedArticleIds: $scope.inventorizedArticleIds,
            token: {
                validation_inventaire: processes[0].token
            }
        };

        LMBProcessService.start({
            url: 'page.php/Inventaire/Standard/Visualisation/finalise',
            datas: data,
            process: processes,
            title: __(520049, "Validation de l'inventaire"),
            overlayClickToClose: false,
            closeOnProcessEnd: true,
            onProcessEnd: function(res) {
                if (res && res.data) {
                    $scope.inventaire.statut = res.data.statut;
                    $scope.inventaire.lib_statut = res.data.lib_statut;
                    $scope.inventaire.date_validation = res.data.date_validation;
                    $rootScope.$emit("inventoryStarts");
                } else {
                    LMBTools.alert(__(510298,"Une erreur est survenue lors de la validation de l'inventaire"));
                }
            }
        });

    };

    $scope.restart = function() {
        LMBTools.confirm({
            content: _e_html(113584,"Êtes-vous sûr de vouloir recommencer l'inventaire ?"),
            confirm: _e_html(113585,"Recommencer"),
            onValid: function (confirm) {
                if (confirm) {
                    var data = {};
                    data.id_inventaire = $scope.inventaire.id_inventaire;
                    InventaireService.restart(data).then(function(e) {
                        $scope.inventaire.statut = e.statut;
                        $scope.inventaire.lib_statut = e.lib_statut;
                        angular.forEach($scope.inventaire.lines, function(line, key) {
                            line.qte_inventaire_line = null;
                        });

                        LMBToast.success({
                            title: __(113570, 'Inventaire'),
                            message: "Inventaire réinitialisé",
                            entity: LMBToast.ENTITY_STOCK
                        });

                    });
                }
            }
        });
    };

    $scope.validPermissionValidate = function () {
        if ($scope.permissions.length <= 0 || !$scope.permissions.can_validate) {
            return false;
        }
        return true;
    }

    $scope.finalise = function(actionForRemaining) {
        let inventorizedArticleIds = [];
        $scope.paginationData.rechercheRecap.lines.forEach(function(line){
            inventorizedArticleIds.push(line.id_article);
        });
        $scope.inventorizedArticleIds = inventorizedArticleIds;
        
        if(!$scope.validPermissionValidate()) {
            alert_no_permission();
            return;
        }
        $scope.actionForRemaining = actionForRemaining;
        if(parseInt($scope.inventaire.nb_articles_restants) === 0 || actionForRemaining !== null) {
            InventaireService.getUnknownArticles($scope.inventaire.id_inventaire).then(function(res) {
                if(res.error) {
                    LMBToast.error({
                        title: __(113570, 'Inventaire'),
                        message: __(510591, "Veuillez sélectionner une action pour les articles non inventoriés."),
                        entity: LMBToast.ENTITY_STOCK
                    });
                } else {
                    if(res.articles.length === 0) {
                        $scope.updateStatut('done');
                    } else {
                        LMBModalService.showModal({
                            LMBModalConfig: {
                                titre: 'Articles inconnus',
                                style: LMBModal.SIZE_AUTO
                            },
                            angularModal: {
                                config: {
                                    templateUrl: 'page.php/Inventaire/Standard/Visualisation/gotoFinaliseModal',
                                    controller: function($scope, close) {
                                        $scope.confirm = function() {
                                            close(true);
                                        };

                                        $scope.cancel = function() {
                                            close(false);
                                        };
                                    }
                                },
                                then: function (modal) { },
                                onClose: function (result) {
                                    if(result == false || res.articles.length === 0) {
                                        $scope.updateStatut('done');
                                    } else {
                                        var articles = res.articles;
                                        var article = articles.shift();
                                        $scope.openQuickArticleCreation(article, articles);
                                    }
                                }
                            }
                        });
                    }
                }
            });
        } else {
            LMBToast.error({
                title: __(113570, 'Inventaire'),
                message: __(510591, "Veuillez sélectionner une action pour les articles non inventoriés."),
                entity: LMBToast.ENTITY_STOCK
            });
        }
    };

    $scope.refreshZones = function() {
        if ($scope.id_inventaire <= 0) return $q.when(true);;
        return InventaireService.getZones({id_inventaire: $scope.id_inventaire}).then(function(res) {
            $scope.zones = res.zones.map(zone => {
                zone.wholeCatalog = zone.wholeCatalog || true;
                return zone;
            });
        });
    };

    $scope.addSerialNumber = function(line, sn) {
        var data = {
            id_inventaire: $scope.inventaire.id_inventaire,
            id_inv_line: line.id_inv_line,
            sn: sn,
            codec: "SN"
        };

        InventaireService.addSerialNumber(data).then(function(e) {
            if(e.error) {
                LMBToast.error({
                    title: __(113570, 'Inventaire'),
                    message: e.error,
                    entity: LMBToast.ENTITY_STOCK
                });
            } else {
                line = angular.extend(line, e.line);
                line.new_sn = "";
            }
        });
    };

    $scope.addBatchNumber = function(line) {
        var data = {
            id_inventaire: $scope.inventaire.id_inventaire,
            id_inv_line: line.id_inv_line,
            sn: line.new_sn,
            qte: Math.abs(line.qte_lot),
            codec: "LOT"
        };

        InventaireService.addBatchNumber(data).then(function(e) {
            if(e.error) {
                LMBToast.error({
                    title: __(113570, 'Inventaire'),
                    message: e.error,
                    entity: LMBToast.ENTITY_STOCK
                });
            } else {
                line = angular.extend(line, e.line);
                line.new_sn = "";
            }
        });
    };

    $scope.updateQteLot = function(line, lot) {
        var data = {
            id_inventaire: $scope.inventaire.id_inventaire,
            id_inv_line: line.id_inv_line,
            id_zone: $stateParams.idZone ? $stateParams.idZone : null,
            qte: lot.qte_inventaire,
            sn: lot.sn,
            gestion_sn: line.gestion_sn,
            date: lot.date
        };

        InventaireService.updateBatchNumberQte(data).then(function(e) {
            lot.qte_inventaire = parseInt(e.qte);
            line.qte_inventaire = e.line.qte_inventaire || 0;
        });
    };

    $scope.isDate = function(obj) {
        return obj instanceof Date;
    };

    $scope.formatDate = function(date) {
        var d = new Date(date);
        return d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear();
    };

    $scope.isMultiZones = function () {
        const zones = InventaireDataService.getZones();
        return zones.length >= 2;
    }

    $scope.isNewPage = function() {
        const status = $scope.inventaireStatus;
        return $scope.isStatus(status.todo) || $scope.isStatus(status.setup);
    }

    $scope.isTab = function(tab) {
        return $scope.options.selectedPage === tab;
    }

    $scope.setTab = function (tab) {
        $scope.options.selectedPage = tab;
    }

    $scope.isStatus = function(status) {
        return $scope.inventaire.statut === status;
    }

    $scope.refreshHistory = function() {
        var data = {
            id_inventaire: $scope.inventaire.id_inventaire
        };
        InventaireService.getHistory(data).then(function(res) {
            const handledHistories = $scope.handleHistories(res.history);
            $scope.history = handledHistories;
        });
    };

    $scope.handleHistories = function (histories)  {
        if (histories.length <=0) return [];
        return histories.map(history => {
            history.description = $scope.getHistoryDescription(history);
            return history;
        })
    }

    const HistoryDescriptions = {
        setup:  __(520044, "Paramétrage de l'inventaire"),
        inprogress: __(520045, "Démarrage de l'inventaire"),
        todo: __(520046, "Recommencement de l'inventaire"),
        tofinalise: __(520047, "Finalisation de l'inventaire"),
        return: __(520048, "Retour à l'inventaire"),
        validating: __(520049, "Validation de l'inventaire"),
        done: __(520050, "Inventaire validé")
    };

    $scope.getHistoryDescription = function (event)  {
        if (event.ref_event_type === 'INVENTAIRES_CREATE') {
            if (event.params.creation.new=='convert') {
                return __(520042, "Converti à partir d'un ancien inventaire");
            } else if (event.params.creation.new==true) {
                return __(520043, "Création de l'inventaire");
            }
        } else if (event.ref_event_type === 'INVENTAIRES_UPDATE_STATUT') {
            if (event.params.statut.old === 'setup' && (event.params.statut.new === 'todo' || event.params.statut.new === 'setup')) {
                return __(520044, "Paramétrage de l'inventaire");
            } else if ((event.params.statut.old === 'todo' || event.params.statut.old === 'setup') && event.params.statut.new === 'inprogress') {
                return  __(520045, "Démarrage de l'inventaire");
            } else if (event.params.statut.old !== 'todo' && event.params.statut.old !== 'setup' && event.params.statut.new === 'todo') {
                return  __(520046, "Recommencement de l'inventaire");
            } else if (event.params.statut.old === 'inprogress' && event.params.statut.new === 'tofinalise') {
                return  __(520047, "Finalisation de l'inventaire");
            } else if (event.params.statut.old === 'tofinalise' && event.params.statut.new === 'inprogress') {
                return  __(520048, "Retour à l'inventaire");
            } else if (event.params.statut.old === 'tofinalise' && event.params.statut.new === 'validating') {
                return  __(520049, "Validation de l'inventaire");
            } else if (event.params.statut.old === 'validating' && event.params.statut.new === 'done') {
                return  __(520050, "Inventaire validé");
            }
        }
    }

    $scope.toggleEditInventaire = function () {
        $scope.editDateInventaire = !$scope.editDateInventaire;
    }

    $scope.updateDateInventaire = function () {
        $scope.toggleEditInventaire();
        if (new Date($scope.dateInventaire).setHours(0,0,0,0) == new Date($scope.dateInventaireOld).setHours(0,0,0,0)) {
            return;
        }
        if ($scope.inventaire.id_inventaire) {
            const data = {
                id_inventaire: $scope.inventaire.id_inventaire,
                date_inventaire: $scope.dateInventaire
            };

            InventaireService.updateDateInventaire(data).then(function (res) {
                if (res && res.id_inventaire) {
                    $scope.dateInventaireOld = res.date_inventaire;
                    LMBToast.success({
                        title: __(520019, 'Inventaire'),
                        message: __(520020, "Mise à jour réussie"),
                        entity: LMBToast.ENTITY_STOCK
                    });
                } else {
                    LMBToast.error({
                        title: __(520021, 'Inventaire'),
                        message: __(520022, "Une erreur s'est produite. Veuillez réessayer"),
                        entity: LMBToast.ENTITY_STOCK
                    });
                }
            });
        } else {
            $scope.createInventaire();
        }
    }

    $scope.updateOperateur = function (id_contact) {
        if ($scope.inventaire.id_inventaire) {
            if (id_contact && !Array.isArray(id_contact)) {
                const data = {
                    id_inventaire: $scope.inventaire.id_inventaire,
                    id_contact: id_contact
                };
                InventaireService.updateOperateurInventaire(data).then(function (res) {
                    if (res && res.id_user && res.id_inventaire) {
                        $scope.inventaire.id_user = res.id_user;
                    } else {
                        LMBToast.error({
                            title: __(520021, 'Inventaire'),
                            message: res.error
                        });
                    }
                });
            }
        } else {
            if (!$scope.isCreatingInventaire) {
                $scope.isCreatingInventaire = true;
                $scope.createInventaire().then(function (resp) {
                    $scope.isCreatingInventaire = false;
                }).catch(function (error) {
                    console.error("Erreur lors de la création de l'inventaire:", error);
                    $scope.isCreatingInventaire = false;
                    $rootScope.isApiCreateInventaireCalled = false;
                });
            }
        }
    };

    $scope.updateLibInventaire = function () {
        if ($scope.inventaire.id_inventaire) {
            const data = {
                id_inventaire: $scope.inventaire.id_inventaire,
                lib: $scope.inventaire.lib
            };
            InventaireService.updateLibInventaire(data).then(function (res) {
                if (res && res.id_inventaire) {

                } else {
                    LMBToast.error({
                        title: __(520021, 'Inventaire'),
                        message: __(520022, "Une erreur s'est produite. Veuillez réessayer"),
                        entity: LMBToast.ENTITY_STOCK
                    });
                }
            });
        } else {
            if (!$scope.isCreatingInventaire) {
                $scope.isCreatingInventaire = true;
                $scope.createInventaire().then(function (resp) {
                    $scope.isCreatingInventaire = false;
                }).catch(function (error) {
                    console.error("Erreur lors de la création de l'inventaire:", error);
                    $scope.isCreatingInventaire = false;
                    $rootScope.isApiCreateInventaireCalled = false;
                });
            }
        }
    }

}]);

})(window.angular);
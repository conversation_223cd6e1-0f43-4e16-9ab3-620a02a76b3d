#page-opportunite-pipeline > .container {
    overflow-x: auto;
}
#page-opportunite-pipeline .dropdown-menu {
    z-index: 2;
}
.steps-container {
    min-height: 75vh;
    table-layout: fixed;
}
.steps-container>thead th {
    color: #FFF;
    padding: 11px;
    font-weight: lighter;
    font-size: 14px;
    text-align: center;
    position: relative;
    min-width: 300px;
}
.steps-container>thead>tr>th:not(:last-child) .arrow-right {
    display: block;
    position: absolute;
    right: -37px;
    top: -7px;
    font-size: 55px;
    line-height: 100%;
    z-index: 1;
}
.steps-container>thead>tr>th:last-child .arrow-right {
    display: none;
}
.steps-container>tbody>tr>td {
    vertical-align: top;
    background-color: white;
}
.steps-container>tbody>tr>td:not(:last-child) {
    border-right: 1px solid #dadada;
}
.step-progress {
    width: 100%;
}
.step-progress-content {
    height: 3px;
    width:1%;
    background-color: #01aef2;
    -webkit-transition: width .2s;
    -moz-transition: width .2s;
    -ms-transition: width .2s;
    -o-transition: width .2s;
    transition: width .2s;
}
.step-content {
    padding: 5px 8px 50px;
}
.step-amount {
    font-size: 25px;
    font-weight: lighter;
    padding: 7px 0;
    text-align: right;
}
.card-container {
    height: 100%;
    min-height: 100px;
    padding-bottom: 130px !important;
}
.card-container > .card + .card {
    margin-top: 10px;
}
.card {
    background-color: #F3F3F3;
    padding: 9px;
    border: 1px solid #dadada;
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
}
.card-content {
    padding: 8px;
}
.card.ui-sortable-placeholder {
    border: 2px dashed #808080 !important;
    background-color: rgba(237, 241, 242, 0.54) !important;
    visibility: visible !important;
    height:99px;
}
.card-contact {
    font-weight: bold;
    font-size: 1.1em;
}
.card-amount {
    font-weight: 700;
}
.card-actions {
    text-align: right;
}
.card-actions {
    text-align: right;
    color:black;
}
.card.tilt {
    -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    -webkit-transition: transform .2s;
    -moz-transition: transform .2s;
    -ms-transition: transform .2s;
    -o-transition: transform .2s;
    transition: transform .2s;
}
.card.tilt.rot-right {
    transform: rotate(3deg);
    -moz-transform: rotate(3deg);
    -webkit-transform: rotate(3deg);
}
.card.tilt.rot-left {
    transform: rotate(-3deg);
    -moz-transform: rotate(-3deg);
    -webkit-transform: rotate(-3deg);
}
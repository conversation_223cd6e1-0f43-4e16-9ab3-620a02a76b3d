app.controller('ManageController', ["$scope", '$filter', "OpportuniteService", "LMBModalService", function ($scope, $filter, OpportuniteService, LMBModalService) {

    $scope.opportunite = {
        type: '',
        budget: 0,
        delay: '',
        origin: {
            id_client_origine: null
        },
        title: '',
        description: '',
        taux_succes: '',
        etat: 'encours',
        previous: 'encours',
        competitors: [],
        caracs: []
    };
    $scope.search = {
        form: {
            nom_complet: '',
            phone: '',
            email: '',
            id_type: null,
            id_categorie: null
        }
    };

    $scope.etats = [
        {
            value: 'encours',
            lib: 'En cours'
        },
        {
            value: 'gagnee',
            lib: 'Gagn<PERSON>'
        },
        {
            value: 'perdue',
            lib: 'Perdue'
        },
        {
            value: 'annule',
            lib: 'Annulée'
        },
        {
            value: 'stopped',
            lib: 'Arrêtée'
        }
    ];

    $scope.pieces_jointes = {};
    $scope.images = {};

    $scope.position = 1;
    $scope.caracsToUnlink = [];
    $scope.allCaracs = [];
    $scope.availableCaracs = [
        {
            id_carac: '0',
            lib_carac: 'Autres caractéristiques'
        }
    ];

    $scope.selectedCarac = {
        id_carac: '0',
        lib_carac: 'Autres caractéristiques'
    };

    $scope.$on('competitorForm-remove', function(event, args){
        $scope.removeCompetitor(args);
    });

    $scope.$on('competitorForm-clean', function(event, args){
        $scope.cleanCompetitor(args);
    });

    $scope.$on('competitorForm-select', function(event, args){
        $scope.opportunite.competitors[args.position - 1] = args
    });

    $scope.init = function(id, page) {
        OpportuniteService.getConfigs().then(function(datas) {
            $scope.competitors = datas.data.competitors;
            $scope.origines = datas.data.origines;
            if(id) {
                OpportuniteService.getDatas(id).then(function(datas) {
                    $scope.opportunite = datas.opportunite;
                    $scope.opportunite.taux_succes = parseInt(datas.opportunite.taux_succes);
                    $scope.opportunite.taux_succes_value = parseInt(datas.opportunite.taux_succes);
                    $scope.opportunite.previous = $scope.opportunite.etat;


                    if(datas.opportunite.id_contact !== null){
                        $scope.contactSelected = new Contact(datas.opportunite.id_contact, datas.search.form.nom_complet, datas.search.form.initiales, datas.search.form.phone, datas.search.form.email, "", "", datas.search.form.id_type, datas.search.form.id_categorie, datas.search.form.lib_categorie, datas.search.form.code, datas.search.form.statut, datas.search.form.picture_link, "");
                    }

                    $scope.competitorSelected = $scope.opportunite.competitors;

                    angular.forEach($scope.competitorSelected, function(value, key) {
                        $scope.removeCompetitor(value);
                    });

                    $scope.pieces_jointes = angular.extend($scope.pieces_jointes, datas.pieces_jointes);

                    $scope.images = angular.extend($scope.images, datas.images);
                    $scope.position = $scope.opportunite.competitors.length + 1;
                    $scope.delais = datas.delais;

                    $scope.types_opportunite = datas.types;

                    $scope.allCaracs = $scope.availableCaracs.concat(datas.caracs);

                    //$scope.updateLinkedAndAvailableCaracs();
                    var allCaracs = angular.extend([], $scope.allCaracs);

                    angular.forEach($scope.opportunite.caracs, function(carac){
                        allCaracs = $filter('filter')(allCaracs, {id_carac: '!' + carac.id_carac});
                        $scope.opportunite.type.caracs = $filter('filter')($scope.opportunite.type.caracs, {id_carac: '!' + carac.id_carac})
                    });
                    $scope.availableCaracs = allCaracs;

                    $scope.search = datas.search;

                    $scope.$on('contactSelect-loaded', function (){
                        $scope.search = datas.search;
                    });
                    if (!$scope.opportunite.origin.id_client_origine) {
                        $j('select[customer-origins] option').first().text('Non définie');
                    }
                });
            }
            else{
                $scope.competitorSelected = [];
                $scope.position = 1;
                $scope.addCompetitor();

                OpportuniteService.getAllCaracs().then(function(response){
                    $scope.allCaracs = response.data.caracs;

                    OpportuniteService.getTypes().then(function(datas){
                        $scope.types_opportunite = datas.types;
                        $scope.changeType();
                        $j('select[customer-origins] option').first().text('Non définie');
                    });
                });
            }

            $j('#competitorSelect option').first().text('Sélectionner un concurrent');
        });

    };

    $scope.changeType = function(old_type){
        angular.forEach($scope.opportunite.caracs, function(carac){
            $scope.unlinkCarac(carac);
        });

        if($scope.opportunite.id_opportunite === undefined){
            $scope.changeOpportuniteDelai();
        }

        $scope.updateLinkedAndAvailableCaracs();

        if (!old_type || old_type.defaut_titre == "" || old_type.defaut_titre == $scope.opportunite.title) {
            $scope.opportunite.title = $scope.opportunite.type.defaut_titre;
        }
    };

    $scope.changeOpportuniteDelai = function(){
        $scope.opportunite.delay = {};
        if($scope.opportunite.type === ''){
            $scope.opportunite.type = $scope.types_opportunite[0];
        }
        $scope.opportunite.type.delais.unshift({id_opportunite_delai:null,lib:'Non défini'});
        $scope.opportunite.delay = $scope.opportunite.type.delais[0];
    };

    $scope.updateLinkedAndAvailableCaracs = function(){
        $scope.opportunite.caracs = [];
        var allCaracs = angular.extend([], $scope.allCaracs);

        angular.forEach($scope.opportunite.caracs, function(carac){
            allCaracs = $filter('filter')(allCaracs, {id_carac: '!' + carac.id_carac});
            $scope.opportunite.type.caracs = $filter('filter')($scope.opportunite.type.caracs, {id_carac: '!' + carac.id_carac})
        });

        angular.forEach($scope.opportunite.type.caracs, function(carac){
            allCaracs = $filter('filter')(allCaracs, {id_carac: '!' + carac.id_carac});
        });

        $scope.availableCaracs = allCaracs;

        angular.forEach( $scope.opportunite.type.caracs, function(carac){
            $scope.linkPreselectedCarac(carac)
        });
    };

    $scope.linkCarac = function(){
        if($scope.selectedCarac.id_carac !== '0'){
            $scope.opportunite.caracs.push($scope.selectedCarac);
            $scope.removeCaracFromList($scope.selectedCarac, $scope.availableCaracs);
            $scope.removeCaracFromList($scope.selectedCarac.id_carac, $scope.caracsToUnlink);
            $scope.selectedCarac = $scope.availableCaracs[0];
        }
    };
    $scope.linkPreselectedCarac = function(carac){
        if(carac !== '0'){
            $scope.opportunite.caracs.push(carac);
            $scope.removeCaracFromList(carac, $scope.availableCaracs);
            $scope.selectedCarac = $scope.availableCaracs[0];
        }
    };
    $scope.linkPreselectedCarac = function(carac){
        if(carac !== '0'){
            $scope.opportunite.caracs.push(carac);
            $scope.removeCaracFromList(carac, $scope.availableCaracs);
            $scope.selectedCarac = $scope.availableCaracs[0];
        }
    };

    $scope.unlinkCarac = function(carac){
        $scope.availableCaracs.push(carac);
        $scope.removeCaracFromList(carac, $scope.opportunite.caracs);
        $scope.caracsToUnlink.push(carac.id_carac);
    };

    $scope.getCaracById = function(idCarac, source){
        var result = false;
        var nbCaracs = source.length;
        var i = 0;

        while(i < nbCaracs && result === false){
            if(source[i].id_carac == idCarac){
                result = source[i];
            }

            i++;
        }

        return result;
    };

    $scope.removeCaracFromList = function(carac, source){
        var nbCaracs = source.length;
        var found = false;
        var i = 0;

        while (i < nbCaracs && !found){
            if(source[i].id_carac == carac.id_carac){
                found = true;
                source.splice(i, 1);
            }
            i++;
        }
    };

    $scope.saveOpportunite = function() {
        if(!$scope.contactSelected.isEmpty()) {
            // __Récupération des données sur le client sélectionné
            $scope.opportunite.nom_complet = $scope.contactSelected.nom_complet;
            $scope.opportunite.phone = $scope.contactSelected.phone;
            $scope.opportunite.email = $scope.contactSelected.email;
            $scope.opportunite.id_type = $scope.contactSelected.id_type;
            $scope.opportunite.id_categorie = $scope.contactSelected.id_categorie;
            $scope.opportunite.id_contact = $scope.contactSelected.id;
        }
        else {
            // __Récupération des données du formulaire
            $scope.opportunite.nom_complet = $scope.search.form.nom_complet || '';
            $scope.opportunite.phone = $scope.search.form.phone || '';
            $scope.opportunite.email = $scope.search.form.email || '';
            $scope.opportunite.id_type = $scope.search.form.id_type || '';
            $scope.opportunite.id_categorie = $scope.search.form.id_categorie || '';
            $scope.opportunite.id_contact = null;
        }

        $scope.opportunite.pieces_jointes = $scope.pieces_jointes;
        $scope.opportunite.images = $scope.images;

        var caracsList = {
            to_link: [],
            to_unlink: $scope.caracsToUnlink
        };
        var emptyValues = [
            '',
            null,
            undefined
        ];

        angular.forEach($scope.opportunite.caracs, function(carac){
            if(emptyValues.indexOf(carac.valeur) === -1){
                if(emptyValues.indexOf(carac.valeur.start) === -1 && emptyValues.indexOf(carac.valeur.end) === -1){
                    caracsList.to_link.push({
                        id_carac: carac.id_carac,
                        content: {
                            start: carac.valeur.start,
                            end: carac.valeur.end
                        }
                    });
                }
                else{
                    caracsList.to_link.push({
                        id_carac: carac.id_carac,
                        content: carac.valeur
                    });
                }
            }
            else{
                if(opportunite.id_opportunite !== undefined){
                    caracsList.to_unlink.push(carac.id_carac);
                }
            }
        });

        $scope.opportunite.caracs = caracsList;

        /*if($scope.opportunite.taux_succes === 'otherValue'){
            $scope.opportunite.taux_succes = '';
        }else{
            $scope.opportunite.taux_succes = parseInt($scope.opportunite.taux_succes);
        }*/

        $scope.opportunite.competitors = $scope.competitorSelected;
        $scope.opportunite.origin = $scope.opportunite.origin.id_client_origine;

        //  __Appel du service Opportunite pour sauvegarder dans la BDD
        OpportuniteService.save($scope.opportunite);
    };

    $scope.ajouterConcurrent = function () {
        LMBModalService.showModal({
            LMBModalConfig: {
                titre: __(140247,"Ajouter un concurrent"),
                drag: true,
                resizable: true,
                overlay: true,
                style: {width: "475px", height: "200px"}
            },
            angularModal: {
                config: {
                    templateUrl: "page.php/Components/Standard/Template/View:phtml/pp_live_create.phtml",
                    controller: "LiveCreateController",
                    inputs: {
                        titleLiveCreate: "",
                        labelLiveCreate: __(140248,"Nom du concurrent à créer"),
                        fonctionLiveCreate: "createConcurrent"
                    }
                },
                then: function (modal) {},
                onClose: function (result) {
                    if(result.id) {
                        $scope.competitors.push({id_competitor:result.id,lib: result.lib})
                    }
                }
            }
        });
    };

    $scope.setCompetitors = function () {
        var filteredArray = $scope.competitors.filter(function(e) {
            if(e.id_competitor !== $scope.competitorSelection) {
                return e.id_competitor;
            }
            else{
                $scope.competitorSelection = e;
                $scope.competitorSelection.note = $scope.competitorSelection.note || '';
            }
        });
        $scope.competitors = filteredArray;
        $scope.competitorSelected.push($scope.competitorSelection);
    };

    $scope.removeSelectedCompetitor = function (competitor) {
        var filteredArray = $scope.competitorSelected.filter(function(e) {
            if(e.id_competitor !== competitor.id_competitor) {
                return e;
            }
        });
        $scope.competitorSelected = filteredArray;
        $scope.competitors.push(competitor);
    };

    $scope.removeCompetitor = function (competitor) {
        var filteredArray = $scope.competitors.filter(function(e) {
            if(e.id_competitor !== competitor.id_competitor) {
                return e;
            }
        });
        $scope.competitors = filteredArray;
    };

    $scope.addCompetitor = function(){
        if($scope.opportunite.competitors.length == 0 || ($scope.opportunite.competitors.length > 0 && $scope.opportunite.competitors[$scope.opportunite.competitors.length-1].lib!=='')) {
            $scope.opportunite.competitors.push({
                id_competitor: null,
                lib: '',
                actif: 1,
                position: $scope.position,
                note: ''
            });
            $scope.position++;
        }
    };

    $scope.showDesc = function (className) {
        $j(className).toggle();
    };

    /*$scope.removeCompetitor = function(position){
        if($scope.position > 0){
            $scope.position--;
        }

        var nbCompetitors = $scope.opportunite.competitors.length;
        var i = 0;
        var competitorFound = false;

        while(i < nbCompetitors && !competitorFound){
            if($scope.opportunite.competitors[i].position == position){
                competitorFound = i;
            }

            i++;
        }

        if(competitorFound !== false){
            $scope.opportunite.competitors.splice(competitorFound, 1);
            nbCompetitors = $scope.opportunite.competitors.length;
            for(var i = competitorFound; i < nbCompetitors; i++){
                $scope.opportunite.competitors[i].position--;
            }
        }
    };*/

    $scope.cleanCompetitor = function(position){
        if($scope.position > 0){
            $scope.position--;
        }

        var nbCompetitors = $scope.opportunite.competitors.length;
        var i = 0;
        var competitorFound = false;

        while(i < nbCompetitors && !competitorFound){
            if($scope.opportunite.competitors[i].position == position){
                $scope.opportunite.competitors[i].id_competitor= null;
                $scope.opportunite.competitors[i].lib= '';
                $scope.opportunite.competitors[i].note= '';
                $scope.opportunite.competitors[i].actif= 1;
                $scope.opportunite.competitors[i].etat= 'encourse';
                return 0;
            }

            i++;
        }
    };
}])
    .controller('LiveCreateController', ['$scope', '$element', 'OpportuniteService', 'LiveCreateService', 'close', 'titleLiveCreate', 'labelLiveCreate', 'fonctionLiveCreate', function($scope, $element, OpportuniteService, LiveCreateService, close, titleLiveCreate, labelLiveCreate, fonctionLiveCreate) {
        $scope.display = true;

        $scope.titleLC = titleLiveCreate;
        $scope.labelLC = labelLiveCreate;
        $scope.fonctionLC = fonctionLiveCreate;

        var libInput = "";
        var msgLibelleRequis = "";

        var stop = false;

        $scope.close = function() {
            $scope.display = false;
            close({
                objet: {},
                selected: false
            }, 0);
        };

        $scope.appelLC = function(fun) {
            if(typeof $scope[fun] === 'function') {
                $scope[fun]();
            } else {
                console.log('Fonction '+fun+' introuvable.');
            }
        };

        $scope.createConcurrent = function() {
            if($scope.libInput) {
                LiveCreateService.createConcurrent($scope.libInput).then(
                    function(result) {
                        close({
                            id: result.idCompet,
                            lib: result.libCompet
                        }, 0);

                        LMBToast.success({
                            title: __(140249,"Nouveau concurrent"),
                            message: __(140250,"Le concurrent <strong> {name} </strong> a été créé avec succès !",{name:$scope.libInput})
                        });
                    });
            } else {
                $scope.msgLibelleRequis = __(140251,"Le libellé est requis !");
            }
        }

    }]);

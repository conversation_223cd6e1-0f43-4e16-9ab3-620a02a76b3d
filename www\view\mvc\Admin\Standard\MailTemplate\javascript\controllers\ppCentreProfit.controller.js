'use strict';

(function () {
    angular
        .module('MailTemplate')
        .controller('PPCentreProfitController', PPCentreProfitController);

        PPCentreProfitController.$inject = ['$scope', 'MailTemplateApi'];

    function PPCentreProfitController($scope, MailTemplateApi) {
        $scope.idMailTemplate = null;
        $scope.mailTemplate = null;
        $scope.configs = [];
        $scope.configRedirections = [];
        $scope.selectData = {
            selectedMagasins: [],
            selectedMagasinsGroups: [],
            selectedEnseignes: [],
            selectedModeleRedirection: null
        };
        $scope.mailTemplates = [];
        $scope.pageLoading = false;

        $scope.init = function (idMailTemplate) {
            $scope.idMailTemplate = idMailTemplate;
            $scope.load();
        }

        $scope.load = function () {
            $scope.selectData = {
                selectedMagasins: [],
                selectedMagasinsGroups: [],
                selectedEnseignes: [],
                selectedModeleRedirection: null
            };
            if (!$scope.idMailTemplate) return;
            $scope.pageLoading = true;
            let data = {
                id_mail_template: $scope.idMailTemplate,
                load_config_magasins: 1
            };
            MailTemplateApi.load(data).then(function (res) {
                if (res.error) {
                    
                } else {
                    $scope.mailTemplate = res.mailTemplate;
                    $scope.configRedirections = res.mailTemplate.config_redirection;
                    $scope.configs = res.configs;
                    $scope.loadMailTemplate();
                    
                    $scope.loadConfigRedirection();
                }
                $scope.pageLoading = false;
            });
        }

        $scope.add = function () {
            if (!$scope.idMailTemplate) return;
            $scope.pageLoading = true;
            let data = {
                id_mail_template: $scope.idMailTemplate,
                magasins: $scope.selectData.selectedMagasins,
                magasins_groups: $scope.selectData.selectedMagasinsGroups,
                enseignes: $scope.selectData.selectedEnseignes,
                modele_mail: $scope.selectData.selectedModeleRedirection
            }
            MailTemplateApi.saveMagasin(data).then(function (res) {
                if (res.error) {
                    LMBToast.error({
						message: _e_html(580958,"Une erreur est survenue")
					});
                } else {
                  
                }
                $scope.load();
                $scope.pageLoading = false;
            });
        }

        $scope.disableAdd = function () {
            return !$scope.selectData.selectedModeleRedirection ||
                (!$scope.selectData.selectedMagasins && !$scope.selectData.selectedMagasinsGroups && !$scope.selectData.selectedEnseigne)
        }

        $scope.openTemplateMail = function (id_template) {
            window.open('#modele_mail.php?ref=' + id_template,"_blank");
        }

        $scope.loadConfigRedirection = function() {
            if (!$scope.configRedirections || $scope.configRedirections.length == 0) {
                return;
            }
            $scope.configRedirections = $scope.configRedirections.map(redirection => {
                if (!redirection.modele_mail) return;
                if (redirection.magasins) {
                    redirection.magasins = redirection.magasins.map(id_magasin => {
                        return {
                            id:id_magasin,
                            lib: $scope.configs.magasins[id_magasin].lib || ''
                        }
                    });
                }
                if (redirection.magasins_groups) {
                    redirection.magasins_groups = redirection.magasins_groups.map(id_magasin_group => {
                        return {
                            id: id_magasin_group,
                            lib: $scope.configs.magasins_groups[id_magasin_group].lib || ''
                        }
                    });
                }

                if (redirection.enseignes) {
                    redirection.enseignes = redirection.enseignes.map(id_enseigne => {

                        return {
                            id: id_enseigne,
                            lib: $scope.configs.enseignes[id_enseigne].lib || ''
                        }
                    });
                }

                redirection.lib_modele_mail = $scope.configs.modeles[redirection.modele_mail].lib;

                return redirection;

            })
        }

        $scope.loadMailTemplate = function () {
            $scope.mailTemplates = Object.values($scope.configs.modeles);
            $scope.mailTemplates = $scope.mailTemplates.filter(mail => !mail.config_redirection && mail.id != $scope.idMailTemplate);
            $scope.mailTemplates.unshift({id: "add_new", lib:"Créer un nouveau modèle"});
        }

        $scope.deleteRedirectionMagasin = function (index) {
            LMBTools.confirm({
                content: "Êtes vous sûr de vouloir supprimer ce config ?",
                confirm: __(112610, "Supprimer"),
                onValid: function (confirm) {
                    if (confirm) {
                        let data = {
                            id_mail_template: $scope.idMailTemplate,
                            index: index
                        }
                        MailTemplateApi.deleteRedirectionMagasin(data).then(function (res) {
                            if (res.error || res.result != 'ok') {
                                LMBToast.error({
                                    message: _e_html(580958,"Une erreur est survenue")
                                });
                            } else {
                                $scope.load();
                            }
                        });
                    }
                }
            });
           
        }
    }
})();

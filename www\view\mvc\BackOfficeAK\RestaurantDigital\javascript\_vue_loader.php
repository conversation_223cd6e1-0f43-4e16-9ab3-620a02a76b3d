<?php

$JS_DIR = "view/mvc/BackOfficeAK/RestaurantDigital/javascript/vue";
$files = [];

$js_content = "";
foreach (glob($DIR . $JS_DIR . "{,/*}", GLOB_BRACE|GLOB_ONLYDIR) as $dirjs) {
    foreach (glob($dirjs . "/*.js") as $filejs) {
        echo '<script type="module" src="' . langage::parse($filejs) . '?rev=' . lmbconfig::getInstance()->get("SYS_version") . '"></script>';
        $filename = substr($filejs, strlen($DIR.$JS_DIR) + 1);
        $files[$filename] = langage::parse($filejs) . '?rev=' . lmbconfig::getInstance()->get("SYS_version");
    }
}

return $files;

<tr ng-repeat="(nom , data) in transporteur.config.standard">
    <!-- Label de l'input -->
    <td>
        <label for="{{nom.split('.').join('')}}">{{data.name || nom}} <i class="lm-info3" data-lmb-infobulle="{{nom}}"></i></label>
    </td>

    <!-- Input selon le type -->
    <td ng-if="data.type==='text'">
        <input type="text" ng-model="data.default" name="{{nom}}" id="{{nom.split('.').join('')}}"
               maxlength="{{data.maxlength}}" class="input-full" data-lmb-infobulle="{{data.hint}}"
               ng-blur="filter(data)"/>
    </td>
    <td ng-if="data.type==='number'">
        <input ng-model="data.default" type="text" name="{{nom}}" id="{{nom.split('.').join('')}}"
               lmb-number="{{data.numberType}}" class="input-full" data-lmb-infobulle="{{data.hint}}"
               maxlength="{{data.maxlength}}" ng-blur="filter(data)"/>
    </td>
    <td ng-if="data.type==='idPays'">
        <select ng-model="data.default" name="{{nom}}" id="{{nom.split('.').join('')}}"
                data-lmb-country="{'value':'{{data.idPays}}'}" class="input-full"></select>
    </td>
    <td ng-if="data.type==='idEtat'">
        <select ng-model="data.default" id="{{nom.split('.').join('')}}"
                data-lmb-country-states="{'idSelectCountry':'{{data.idSelecteurPays.split('.').join('')}}','value':'{{data.default ? data.default : 'null'}}','onEmpty': { 'hidden': true }}"
                class="input-full"></select>
    </td>
    <td ng-if="data.type==='select'">
        <select ng-model="data.default" name="{{nom}}" id="{{nom.split('.').join('')}}"
                data-lmb-infobulle="{{data.hint}}">
            <option value="" ng-selected="!data.default">Aucune option par défaut</option>
            <option ng-repeat="option in data.options" ng-selected="option===data.default"
                    value="{{option}}">{{option}}
            </option>
        </select>
    </td>
    <td ng-if="data.type==='selectWithLabel'">
        <select ng-model="data.default" name="{{nom}}" id="{{nom.split('.').join('')}}"
                data-lmb-infobulle="{{data.hint}}">
            <option value="" ng-selected="!data.default">Aucune option par défaut</option>
            <option ng-repeat="option in data.options" value="{{option.value}}" ng-selected="option.value===data.default">
                {{option.label}}
            </option>
        </select>
    </td>
    <td ng-if="data.type==='codePays'">
        <select id="{{nom.split('.').join('')}}" name="{{nom}}" ng-model="data.default"
                class="input-full" ng-change="updateChampEtat(nom , data.default)">
            <option value="" ng-selected="!data.default">Aucune option par défaut</option>
            <option ng-repeat="optionPays in pays.liste" ng-if="optionPays.favori==='1'"
                    value="{{optionPays.code_pays}}"  ng-selected="optionPays.code_pays===data.default">
                {{optionPays.code_pays}} - {{optionPays.pays}}
            </option>
            <optgroup label="__________________________________"></optgroup>
            <option ng-repeat="optionPays in pays.liste" value="{{optionPays.code_pays}}"
                    ng-if="optionPays.favori!=='1'" ng-selected="optionPays.code_pays===data.default">
                {{optionPays.code_pays}} - {{optionPays.pays}}
            </option>
        </select>
    </td>
    <td ng-if="data.type==='codeEtat'">
        <select ng-if="data.idSelecteurPays && listeEtats[data.idSelecteurPays.split('.').join('')]"
                id="{{nom.split('.').join('')}}" name="{{nom}}" ng-model="data.default"
                class="input-full">
            <option value="" ng-selected="!data.default">Aucune option par défaut</option>
            <option ng-repeat="etat in listeEtats[data.idSelecteurPays.split('.').join('')]"
                    value="{{etat.code_iso_5c}}" ng-selected="etat.code_iso_5c===data.default">
                {{etat.code_iso_5c}} - {{etat.nom}}
            </option>
        </select>
    </td>
    <td ng-if="data.type==='constant'">
        <input id="{{nom.split('.').join('')}}" name="{{nom}}" type="text" ng-model="data.default"
               readonly
               ng-style="{'border-color': data.default!==data.supposed ? '#d43f3a' : 'inherit'}">
        <button type="button" ng-click="resetConstant(data)">Reset default</button>
    </td>
    <td ng-if="data.type==='codeLangage'">
        <select id="{{nom.split('.').join('')}}" name="{{nom}}" ng-model="data.default">
            <option value="" ng-selected="!data.default">Aucune option par défaut</option>
            <option ng-repeat="langue in langages" value="{{data.lowercase ? (langue.code_langage | lowercase) : langue.code_langage}}"
                    ng-selected="(data.lowercase ? (langue.code_langage | lowercase) : langue.code_langage)===data.default">
                {{data.lowercase ? (langue.code_langage | lowercase) : langue.code_langage}} - {{langue.lib_langage}}
            </option>
        </select>
    </td>
    <!-- Fin input selon le type -->
</tr>
<tr>
    <td style="border:0" class="text-left"><h3>Valeurs non standards</h3></td>
    <td><button type="button" ng-click="ajouter('nomStructure')" class="btn btn-secondary">Ajouter une valeur non standard</button></td>
</tr>
<tr ng-repeat="(clef , configPerso) in transporteur.config.hors_structure">
    <td>
        <input type="text" ng-model="configPerso[0]" name="nom{{clef}}" id="nom{{clef}}"
               class="input-full" title="Nom de la valeur, ne pas assigner deux fois le même nom"/>
    </td>
    <td>
        <input type="text" ng-model="configPerso[1]" name="val{{clef}}" id="val{{clef}}"
               class="input-full" title="Valeur par défaut (non obligatoire)"/>
    </td>
</tr>
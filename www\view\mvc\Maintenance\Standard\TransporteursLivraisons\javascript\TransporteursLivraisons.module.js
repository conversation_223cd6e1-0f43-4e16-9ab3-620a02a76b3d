/**
 * Fichier du module angularJS contenant toutes les routes ui-router de la page maintenance concernant les transporteurs.
 * Chacune doit être enregistrée dans le stateProvider. Elles possèdent leur propre controller pour pouvoir séparer les
 * fonctions selon quelle partie en a besoin. Les appels api se font uniquement par le service associé 'TransporteursLivraisonsApi'
 * qui viens standardiser les appels api, passant par un unique controller. Avant de charger une page, nous faisons un
 * "resolve" de ses données pour éviter qu'avec les ng-repeat la page tréssaute à l'affichage.
 */
'use strict';

(function () {

    const TransporteursLivraisonsModule = angular.module('TransporteursLivraisonsModule', [
        'GenericServices',
        'lmb',
        'ui.router',
        'internationalisation.Directives'
    ]);

    TransporteursLivraisonsModule.config(['$stateProvider', '$urlRouterProvider', function ($stateProvider, $urlRouterProvider) {
            var pages = '/page.php/Components/Standard/Template/View:Maintenance/Standard/TransporteursLivraisons/phtml/pages/';
            var base = '/page.php/Maintenance/Standard/TransporteursLivraisons/home';

            $stateProvider
                .state('transporteurs', {
                    url: base,
                    views: {
                        "content-container": {
                            controller: 'TransporteursActifsController',
                            resolve : {
                                TransporteursLivraisonsApi : 'TransporteursLivraisonsApi',
                                transporteurs : function (TransporteursLivraisonsApi) {
                                    return TransporteursLivraisonsApi.getModulesTransport();
                                }
                            },
                            templateUrl: pages + 'transporteursActifs.phtml'
                        }
                    }
                })
                .state('modules', {
                    url: base + '/modules',
                    views: {
                        "content-container": {
                            controller: 'TransporteursConfigController',
                            resolve: {
                                commonMethodsService: 'commonMethodsService',
                                TransporteursLivraisonsApi : 'TransporteursLivraisonsApi',
                                transporteurs : function (TransporteursLivraisonsApi) {
                                    return TransporteursLivraisonsApi.getModulesActifs();
                                },
                            },
                            templateUrl: pages + 'configurationModules.phtml'
                        }
                    }
                })
                .state('modules.module', {
                    url: '/{idModule}',
                    views: {
                        "transporteur": {
                            controller: 'TransporteurConfigController',
                            resolve: {
                                commonMethodsService: 'commonMethodsService',
                                TransporteursLivraisonsApi : 'TransporteursLivraisonsApi',
                                transporteur : function (TransporteursLivraisonsApi , $stateParams) {
                                    return TransporteursLivraisonsApi.getConfigModule({idModule : $stateParams.idModule});
                                },
                                idModule: function ($stateParams) {
                                    return $stateParams.idModule
                                }
                            },
                            templateUrl: pages + 'configurationModule.phtml'
                        }
                    }
                })
                .state('livraisons', {
                    url: base + '/livraisons',
                    views: {
                        "content-container": {
                            controller: 'LivraisonsController',
                            resolve: {
                                commonMethodsService: 'commonMethodsService',
                                TransporteursLivraisonsApi : 'TransporteursLivraisonsApi',
                                transporteurs : function (TransporteursLivraisonsApi) {
                                    return TransporteursLivraisonsApi.getModulesActifs();
                                },
                                infos : function (TransporteursLivraisonsApi) {
                                    return TransporteursLivraisonsApi.getInfosGeneralesModeDeLivraison();
                                }
                            },
                            templateUrl: pages + 'livraisons.phtml'
                        }
                    }
                })
                .state('livraisons.liste', {
                    url: '/{idModule}',
                    views: {
                        "listelivraison": {
                            controller: 'LivraisonsListeController',
                            resolve: {
                                TransporteursLivraisonsApi : 'TransporteursLivraisonsApi',
                                listeModesDeLivraisons: function (TransporteursLivraisonsApi , $stateParams) {
                                    return TransporteursLivraisonsApi.getListeModesDeLivraisons({idTransportModule: $stateParams.idModule});
                                }
                            },
                            templateUrl: pages + 'livraisonsListe.phtml'
                        }
                    }
                })
                .state('livraisons.liste.spe', {
                    url: '/{idMode}',
                    views: {
                        "modeLivraison": {
                            controller: 'ModeLivraisonController',
                            resolve: {
                                commonMethodsService: 'commonMethodsService',
                                TransporteursLivraisonsApi : 'TransporteursLivraisonsApi',
                                infosModeDeLivraison: function (TransporteursLivraisonsApi , $stateParams) {
                                    return TransporteursLivraisonsApi.getInfosModeDeLivraison({idMode: $stateParams.idMode});
                                }
                            },
                            templateUrl: pages + 'modeLivraison.phtml'
                        }
                    }
                })
        }])
            .run(['$rootScope', '$window', '$state', function ($rootScope, $window, $state) {
                $rootScope.$on('$stateChangeStart',
                    function (event, toState, toParams, fromState, fromParams) {
                        if (toState.redirectTo) {
                            event.preventDefault();
                            $state.go(toState.redirectTo, toParams, {location: 'replace'});
                        } else if (!fromState.abstract) {
                            LMBNavigation.ignoreNextHashChange();
                        }
                    }
                );
                $rootScope.$on('$stateChangeSuccess',
                    function (event, toState, toParams, fromState, fromParams) {
                        $rootScope.page = toState.name
                    }
                );
                $rootScope.$on('$stateChangeError',
                    function (event, toState, toParams, fromState, fromParams) {
                    });

                $rootScope.$on('$stateNotFound',
                    function (event, toState, toParams, fromState, fromParams) {
                    });

                $rootScope.$on('$viewContentLoading',
                    function (event, viewConfig) {
                    });

                $rootScope.$on('$viewContentLoaded',
                    function (event, viewConfig) {
                    });
            }]);
    })();
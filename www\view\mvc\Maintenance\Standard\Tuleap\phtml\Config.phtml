<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Maintenance/Standard/Tuleap/javascript/_javascript_loader.php");
?>


<div class="container">
  <div class="row">
    <div class="col-1-3 col-1-2-md col-full-sm col-gutter">
        <div class="portlet">
          <div class="portlet-header">
            <h2><?php _e_html(510003, "Configuration de base"); ?></h2>
          </div>
          <div class="portlet-body">
            <table class="style-1">
              <tbody>
              <tr>
                <td>
                  <span>Id du tracker</span>
                </td>
                <td>
                  <input
                    type="text"
                    ng-model="vm.new_tracker.tracker_id"
                    value="{{ vm.new_tracker.tracker_id }}"
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <span>Reference du tracker</span>
                </td>
                <td>
                  <input
                    type="text"
                    ng-model="vm.new_tracker.tracker_ref"
                    value="{{ vm.new_tracker.tracker_ref }}"
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <span>Label du tracker</span>
                </td>
                <td>
                  <input
                    type="text"
                    ng-model="vm.new_tracker.tracker_label"
                    value="{{ vm.new_tracker.tracker_label }}"
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <span>Id du projet</span>
                </td>
                <td>
                  <input
                    type="text"
                    ng-model="vm.new_tracker.project_id"
                    value="{{ vm.new_tracker.project_id }}"
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <span>Ref du projet</span>
                </td>
                <td>
                  <input
                    type="text"
                    ng-model="vm.new_tracker.project_ref"
                    value="{{ vm.new_tracker.project_ref }}"
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <span>Id du champs statut</span>
                </td>
                <td>
                  <input
                    type="text"
                    ng-model="vm.new_tracker.artifact_status_field_id"
                    value="{{ vm.new_tracker.artifact_status_field_id }}"
                  >
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
    </div>
    <div class="col-1-3 col-1-2-md col-full-sm col-gutter">
      <div class="portlet">
        <div class="portlet-header">
          <h2>
              <?php _e_html(510004, "Configuration des associations id champs / statut"); ?>
          </h2>
        </div>
        <div class="portlet-body">
          <table class="style-1 auto-layout">
            <thead>
            <tr>
              <th class="text-center" style="width: 125px; padding: 5px 15px;">
                <span>Statut</span>
              </th>
              <th class="text-center" style="width: 125px; padding: 5px 15px;">
                <span>Id du champ</span>
              </th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            <tr
              ng-repeat="(field_id, status) in vm.new_tracker.artifact_status_fields_assoc track by field_id"
            >
              <td>
                <input
                  type="text"
                  ng-model="vm.new_tracker.artifact_status_fields_assoc[field_id]"
                  value="{{ status }}"
                  style="max-width: 125px;"
                >
              </td>
              <td>
                <input
                  type="text"
                  ng-model="field_id"
                  value="{{ field_id }}"
                  style="max-width: 125px;"
                >
              </td>
              <td>
                <button
                  type="button"
                  class="btn btn-red icon rounded"
                  ng-click="vm.removeStatusLine(field_id)"
                  data-lmb-infobulle="Supprimer l'entrée"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr>
              <th colspan="2" style="padding: 7px 20px;">
                <button type="button" class="btn btn-primary icon rounded" ng-click="vm.addStatusLine()">
                  <i class="fa fa-plus-circle"><span> Ajouter une ligne</span></i>
                </button>
              </th>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="col-1-3 col-1-2-md col-full-sm col-gutter">
      <div class="portlet">
        <div class="portlet-header">
          <h2>
              <?php _e_html(510005, "Configuration des associations status / couleurs"); ?>
          </h2>
        </div>
        <div class="portlet-body">
          <table class="style-1 auto-layout">
            <thead>
            <tr>
              <th class="text-center" style="width: 125px; padding: 5px 15px;">
                <span>Statut</span>
              </th>
              <th class="text-center" style="width: 125px; padding: 5px 15px;">
                <span>Couleur</span>
              </th>
              <th></th>
            </tr>
            </thead>
            <tbody>
            <tr
              ng-repeat="(status, color) in vm.new_tracker.artifact_status_assoc_colors track by $index"
            >
              <td>
                <input
                  type="text"
                  ng-model="status"
                  value="{{ status }}"
                  style="max-width: 125px;"
                >
              </td>
              <td>
                <input
                  type="color"
                  ng-model="vm.new_tracker.artifact_status_assoc_colors[status]"
                  value="{{ color }}"
                  style="max-width: 125px;"
                >
              </td>
              <td>
                <button
                  type="button"
                  class="btn btn-red icon rounded"
                  ng-click="vm.removeColorAssocLine(status)"
                  data-lmb-infobulle="Supprimer l'entrée"
                >
                  <i class="fa fa-trash"></i>
                </button>
              </td>
            </tr>
            <tr>
              <th colspan="2" style="padding: 7px 20px;">
                <button type="button" class="btn btn-primary icon rounded" ng-click="vm.addColorAssocLine()">
                  <i class="fa fa-plus-circle"><span> Ajouter une ligne</span></i>
                </button>
              </th>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <div class="row" style="margin-top: 30px;">
    <div class="buttons-container text-right">
      <button
        ng-if="!vm.isNew"
        type="button"
        onclick="$j(this).getModalInstance().close()"
        class="btn btn-secondary" ng-click="vm.updateConfiguration()"
      >
        <span> Valider les modifications</span>
      </button>
      <button
        ng-if="vm.isNew"
        type="button"
        onclick="$j(this).getModalInstance().close()"
        class="btn btn-tertiary" ng-click="vm.updateConfiguration(true)"
      >
        <span> Ajouter la configuration </span>
      </button>
    </div>
  </div>
</div>
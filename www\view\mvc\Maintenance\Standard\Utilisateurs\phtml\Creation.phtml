<div id="page-utilisateurs-creation" class="lmb-theme">

    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading">Application > Utilsateurs</div>
                <h1>
                    Création d'un nouvel utilisateur
                </h1>
            </div>
            <div class="jumbotron-actions">
                <a href="#/page.php/Maintenance/Standard/Utilisateurs/Home" class="btn btn-white light">
                    <i class="fa fa-chevron-left"></i> Retour à la page précédente
                </a>
            </div>
        </div>
    </header>

    <div class="container row">

        <div class="page-content">

            <div class="portlet">

                <form id="form-create-user">

                    <div class="row">

                        <div class="portlet-body col-1-2">

                            <table class="style-1">
                                <tbody>
                                <tr>
                                    <td>Nom de l'utilisateur</td>
                                    <td>
                                        <input class="input-full" type="text" value="">
                                        <input type="hidden" name="user_name" value="">
                                    </td>
                                </tr>
                                <tr>
                                    <td>Mot de passe</td>
                                    <td>
                                        <input class="input-full" type="text" value="">
                                        <input type="hidden" name="user_pass" value="">
                                    </td>
                                </tr>
                                <tr>
                                    <td>Type</td>
                                    <td>
                                        <select class="input-full" name="user_type">
                                            <option value=""></option>
                                            <?php foreach ($this->view->users_types as $user_type): ?>
                                                <option value="<?= $user_type['id'] ?>"><?= $user_type['label'] ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Utilisateur système</td>
                                    <td>
                                        <input type="checkbox" class="checkbox-switch" name="user_system">
                                    </td>
                                </tr>
                                <tr>
                                    <td>Utilisateur Webservice</td>
                                    <td>
                                        <input type="checkbox" class="checkbox-switch" name="user_webservice">
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="portlet-body col-1-2">

                            <table class="style-1">
                                <tbody>
                                <tr>
                                    <td>Nom autogénéré</td>
                                    <td>
                                        <input type="checkbox" class="checkbox-switch" name="name_auto">
                                    </td>
                                </tr>
                                <tr>
                                    <td>Mot de passe autogénéré</td>
                                    <td>
                                        <input type="checkbox" class="checkbox-switch" name="pass_auto">
                                    </td>
                                </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="row text-center">

                        <button id="create-user" type="button" class="btn btn-primary">Créer l'utilisateur</button>

                    </div>

                </form>
            </div>

        </div>

    </div>

</div>

<script type="text/javascript">
    (function($){

        $('#create-user').on('click', function() {
            LMBTools.post({
                url: "page.php/Maintenance/Standard/Utilisateurs/create",
                data: $("#form-create-user").serializeJSON(),
                success: function(result) {
                    if (result.message) {
                        LMBTools.alert(result.message);
                        if (result.success) {
                            $("#create-user").prop("disabled", true);
                        }
                    } else {
                        LMBTools.alert("<b class='text-error'>Une erreur est survenue.</b>");
                    }
                }
            });
        });

    })(jQuery);
</script>
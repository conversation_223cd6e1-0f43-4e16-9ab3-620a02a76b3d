# Contributing

First of all, **thank you** for contributing!

Here are a few rules to follow in order to ease code reviews and merging:

- follow the coding standard of the project
- run the test suite
- write (or update) tests when applicable
- write documentation for new features
- use [commit messages that make sense](http://tbaggery.com/2008/04/19/a-note-about-git-commit-messages.html)

When creating your pull request on GitHub, please write a description which gives the context and/or explains why you are creating it.

<script src="<?php echo $DIR; ?>ressources/javascript/angular-ui/angular-ui-sortable.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular-file-upload.min.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular/composants/contact-select/contact-select.directive.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular/composants/upload/upload.directive.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular/composants/editable-content/editable-content.directive.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/javascript/angular/composants/competitor-form/competitor-form.directive.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/Opportunite/Standard/javascript/_common/angular/services.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>
<script src="<?php echo $DIR; ?>view/mvc/Opportunite/Standard/javascript/Competitors/angular/services.js?rev=<?php echo lmbconfig::getSysVersion() ?>"></script>

<?php
// @todo
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Opportunite/Standard/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Opportunite/Standard/javascript/Pipeline/_javascript_loader.php");
?>

<link rel="stylesheet" href="<?php echo $DIR; ?>view/mvc/Opportunite/Standard/css/Pipeline/Pipeline.css?rev=<?php echo lmbconfig::getSysVersion() ?>" type="text/css"/>

<div id="page-opportunite-pipeline" class="lmb-theme" ng-app="OpportuniteStandard" ng-controller="GridController">

    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading"><?php echo langage::write("ventes"); ?></div>
                <h1>Pipeline des opportunités</h1>
            </div>
            <div class="jumbotron-actions">
                <!--<div class="dropdown">
                    <button class="btn btn-white light icon-right dropdown-button" type="button">
                        Voir les objectifs
                        <i class="fa fa-chevron-down"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li ng-click="objectifs = 'mensuel'" ng-class="{'active': objectifs = 'mensuel'}">
                            Objectif mensuel
                        </li>
                        <li ng-click="objectifs = 'annuel'" ng-class="{'active': objectifs = 'annuel'}">
                            Objectif annuel
                        </li>
                    </ul>
                </div>-->
                <a href="#/page.php/Opportunite/Standard/Standard/CreationEdition" class="btn btn-white" target="_blank">
                    <i class="fa fa-plus"></i> Nouvelle opportunité
                </a>
            </div>
        </div>
    </header>

    <div class="container">

        <div class="portlet-tab style-1">
            <ul>
                <li ng-repeat="pipe in datas.pipelines" ng-class="$first ? 'selected' : ''">
                    <a qa_id="740587" data-lmb-tab="#tab-{{pipe.type.id_opportunite_type}}" class="tab-small">
                        {{pipe.type.lib}}
                    </a>
                </li>
            </ul>
        </div>

        <div id="tab-{{pipe.type.id_opportunite_type}}" class="portlet" ng-repeat="(typeOpp,pipe) in datas.pipelines track by $index" ng-class="!$first ? 'hidden' : ''">
            <div class="portlet-body" ng-class="$first ? 'selected' : ''">
                <table class="steps-container">
                    <thead>
                    <tr>
                        <th class="bg-primary" ng-repeat="step in pipe.type.steps">
                            <div qa_id="740586" class="step-title">{{ step.title }}</div>
                            <div class="arrow-right">
                                <i class="lm-angle-right2"></i>
                            </div>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                        <tr>

                            <td ng-repeat="step in pipe.type.steps">

                                <div class="step-progress" ng-show="pipe.objectif">
                                    <div class="step-progress-content" style="width:{{ getAvancementPourcentage($index, pipe.type.id_opportunite_type) }}%;"></div>
                                </div>

                                <div class="step-content">

                                    <div class="step-amount text-important" ng-show="objectifs">
                                        <span lmb-number="type:entierPos">{{ getTotalAmount($index, pipe.type.id_opportunite_type) }}</span> €
                                    </div>

                                    <ul style="padding-bottom: 60px !important;" class="card-container" ui-sortable="sortableOptionsList[$index]" ng-model="step.cards" data-id-statut="{{step.id_opportunite_statut}}">

                                        <li class="card" ng-repeat="card in step.cards">
                                            <div class="card-content">
                                                <div class="card-contact"><a href="#/page.php/Opportunite/Standard/Standard/Visualisation:{{card.id_card}}" target="_blank">{{card.contact_name}}</a></div>
                                                <div class="card-title"><a qa_id="740657" href="#/page.php/Opportunite/Standard/Standard/Visualisation:{{card.id_card}}" target="_blank">{{card.title}}</a></div>
                                                <div class="card-amount text-important">
                                                    <span lmb-currency>{{card.amount}}</span> <?php echo devise::getDefaut()->getSigle();?>
                                                </div>
                                                <div class="card-actions">
                                                    <!--<a href="#page.php/Opportunite/Standard/Standard/Visualisation" class="action btn btn-primary light icon rounded">
                                                        <i class="fa fa-search"></i>
                                                    </a>-->
                                                    <div class="dropdown action">
                                                        <button qa_id="740658" type="button" class="btn btn-tertiary light icon rounded dropdown-button">
                                                            <i class="fa fa-chevron-down"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <?php if(security::IS_SUPPORTLMB()): ?><li ng-click="manageEvent(card.id_card)">Saisir ou planifier une action</li><?php endif; ?>
                                                            <li qa_id="740659" ng-click="gestionAffaire(card.id_card, 1, pipe.type.id_opportunite_type)">Affaire gagnée</li>
                                                            <li qa_id="740660" ng-click="gestionAffaire(card.id_card, 0, pipe.type.id_opportunite_type)">Affaire perdue</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>

                                    <ul style="padding-bottom: 60px !important;" class="card-container" data-id-statut="{{step.id_opportunite_statut}}">
                                        <li class="card" ng-click="newOpp(typeOpp,step.id_opportunite_statut);">
                                            <div class="card-content">
                                                <div class="card-title text-center" style="font-size: 16px;"><i qa_id="740584" class="fa fa-plus-square"></i></div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>

<script>

    (function($){

        angular.bootstrap($("#page-opportunite-pipeline")[0], ["OpportuniteStandard"]);

    })(jQuery);

</script>
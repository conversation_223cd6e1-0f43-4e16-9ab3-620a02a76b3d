'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .controller('LivraisonsListeController', LivraisonsListeController);

    LivraisonsListeController.$inject = ['$scope', 'TransporteursLivraisonsApi','listeModesDeLivraisons','$state'];

    function LivraisonsListeController($scope, TransporteursLivraisonsApi , listeModesDeLivraisons, $state) {
        $scope.modesLivraisons = listeModesDeLivraisons.liste;

        /**
         * Sélection automatique du premier élément
         **/
        if ($scope.modesLivraisons.length && $state.current.name === 'livraisons.liste') {
            LMBNavigation.ignoreNextHashChange();
            $state.go('.spe', {idMode: $scope.modesLivraisons[0].id_livraison_mode});
        }
    }

})();
<table class="style-1">
    <?php include($DIR."view/mvc/Marketing/Animation/phtml/templates/effets/formulaire_remise.phtml");?>
    <tr>
        <td>Condition déclenchée</td>
        <td>
            <div class="input-group">
                <select qa_id="740298" ng-model="effet.id_am_condition">
                    <option
                        ng-repeat="(condition_index, condition) in groupe.cond_groupe"
                        value="{{(condition.id_am_condition ? condition.id_am_condition : condition.$$hashKey)}}"
                        ng-if="vm.filtreConditionsEnsemble(condition)"
                        ng-selected="effet.id_am_condition == condition.id_am_condition">
                        #{{condition_index + 1}} - {{condition.lib}}
                    </option>
                </select>
            </div>
        </td>
    </tr>
    <tr>
        <td></td>
        <td>
            <div class="input-group">
                <span class="input-helper-text">Appliquer l'effet </span>
                <input qa_id="740299" class="input-xxsmall" lmb-ang-number="type:entierPos" type="text" maxlength="5" ng-model="effet.nbFois"/>
                <span class="input-helper-text"> fois, tous les </span>
                <input qa_id="740300" class="input-xxsmall" lmb-ang-number="type:entierPos" type="text" maxlength="5" ng-model="effet.palier"/>
                <span class="input-helper-text"> ensembles</span>
            </div>
        </td>
    </tr>
    <tr>
        <td></td>
        <td>
            <div class="input-group">
                <span qa_id="740301" class="input-helper-text" >Nombre de répétition maximum de l'effet : </span><input class="input-xxsmall" lmb-ang-number="type:entierPosOrZero" type="text" maxlength="5" ng-model="effet.limite"/>
            </div>
        </td>
    </tr>
</table>
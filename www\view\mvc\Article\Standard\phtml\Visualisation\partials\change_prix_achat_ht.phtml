<div class="center lmb-theme">
    </br>
    
    <form  id="form-change-prix-achat-ht" action="#" method="post">
        <label><?php _e(220082,"Nouvelle valeur du produit en stock (HT) : "); ?></label>
        <input type="hidden" name="submitForm" value="1"/>
        <input type="hidden" name="idArticle" value="<?php echo $this->view->idArticle ?>"/>
        <input lmb-currency qa_id="220021" type="text" name="newPrixAchatHT" value="<?php echo $this->view->ancienPrixAchatHT ?>"/>
        <?php echo devise::getDefaut()->getSigle(); ?>
        </br></br>
        <input qa_id="220022" type="submit" class="right green_button" value='<?php _e(220083,"Valider"); ?>' />
    </form>
    <?php if (count($this->view->archives_pmp)) { ?>
        <br/>
        <hr>
        <div class="text-left bold" style="">Historique des variations de la valeur en stock</div><br>
        <table class="style-2">
            <tr>
                <th>Date</th>
                <th class="text-right">Qté</th>
                <th class="text-right">Valeur du produit en stock</th>
                <th class="text-right">Frais</th>
            </tr>
            <?php foreach($this->view->archives_pmp as $histo) { ?>
            <tr>
                <td class="text-left"><span lmb-date="type:dateTime"><?php echo $histo['date_maj'] ?></span></td>
                <td class="text-right"><span lmb-number class="text-right"><?php echo $histo['qte_totale_histo'] ?></span></td>
                <td class="text-right"><span lmb-currency="suffixe:sigle" class="text-right"><?php echo $histo['pmp_ht'] ?></span></td>
                <td class="text-right"><span lmb-currency="suffixe:sigle" class="text-right"><?php echo $histo['montant_frais'] ?></span></td>
            </tr>
            <?php } ?>
        </table>
    <?php } ?>
</div>

<script type="text/javascript">

    var modalChangePrixAchatHT = LMBModal.getInstance('modalChangePrixAchatHT');

    $j('#form-change-prix-achat-ht').submit(function(e){
        e.preventDefault();

        if(!$j.isNumeric($j("input[name=newPrixAchatHT]").val())){
            LMBTools.alert(__(220085,"Veuillez renseigner un chiffre"));
        } else {
            LMBTools.post({
                url: "page.php/Article/Standard/Visualisation/changePrixAchatHT",
                data: $j(this).serialize(),
                success: function(data){
                    if (data.statut = 'OK'){
                        modalChangePrixAchatHT.close(data);
                        LMBNavigation.refresh();
                    }
                }
            });
        }
    });
            
</script>
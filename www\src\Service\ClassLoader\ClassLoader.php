<?php

namespace App\Service\ClassLoader;

class ClassLoader {

    private string $extension = '.php';

    private array $staticAutoloader = [];

    /**
     * Creates a new <tt>ClassLoader</tt> that loads classes of the
     * specified namespace.
     *
     */
    public function __construct(
        private readonly string $namespace,
        private readonly string $path,
        private readonly bool $replaceNamespace = true
    ) {}

    public function setStaticAutoloader(array $staticAutoloader): void {
        $this->staticAutoloader = $staticAutoloader;
    }

    public function setStaticAutoloaderFile(string $staticAutoloaderFilepath): void {
        if (file_exists($staticAutoloaderFilepath)) {
            $this->staticAutoloader = require ($staticAutoloaderFilepath);
        }
    }

    public function setExtension(string $extension): ClassLoader {
        $this->extension = $extension;
        return $this;
    }

    /**
     * Installs this class loader on the SPL autoload stack.
     *
     */
    public function register(): self {
        spl_autoload_register(array($this, 'loadClass'));
        return $this;
    }

    /**
     * Uninstalls this class loader from the SPL autoloader stack.
     */
    public function unregister(): self {
        spl_autoload_unregister(array($this, 'loadClass'));
        return $this;
    }

    /**
     * Loads the given class or interface.
     *
     * @param string $className The name of the class to load.
     * @return void
     */
    public function loadClass(string $className): void {
        global $DIR;

        if (isset($this->staticAutoloader[$className])) {
            require ($DIR . $this->staticAutoloader[$className]);
            return;
        }

        $classNamePath = explode('\\', $className);
        $namespace = reset($classNamePath);

        if ($namespace !== $this->namespace) {
            return;
        }

        if ($this->replaceNamespace) {
            unset ($classNamePath[0]);
        }

        $classFilePath = $this->path .
            DIRECTORY_SEPARATOR .
            implode(DIRECTORY_SEPARATOR, $classNamePath) .
            $this->extension;

        if (file_exists($classFilePath)) {
            require ($classFilePath);
        }
    }

}
<div class="lmb-theme" lmb-config-currency="<?php echo devise::getDefaut()->getId_devise(); ?>">

    <div class="portlet">
        <div class="portlet-header">
            <h1><?php _e_html(370100, "Opération à rapprocher"); ?></h1>
        </div>
        <div class="portlet-body">
            <div class="row">
                <div class="col-1-2">
                    <table class="style-1">
                        <tbody>
                        <tr>
                            <td><?php echo langage::write("date"); ?></td>
                            <td>
                                <span lmb-date><?php echo $infos_operation->date; ?></span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <?php
                                if (empty($_REQUEST['type'])) {
                                    _e_html(370101, "Opération du relevé de banque");
                                } else {
                                    _e_html(370102, "Opération du journal de trésorerie");
                                }
                                ?>
                            </td>
                            <td>
                                <?php echo $infos_operation->lib; ?>
                            </td>
                        </tr>
                        <tr>
                            <td><?php _e_html(370103, "Débit"); ?></td>
                            <td>
                                    <span lmb-currency="suffixe: sigle" class="text-error">
                                        <?php
                                        echo ($infos_operation->montant < 0) ? abs($infos_operation->montant) : 0;
                                        ?>
                                    </span>
                            </td>
                        </tr>
                        <tr>
                            <td><?php _e_html(370104, "Crédit"); ?></td>
                            <td>
                                    <span lmb-currency="suffixe: sigle" class="text-success">
                                        <?php
                                        echo ($infos_operation->montant > 0) ? $infos_operation->montant : 0;
                                        ?>
                                    </span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <form id="form_rapprochement_journal" action="compta_compte_bancaire_rapprochement_journal_result.php" method="POST"
          target="#form_rapprochement_journal_result">

        <div class="portlet">
            <div class="portlet-header">
                <h1>
                    <?php
                    if (empty($_REQUEST['type'])) {
                        _e_html(370110, "Recherche sur le journal de trésorerie");
                    } else {
                        _e_html(370109, "Recherche sur le relevé de banque");
                    }
                    ?>
                </h1>
            </div>
            <div class="portlet-body">
                <div class="row">
                    <table class="style-1">
                        <tr>

                            <?php
                                $rapproche = array_reduce($infos_operation->rapprochements, function ($i, $rapprochement) {
                                    return $i += $rapprochement->montant_rapproche;
                                });
                                $reste_a_rapproche = abs($infos_operation->montant) - $rapproche;
                            ?>

                            <td><?php _e_html(370114, "Montant du débit"); ?></td>
                            <td style="width: 300px">
                                <div class="input-group input-group-justified input-large">
                                    <span class="highlight"><?php _e_html(370112, "De"); ?></span>
                                    <input <?php if ($infos_operation->montant < 0) {
                                        echo "disabled";
                                    } ?> qa_id="480312" type="text" name="montant_debit_debut" id="montant_debit_debut"
                                         value="<?php
                                                    if ($infos_operation->montant > 0){
                                                        echo number_format(abs(($reste_a_rapproche - 0.01)), lmbconfig::getInstance()->get('CAT_nb_decimales'), $PRICES_DECIMAL_SEPARATOR, "");
                                                    }else{
                                                        echo "";
                                                    }
                                                ?>"
                                         onkeydown="if (event.keyCode == 13) sendForm()"
                                         lmb-currency/>
                                    <span class="highlight"><?php _e_html(370113, "à"); ?></span>
                                    <input <?php if ($infos_operation->montant < 0) {
                                        echo "disabled";
                                    } ?> qa_id="480313" type="text" name="montant_debit_fin" id="montant_debit_fin"
                                         value="<?php
                                                    if ($infos_operation->montant > 0){
                                                        echo number_format(abs(($reste_a_rapproche + 0.01)), lmbconfig::getInstance()->get('CAT_nb_decimales'), $PRICES_DECIMAL_SEPARATOR, "");
                                                    }else{
                                                        echo "";
                                                    }
                                                ?>"
                                         onkeydown="if (event.keyCode == 13) sendForm()"
                                         lmb-currency/>
                                </div>
                            </td>

                            <?php if ($infos_operation->montant > 0): ?>
                                <td style="padding-left: 0">
                                    <input id="tout_debit" type="checkbox" class="checkbox tout_montant">
                                    <label for="tout_debit">Tout montant</label>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <tr>
                            <td><?php _e_html(370115, "Montant du crédit"); ?></td>
                            <td style="width: 300px">
                                <div class="input-group input-group-justified input-large">
                                    <span class="highlight"><?php _e_html(370112, "De"); ?></span>
                                    <input <?php if ($infos_operation->montant > 0) {
                                        echo "disabled";
                                    } ?> qa_id="480314" type="text" name="montant_credit_debut" id="montant_credit_debut"
                                         value="<?php echo ($infos_operation->montant < 0) ? min(number_format(abs(($reste_a_rapproche - 0.01)), lmbconfig::getInstance()->get('CAT_nb_decimales'), $PRICES_DECIMAL_SEPARATOR, ""), number_format(abs(($reste_a_rapproche + 0.01)), lmbconfig::getInstance()->get('CAT_nb_decimales'), $PRICES_DECIMAL_SEPARATOR, "")) : ""; ?>"
                                         onkeydown="if (event.keyCode == 13) sendForm()"
                                         lmb-currency/>
                                    <span class="highlight"><?php _e_html(370113, "à"); ?></span>
                                    <input <?php if ($infos_operation->montant > 0) {
                                        echo "disabled";
                                    } ?> qa_id="480315" type="text" name="montant_credit_fin" id="montant_credit_fin"
                                         value="<?php echo ($infos_operation->montant < 0) ? max(number_format(abs(($reste_a_rapproche - 0.01)), lmbconfig::getInstance()->get('CAT_nb_decimales'), $PRICES_DECIMAL_SEPARATOR, ""), number_format(abs(($reste_a_rapproche + 0.01)), lmbconfig::getInstance()->get('CAT_nb_decimales'), $PRICES_DECIMAL_SEPARATOR, "")) : ""; ?>"
                                         onkeydown="if (event.keyCode == 13) sendForm()"
                                         lmb-currency/>
                                </div>
                            </td>
                            <?php if ($infos_operation->montant < 0): ?>
                                <td style="padding-left: 0">
                                    <input id="tout_credit" type="checkbox" class="checkbox tout_montant">
                                    <label for="tout_credit">Tout montant</label>
                                </td>
                            <?php endif; ?>
                        </tr>
                        <tr>
                            <td><?php echo langage::write("periode_m"); ?></td>
                            <td style="width: 300px;">
                                <div class="input-group input-group-justified input-large">
                                    <label for="periode_debut"
                                           class="highlight"><?php echo langage::write("dum"); ?></label>
                                    <input qa_id="480316" type="text" lmb-date id="periode_debut" name="periode_debut"
                                           value="<?php echo strftime(MYSQL_DATE_FORMAT, strtotime("-" . lmbconfig::getInstance()->get("COMPTA_periode_rapprochement_auto") . " day", strtotime($infos_operation->date))); ?>"/>

                                    <label for="periode_fin"
                                           class="highlight"><?php echo langage::write("au"); ?></label>
                                    <input qa_id="480317" type="text" lmb-date id="periode_fin" name="periode_fin"
                                           value="<?php echo strftime(MYSQL_DATE_FORMAT, strtotime("+" . lmbconfig::getInstance()->get("COMPTA_periode_rapprochement_auto") . " day", strtotime($infos_operation->date))); ?>"/>
                                </div>
                            </td>
                            <td style="padding-left: 0">
                                <input id="toute_periode" type="checkbox" class="checkbox">
                                <label for="toute_periode">Toute période</label>
                            </td>
                        </tr>

                        <?php if (empty($_REQUEST['type'])) { ?>
                            <tr>
                                <td><?php echo langage::write("compta_type"); ?>:</td>
                                <td>
                                    <select name="id_operation_type" id="rap_id_operation_type" class="input-large">
                                        <?php if ($infos_operation->montant >= 0) { ?>
                                            <option value="2,4,6"><?php echo langage::write("tout"); ?></option>
                                            <option value="4-DEP"><?php echo langage::write("remises_en_banque"); ?></option>
                                            <option value="4-PRB"><?php echo langage::write("prelevements_bancaires"); ?></option>
                                            <option value="4-TNA"><?php echo langage::write("traites_na"); ?></option>
                                            <option value="6"><?php echo langage::write("telecollectes"); ?></option>
                                            <option value="2"><?php echo langage::write("virements"); ?></option>
                                            <option value="4-TRF"><?php _e_html(370120, "Virements internes"); ?></option>
                                        <?php } else { ?>
                                            <option value="1,3,2,6"><?php echo langage::write("gs__tous"); ?></option>
                                            <option value="3"><?php echo langage::write("retraits_bancaires"); ?></option>
                                            <option value="1,2"><?php echo langage::write("reglements_sortants"); ?></option>
                                            <option value="6"><?php echo langage::write("telecollectes"); ?></option>
                                            <option value="3-TRF"><?php _e_html(370119, "Virements internes"); ?></option>
                                        <?php } ?>
                                    </select>
                                </td>
                            </tr>
                        <?php } ?>

                        <?php if (!empty($_REQUEST['type'])) { ?>
                            <tr>
                                <td><?php _e_html(370111, "Libellé"); ?></td>
                                <td>
                                    <input class="input-medium" type="text" id="libelle" name="libelle"/></td>
                            </tr>
                        <?php } ?>
                    </table>
                </div>
                <input type="hidden" name="recherche_sens"
                       value="<?php echo ($infos_operation->montant > 0) ? "debit" : "credit"; ?>"/>
                <button qa_id="480318" type="submit" class="btn btn-secondary"
                        style="float: right;"><?php echo langage::write("rechercherm"); ?></button>
            </div>
        </div>

        <div class="text-right">

            <input type="hidden" name="date" id="rap_date_move"
                   value="<?php echo $infos_operation->date; ?>"/>
            <input type="hidden" name="id_compte_bancaire" id="rap_id_compte_bancaire"
                   value="<?php echo $compte_bancaire->getId(); ?>"/>
            <input type="hidden" name="id_compte_bancaire_move" id="rap_id_compte_bancaire_move"
                   value="<?php echo $infos_operation->id_compte_bancaire_move; ?>"/>
            <input type="hidden" name="id_journal" id="rap_id_compte_journal"
                   value="<?php echo $journal->getId_journal(); ?>"/>
            <input type="hidden" name="page_to_show" id="page_to_show_rap" value="1"/>
            <input type="hidden" name="type" id="type"
                   value="<?php if (!empty($_REQUEST['type'])) echo $_REQUEST['type']; ?>"/>
            <input type="hidden" name="id" id="id" value="<?php if (!empty($_REQUEST['id'])) echo $_REQUEST['id']; ?>"/>
            <input type="hidden" name="recherche" id="recherche" value="1"/>
            <input type="hidden" name="rap_id_operation_type" id="rap_id_operation_type" value=""/>

        </div>

    </form>

    <div id="form_rapprochement_journal_result"></div>

</div>

<script>

    function sendForm() {
        let $j = jQuery.noConflict();
        $j('#form_rapprochement_journal').request();
    }

    (function ($) {

        var rechercheEnCours = false;
        $('#form_rapprochement_journal').submit(function (e) {
            e.preventDefault();
            if (!rechercheEnCours) {
                rechercheEnCours = true;
                $(this).request().done(function () {
                    rechercheEnCours = false;
                });
            }
        }).submit();

        $('#toute_periode').change(function () {
            if ($(this).is(':checked')) {
                periode_debut = $('#periode_debut').val();
                periode_fin = $('#periode_fin').val();

                $('#periode_debut').val("");
                $('#periode_fin').val("");
                $('#periode_debut').next('input').prop("disabled", true);
                $('#periode_fin').next('input').prop("disabled", true);
            }
            else {
                $('#periode_debut').next('input').prop("disabled", false);
                $('#periode_fin').next('input').prop("disabled", false);
                $('#periode_debut').val(periode_debut);
                $('#periode_fin').val(periode_fin);
            }
            $('#form_rapprochement_journal').submit();
        });

        $('.tout_montant').change(function () {
            if ($('#montant_debit_debut').prop('disabled')) {
                // on cherche un crédit
                if ($(this).is(':checked')) {
                    debit_min = $('#montant_credit_debut').val();
                    debit_max = $('#montant_credit_fin').val();

                    $('#montant_credit_debut').val("");
                    $('#montant_credit_fin').val("");
                    $('#montant_credit_debut').next('input').prop("readonly", true);
                    $('#montant_credit_fin').next('input').prop("readonly", true);
                }
                else {
                    $('#montant_credit_debut').next('input').prop("readonly", false);
                    $('#montant_credit_fin').next('input').prop("readonly", false);
                    $('#montant_credit_debut').val(debit_min);
                    $('#montant_credit_fin').val(debit_max);
                }
            }
            else {
                // on cherche un débit
                if ($(this).is(':checked')) {
                    debit_min = $('#montant_debit_debut').val();
                    debit_max = $('#montant_debit_fin').val();

                    $('#montant_debit_debut').val("");
                    $('#montant_debit_fin').val("");
                    $('#montant_debit_debut').next('input').prop("readonly", true);
                    $('#montant_debit_fin').next('input').prop("readonly", true);
                }
                else {
                    $('#montant_debit_debut').next('input').prop("readonly", false);
                    $('#montant_debit_fin').next('input').prop("readonly", false);
                    $('#montant_debit_debut').val(debit_min);
                    $('#montant_debit_fin').val(debit_max);
                }
            }

            $('#form_rapprochement_journal').submit();
        });

        let periode_fin_val = null;

        $('#periode_fin').change(function () {
            if ($('#periode_fin').val() != periode_fin_val) {
                $('#form_rapprochement_journal').submit();
                periode_fin_val = $('#periode_fin').val();
            }
        });

        let periode_debut_val = null;

        $('#periode_debut').change(function () {
            if ($('#periode_debut').val() != periode_debut_val) {
                $('#form_rapprochement_journal').submit();
                periode_debut_val = $('#periode_debut').val();
            }
        });

    })(jQuery);

    //on masque le chargement
    H_loading();
</script>

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
include($DIR . "view/mvc/Compta/Restaurant/javascript/_javascript_loader.php");
?>
<link rel="stylesheet" type="text/css" href="<?= $DIR ?>view/mvc/Compta/Restaurant/css/style.css?rev=<?= lmbconfig::getInstance()->get('SYS_version') ?>">
<style>
    .showDetails{
        display: block !important;
    }
</style>

<div id="page_journal_ventes" class="lmb-theme">

    <div ng-app="ComptaRestaurant" 
        ng-controller="VentesDeliverooController as vm"
        ng-cloak>

        <header class="jumbotron">
            <div class="container">
                <div class="jumbotron-title">
                    <div class="jumbotron-heading"><?php _e_html(250011,"Comptabilité"); ?></div>
                    <h1>Ventes Deliveroo</h1>
                </div>
            </div>
        </header>

        <div class="container">

            <div class="portlet" ng-init="vm.initVentes()">
                <div class="portlet-body">
                    <div class="row p-b-5">
                        <div class="col-2-4 criteria-column">
                            <input type="text" class="hidden"
                                ng-model="vm.criteres.date_debut"
                                name="debut_periode" ng-change="vm.search()"/>
                            <input type="text" class="hidden"
                                ng-model="vm.criteres.date_fin"
                                name="fin_periode" ng-change="vm.search()"/>
                            <div lmb-date-range="periodes:true; type:dateTime"
                                linked-name-start="debut_periode"
                                linked-name-end="fin_periode" style="display:inline-flex">
                            </div>
                        </div>
                    </div>

                    <div class="main-content row">
                        <div id="graph-container" class="col-3-4 col-full-sm">
                            <div id="graph"></div>
                        </div>
                        <div id="summary-container" class="col-1-4 col-full-sm">
                            <div class="summary-stat">
                                <p class="stat-title bold">
                                    Balance du compte Deliveroo
                                </p>
                                <div class="stat-block">
                                    <div class="icon icon-ca">
                                        <img src="<?= $DIR ?>view/mvc/Compta/Standard/css/img/coin-stack.svg"/>
                                    </div>
                                    <p qa_id="740504" class="stat-main">
                                        <span lmb-ang-number="dec:2" ng-bind="vm.results.data_ventes_deliveroo.balance_compte"></span>
                                    </p>
                                    <p class="stat-detail"><?= devise::getDefaut()->getLib_devise_pluriel() ?></p>
                                </div>
                            </div>
                            <div class="summary-stat">
                                <p class="stat-title bold">
                                    <?php _e_html(510315,"Nb total de ventes"); ?>
                                </p>
                                <div class="stat-block">
                                    <div class="icon icon-ventes">
                                        <img src="<?= $DIR ?>view/mvc/Compta/Standard/css/img/tag.svg"/>
                                    </div>
                                    <p class="stat-main" ng-bind="vm.results.data_ventes_deliveroo.nb_ventes"></p>
                                    <p class="stat-detail">
                                        <?php _e_html(113218,"Ventes"); ?>
                                    </p>
                                </div>
                            </div>
                            <div class="summary-stat">
                                <p class="stat-title bold">
                                    <?php _e_html(100281, "Panier moyen"); ?>
                                </p>
                                <div class="stat-block">
                                    <div class="icon icon-panier">
                                        <img src="<?= $DIR ?>view/mvc/Compta/Standard/css/img/shopping-basket.svg"/>
                                    </div>
                                    <p class="stat-main">
                                        <span lmb-ang-number="dec:2" ng-bind="vm.results.data_ventes_deliveroo.panier_moyen"></span>
                                    </p>
                                    <p class="stat-detail"><?= devise::getDefaut()->getLib_devise_pluriel() ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

</div>

<script>

    (function($) {
        LMBTools.require({
            onReady: function () {
                var app = $("*[ng-app]");
                angular.bootstrap(app[0], [app.attr('ng-app')]);
            }
        });

    })(jQuery);

</script>
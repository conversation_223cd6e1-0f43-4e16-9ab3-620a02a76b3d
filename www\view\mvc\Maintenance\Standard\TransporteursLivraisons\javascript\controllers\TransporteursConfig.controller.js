/**
 * Controller qui concerne la page de sélection des controlleurs.
 */
'use strict';

(function () {
    angular
        .module('TransporteursLivraisonsModule')
        .controller('TransporteursConfigController', TransporteursConfigController);

    TransporteursConfigController.$inject = ['$scope', 'TransporteursLivraisonsApi', 'transporteurs', '$state' , 'commonMethodsService'];

    function TransporteursConfigController($scope, TransporteursLivraisonsApi, transporteursActifs , $state , commonMethodsService) {
        // Attribution au scope des valeurs de transporteurs, pays, langages et de la méthode updateChampEtat()
        commonMethodsService.transporteursListeCommun($scope,transporteursActifs,TransporteursLivraisonsApi);

        /**
         * Sélection automatique du premier élément
        **/
        if ($scope.transporteurs.length  && $state.current.name === 'modules') {
            LMBToast.info({
                title: "Nom des variables en BD",
                message: "Survollez les points d'interrogation à côté de chaque nom de variable pour connaître son équivalent réel en BD pour le module. Certains champs peuvent aussi être survollés pour obtenir des infos supplémentaires."
            });

            LMBNavigation.ignoreNextHashChange();
            $state.go('modules.module', {idModule: transporteursActifs.liste[0].id_transport_module});
        }
    }

})();
<?php

namespace App\Service\ClassLoader;

class StaticAutoloadBuilder {

    public function buildStaticAutoloader(string $directory, string $staticFilePath): void {
        $filePathByClassName = [];

        $allFiles = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory));
        $phpFiles = new \RegexIterator($allFiles, '/\.php$/');

        foreach ($phpFiles as $phpFile) {
            $content = file_get_contents($phpFile->getRealPath());
            $tokens = token_get_all($content);
            $namespace = '';

            for ($index = 0; isset($tokens[$index]); $index++) {
                if (!isset($tokens[$index][1])) {
                    continue;
                }

                if ($tokens[$index][1] === 'namespace') {
                    // Skip "namespace" keyword, whitespaces
                    $index += 2;

                    $namespace = [];
                    while (is_array($tokens[$index])) {
                        $namespace[] = $tokens[$index][1];
                        $index++;
                    }

                    $namespace = implode('\\', $namespace);

                    continue;
                }

                if ($tokens[$index][1] === 'class') {
                    // Skip "class" keyword, whitespaces
                    $index += 2;

                    if (!isset($tokens[$index][1])) {
                        continue;
                    }

                    $className = $tokens[$index][1];
                    if (!empty($namespace)) {
                        $className = $namespace . '\\' . $className;
                    }

                    $filePathByClassName[$className] = str_replace(
                        "\\",
                        "/",
                        $phpFile->getPathname()
                    );

                    # break if you have one class per file (psr-4 compliant)
                    # otherwise you'll need to handle class constants (Foo::class)
                    break;
                }
            }
        }

        file_put_contents($staticFilePath, "");
        $file = fopen($staticFilePath, 'w');

        fwrite($file, "<?php" . PHP_EOL);
        fwrite($file, PHP_EOL);
        fwrite($file, "/**" . PHP_EOL);
        fwrite($file, "\t* static autoload file for " . $directory . PHP_EOL);
        fwrite($file, "\t* @generatedBy " . basename(__FILE__) . PHP_EOL);
        fwrite($file, "\t* @generatedOn " . date('Y-m-d H:i:s') . PHP_EOL);
        fwrite($file, "\t* @generatedInVersion " . ($_SERVER['VERSION'] ?? 'N/A') . PHP_EOL);
        fwrite($file, " */" . PHP_EOL);
        fwrite($file, PHP_EOL);
        fwrite($file, "return array(" . PHP_EOL);

        foreach ($filePathByClassName as $className => $filePath) {
            fwrite($file, "\t'{$className}' => '{$filePath}',". PHP_EOL);
        }

        fwrite($file, ");" . PHP_EOL);

        fclose($file);
    }

}
<?php
ini_set("display_errors", 1);
error_reporting(E_ALL);
function rmdir_recursive($dir)
{
    $dir_content = scandir($dir);
    if($dir_content !== FALSE){
        foreach ($dir_content as $entry)
        {
            if(!in_array($entry, array('.','..'))){
                $entry = $dir . '/' . $entry;
                if(!is_dir($entry)){
                    unlink($entry);
                }
                else{
                    rmdir_recursive($entry);
                }
            }
        }
    }
    rmdir($dir);
}
function secure_unlink($file){

    if (is_file($file)){
        unlink ($file);
        return true;
    }
    return false;
}
set_time_limit(0);
$maj_log_file_name = $DIR."echange_lmb/maj_lmb_".$new_version."_log.txt";
$maj_log_file = fopen($maj_log_file_name, "a");
fwrite($maj_log_file,"********************************** ".date("d/m/Y - H:i")." **********************************\n");
fwrite($maj_log_file," Debut de Maj LMB vers ".$new_version."\n");
$tmp_files_dir = $DIR."echange_lmb/maj_lmb_".$new_version."/";
$GLOBALS['_ALERTES'] = array();
$GLOBALS['_INFOS']['maj_actions'][] = "<i>Ajout de tâches administrateur</i>";
$taches_admin = array();
$query_maj_sql = array();
$query_maj_sql_ne = array();
$pdo = new PDO("mysql:host=$bdd_hote;dbname=$bdd_base", $bdd_user, $bdd_pass, array(PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8;"));
$pdo->setAttribute (PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
$pdo->setAttribute (PDO::ATTR_EMULATE_PREPARES, true);
$pdo->setAttribute (PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
$query = "DROP PROCEDURE IF EXISTS `lmb_addcolumn`";
$pdo->exec($query);
$query = "create procedure lmb_addcolumn(IN tbl TEXT, IN colone TEXT, IN alt TEXT)
begin
    IF NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
            AND convert(COLUMN_NAME USING utf8) = convert(colone USING utf8)
            AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
        SET @Sql = alt;
        PREPARE STMT FROM @Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    END IF;
end";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `lmb_dropcolumn`";
$pdo->exec($query);
$query = "CREATE PROCEDURE lmb_dropcolumn(IN tbl TEXT, IN col TEXT)
BEGIN
    IF EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
        WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
            AND convert(COLUMN_NAME USING utf8) = convert(col USING utf8)
            AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
        SET @Sql := concat( 'ALTER TABLE ', convert(tbl USING utf8), ' DROP COLUMN ', convert(col USING utf8) );
        PREPARE STMT FROM @Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    END IF;
END";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `lmb_renamecolumn`";
$pdo->exec($query);
$query = "create procedure lmb_renamecolumn(IN tbl TEXT, IN oldcolone TEXT, IN newcolone TEXT, IN alt TEXT)
    begin
        IF NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
                AND convert(COLUMN_NAME USING utf8) = convert(newcolone USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci)
            AND EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
                AND convert(COLUMN_NAME USING utf8) = convert(oldcolone USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
            SET @Sql = alt;
            PREPARE STMT FROM @Sql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END IF;
    end";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `lmb_renametable`";
$pdo->exec($query);
$query = "create procedure lmb_renametable(IN oldtbl TEXT, IN newtbl TEXT, IN alt TEXT)
    begin
        IF NOT EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(newtbl USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci)
            AND EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
            WHERE convert(TABLE_NAME USING utf8) = convert(oldtbl USING utf8)
                AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
            SET @Sql = alt;
            PREPARE STMT FROM @Sql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END IF;
    end";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `DropFK_Name`";
$pdo->exec($query);
$query = "CREATE PROCEDURE DropFK_Name (
IN parm_table_name VARCHAR(100),
IN parm_key_name VARCHAR(100)
)
BEGIN
-- Verify the foreign key exists
IF EXISTS (SELECT NULL FROM information_schema.TABLE_CONSTRAINTS WHERE CONSTRAINT_SCHEMA = DATABASE() AND CONSTRAINT_NAME = convert(parm_key_name USING utf8) COLLATE utf8_unicode_ci) THEN
-- Turn the parameters into local variables
set @ParmTable = parm_table_name ;
set @ParmKey = parm_key_name ;
-- Create the full statement to execute
set @StatementToExecute = concat('ALTER TABLE ',@ParmTable,' DROP FOREIGN KEY ',@ParmKey);
-- Prepare and execute the statement that was built
prepare DynamicStatement from @StatementToExecute ;
execute DynamicStatement ;
-- Cleanup the prepared statement
deallocate prepare DynamicStatement ;
END IF;
END";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `DropFK`";
$pdo->exec($query);
$query = "CREATE PROCEDURE DropFK ( IN table_name VARCHAR(100), IN column_name VARCHAR(100), IN referenced_table VARCHAR(100), IN referenced_column VARCHAR(100) )
    BEGIN
        DECLARE done INT DEFAULT 0;
        DECLARE key_table_name VARCHAR(100);
        DECLARE key_name VARCHAR(100);
        DECLARE curseur1 CURSOR FOR SELECT k.TABLE_NAME,k.CONSTRAINT_NAME
                                        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE AS k
                                        INNER JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS AS c
                                        ON k.CONSTRAINT_SCHEMA = c.CONSTRAINT_SCHEMA AND k.CONSTRAINT_NAME = c.CONSTRAINT_NAME
                                        WHERE c.CONSTRAINT_TYPE = 'FOREIGN KEY'
                                        AND k.CONSTRAINT_SCHEMA = DATABASE()
                                        AND k.TABLE_NAME = convert(table_name USING utf8) COLLATE utf8_unicode_ci
                                        AND k.COLUMN_NAME = convert(column_name USING utf8) COLLATE utf8_unicode_ci
                                        AND k.REFERENCED_TABLE_NAME = convert(referenced_table USING utf8) COLLATE utf8_unicode_ci
                                        AND k.REFERENCED_COLUMN_NAME = convert(referenced_column USING utf8) COLLATE utf8_unicode_ci;

        DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

        OPEN curseur1;
        REPEAT
                FETCH curseur1 INTO key_table_name,key_name;
                IF done = 0 THEN
                        SET @SQL := concat('ALTER TABLE ',key_table_name,' DROP FOREIGN KEY ',key_name);
                        PREPARE stmt FROM @SQL;
                        EXECUTE stmt;
                        DEALLOCATE PREPARE stmt;
                END IF;
        UNTIL done
        END REPEAT;
        CLOSE curseur1;
    END";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `AddFK`";
$pdo->exec($query);
$query = "CREATE PROCEDURE AddFK ( IN parm_table_name VARCHAR(100), IN parm_key_name VARCHAR(100), IN alt TEXT)
  BEGIN
    IF NOT EXISTS (SELECT NULL
                    FROM information_schema.TABLE_CONSTRAINTS
                    WHERE CONSTRAINT_SCHEMA = DATABASE()
                        AND CONSTRAINT_NAME = convert(parm_key_name USING utf8) COLLATE utf8_unicode_ci
                        AND TABLE_NAME = convert(parm_table_name USING utf8) COLLATE utf8_unicode_ci) THEN
      SET @SQL := alt;
      PREPARE stmt FROM @SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
    END IF;
  END";
$pdo->exec($query);
$query = "DROP PROCEDURE IF EXISTS `DropIndex`";
$pdo->exec($query);
$query = "CREATE PROCEDURE DropIndex ( IN table_name VARCHAR(100), IN columns_names VARCHAR(100) )
	BEGIN
		DECLARE done INT DEFAULT 0;
		DECLARE index_table_name VARCHAR(100);
		DECLARE index_name VARCHAR(100);
		DECLARE columns_concat VARCHAR(100);
		DECLARE curseur1 CURSOR FOR SELECT k.TABLE_NAME,k.INDEX_NAME, GROUP_CONCAT(k.COLUMN_NAME ORDER BY k.COLUMN_NAME ASC) AS columns
										FROM INFORMATION_SCHEMA.STATISTICS AS k
										WHERE k.TABLE_SCHEMA = DATABASE()
										AND k.TABLE_NAME = convert(table_name USING utf8) COLLATE utf8_unicode_ci
										AND k.INDEX_NAME != 'PRIMARY'
										GROUP BY k.TABLE_NAME,k.INDEX_NAME
										HAVING columns = convert(columns_names USING utf8) COLLATE utf8_unicode_ci;

        DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

        OPEN curseur1;
        REPEAT
                FETCH curseur1 INTO index_table_name,index_name,columns_concat;
                IF done = 0 THEN
						SET @SQL := concat('ALTER TABLE ',index_table_name,' DROP INDEX ',index_name);
                        SELECT @SQL;
						PREPARE stmt FROM @SQL;
						EXECUTE stmt;
						DEALLOCATE PREPARE stmt;
                END IF;
        UNTIL done
        END REPEAT;
        CLOSE curseur1;
	END";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `AddIndex`";
$pdo->exec($query);
$query = "CREATE PROCEDURE AddIndex ( IN nom_table VARCHAR(100), IN nom_index VARCHAR(100), IN liste_colonnes VARCHAR(100) )
  BEGIN

    DECLARE existant INT DEFAULT 1;

    SELECT count(1) INTO existant FROM (
        SELECT 
            k.TABLE_NAME,
            k.INDEX_NAME, 
            GROUP_CONCAT(k.COLUMN_NAME ORDER BY k.COLUMN_NAME ASC) AS columns
        FROM INFORMATION_SCHEMA.STATISTICS AS k
        WHERE k.TABLE_SCHEMA = DATABASE()
              AND k.TABLE_NAME = convert(nom_table USING utf8) COLLATE utf8_unicode_ci
              AND INDEX_NAME = convert(nom_index USING utf8)
        GROUP BY k.TABLE_NAME,k.INDEX_NAME
        HAVING columns = convert(liste_colonnes USING utf8) COLLATE utf8_unicode_ci
    ) as indexes;


    IF existant = 0 THEN

      SET @SQL := concat('ALTER TABLE ',nom_table,' ADD INDEX ',nom_index,' (',liste_colonnes,')');
      SELECT @SQL;
      PREPARE stmt FROM @SQL;
      EXECUTE stmt;
      DEALLOCATE PREPARE stmt;
    END IF;

  END";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `If_col_exists`";
$pdo->exec($query);
$query = "  CREATE PROCEDURE If_col_exists (IN tbl VARCHAR (100),  IN col VARCHAR (100), IN alt TEXT)
  BEGIN
  IF EXISTS(SELECT * FROM INFORMATION_SCHEMA.COLUMNS
    WHERE convert(TABLE_NAME USING utf8) = convert(tbl USING utf8)
          AND convert(COLUMN_NAME USING utf8) = convert(col USING utf8)
          AND convert(TABLE_SCHEMA USING utf8) = convert(DATABASE() USING utf8) COLLATE utf8_unicode_ci) THEN
    SET @Sql = alt;
    PREPARE STMT FROM @Sql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;
  END IF;

  END";
$pdo->exec($query);

$query = "DROP PROCEDURE IF EXISTS `AddUniqueIndex`";
$pdo->exec($query);
$query = "  CREATE PROCEDURE AddUniqueIndex ( IN nom_table VARCHAR(100), IN nom_index VARCHAR(100), IN liste_colonnes VARCHAR(100) )
	BEGIN

    DECLARE existant INT DEFAULT 1;

	SET @nom_table = nom_table;
	SET @nom_index = nom_index;
	SET @liste_colonnes = liste_colonnes;

	SET @S1 = 'SELECT count(1) INTO @existant FROM (
        SELECT
            k.TABLE_NAME,
            k.INDEX_NAME,
            GROUP_CONCAT(k.COLUMN_NAME ORDER BY k.COLUMN_NAME ASC) AS columns
        FROM INFORMATION_SCHEMA.STATISTICS AS k
        WHERE k.TABLE_SCHEMA = DATABASE()
              AND k.TABLE_NAME = convert(? USING utf8) COLLATE utf8_unicode_ci
              AND INDEX_NAME = convert(? USING utf8)
        GROUP BY k.TABLE_NAME,k.INDEX_NAME
        HAVING columns = convert(? USING utf8) COLLATE utf8_unicode_ci
    ) as indexes';


	PREPARE stmt1 FROM @S1;
	EXECUTE stmt1 USING @nom_table, @nom_index, @liste_colonnes;

	SET existant := @existant;

	DEALLOCATE PREPARE stmt1;


    IF existant = 0 THEN

      SET @SQL := concat('ALTER TABLE ',nom_table,' ADD UNIQUE INDEX ',nom_index,' (',liste_colonnes,')');
      PREPARE stmt FROM @SQL;
      EXECUTE stmt;
      DEALLOCATE PREPARE stmt;
    END IF;


  END";
$pdo->exec($query);

lmbconfig::$silent_mode=true;
// *!*!*!*!*!*!*!*!*!* A EXECUTER AVANT LE BASE UPDATE 1 *!*!*!*!*!*!*!*!*!*\\

// *!*!*!*!*!*!*!*!*!* BASE UPDATE *!*!*!*!*!*!*!*!*!*\\
$query_maj_sql = maj_serveur::_parse_sql_file($tmp_files_dir."maj.sql");
try{
    $pdo->beginTransaction();

    foreach ($query_maj_sql as $q) {
        $current_query = $q;
        $pdo->query(trim($q));
    }
    $pdo->commit();
}
catch(Exception $e){
    $pdo->rollback();
    fwrite($maj_log_file,"Erreur lors de l'execution des requetes SQL 1:\n");
    fwrite($maj_log_file,"Erreur N°:".$e->getCode()."\n");
    fwrite($maj_log_file,"Erreur :".$e->getMessage()."\n");
    fwrite($maj_log_file,"Fichier :".$e->getFile()."\n");
    fwrite($maj_log_file,"Ligne :".$e->getLine()."\n");
    fwrite($maj_log_file,"Trace :".$current_query."\n");
    alerte_dev("Erreur lors de la mise à jour de la base de données.");
}
lmbconfig::$silent_mode=true;
// *!*!*!*!*!*!*!*!*!* A EXECUTER APRES LE BASE UPDATE 1 *!*!*!*!*!*!*!*!*!*\\

$plan_lmb_starter = \LMBCore\Licences\LicencePlan::getInstanceByRef("lmb_starter");
$plan_lmb_essentiel = \LMBCore\Licences\LicencePlan::getInstanceByRef("lmb_essentiel");
$plan_lmb_pro = \LMBCore\Licences\LicencePlan::getInstanceByRef("lmb_pro");
$plan_lmb_entreprise = \LMBCore\Licences\LicencePlan::getInstanceByRef("lmb_entreprise");
$plan_borc_starter = \LMBCore\Licences\LicencePlan::getInstanceByRef("borc_starter");
$plan_borc_standard = \LMBCore\Licences\LicencePlan::getInstanceByRef("borc_standard");
$plan_borc_plus = \LMBCore\Licences\LicencePlan::getInstanceByRef("borc_plus");
$plan_boak_starter = \LMBCore\Licences\LicencePlan::getInstanceByRef("boak_starter");
$plan_boak_standard = \LMBCore\Licences\LicencePlan::getInstanceByRef("boak_standard");
$plan_boak_plus = \LMBCore\Licences\LicencePlan::getInstanceByRef("boak_plus");

function setLicencePlanValeur(\LMBCore\Licences\LicenceFonctionnalite $fonctionnalite, \LMBCore\Licences\LicencePlan $plan, $valeur) {
    $valeur_std = \LMBCore\Licences\LicencePlanValeur::getInstanceByFonctionnalitePlan($fonctionnalite->getId(), $plan->getId());
    $valeur_std->setId_licence_plan($plan->getId());
    $valeur_std->setId_licence_fonctionnalite($fonctionnalite->getId());
    $valeur_std->setValeur_standard($valeur);
    $valeur_std->save();
}

function setLicencePlanValeurBulk (\LMBCore\Licences\LicenceFonctionnalite $fonctionnalite, array $plans_non, array $plans_oui) {
    foreach ($plans_non as $plan) {
        setLicencePlanValeur($fonctionnalite, $plan, 0);
    }

    foreach ($plans_oui as $plan) {
        setLicencePlanValeur($fonctionnalite, $plan, 1);
    }
}

function addLicenceFonctionnalite ($ref, $lib, $group, $plans_non, $plans_oui) {
    $fonctionnalite = \LMBCore\Licences\LicenceFonctionnalite::getInstanceByRef($ref);
    $fonctionnalite->setRef_licence_fonctionnalite($ref);
    $fonctionnalite->setId_licence_logiciel(\LMBCore\Licences\LicenceLogiciel::getCacheInstanceByRef('lmb')->getId());
    $fonctionnalite->setGroupe($group);
    $fonctionnalite->setLib($lib);
    $fonctionnalite->setArchive(0);
    $fonctionnalite->save();

    setLicencePlanValeurBulk($fonctionnalite, $plans_non, $plans_oui);
}

addLicenceFonctionnalite("encaissement.cashless", "Cashless", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_borc_standard,
        $plan_boak_starter,
        $plan_boak_standard,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
        $plan_borc_plus,
        $plan_boak_plus,
    ]
);

addLicenceFonctionnalite("encaissement.mobile_money", "Mobile  Money", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ]
);

addLicenceFonctionnalite("encaissement.fid", "Point fidélité", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ]
);

addLicenceFonctionnalite("encaissement.avoirs_fournisseur", "Création avoir fournisseur", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.compensation_fournisseur", "Compensation Fournisseur", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.virement_bancaire", "Virement bancaire", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.prelevement_bancaire", "Prélèvement bancaire", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.transfert_tiers", "Transfert depuis tiers", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.impaye", "Impayé", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.traite_acceptee", "Traite Acceptée", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.traite_non_acceptee", "Traite Non acceptée", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.rendu_espece", "Rendu espèces", "Gestion des encaissements",
    [
    ], [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.rendu_cheque", "Rendu chèque", "Gestion des encaissements",
    [
    ], [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.trop_percu_conserve", "Trop perçu conservé", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
    ], [
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.non_rembourse", "Non remboursé", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
    ], [
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

addLicenceFonctionnalite("encaissement.compensation_client", "Compensation Client", "Gestion des encaissements",
    [
        $plan_borc_starter,
        $plan_boak_starter,
        $plan_borc_standard,
        $plan_boak_standard,
        $plan_borc_plus,
        $plan_boak_plus,
    ], [
        $plan_lmb_starter,
        $plan_lmb_essentiel,
        $plan_lmb_pro,
        $plan_lmb_entreprise,
    ]
);

\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_lmb_starter->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_lmb_essentiel->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_lmb_pro->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_lmb_entreprise->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_borc_starter->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_borc_standard->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_borc_plus->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_boak_starter->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_boak_standard->getId());
\LMBCore\Licences\LicenceFonctionnaliteValeurLocale::populateValeursLocales($plan_boak_plus->getId());

\LMBCore\Licences\AbstractSessionManager::discard();


// *!*!*!*!*!*!*!*!*!**!*!*!*!*!*!*!*!*!*\\
fwrite($maj_log_file," Fin de Maj LMB vers ".$new_version."\n");
fclose($maj_log_file);
if (function_exists("opcache_reset")) opcache_reset();

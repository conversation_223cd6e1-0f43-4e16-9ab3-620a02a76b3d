<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
$files = include($DIR . "view/mvc/BackOfficeAK/TastyCloud/javascript/_vue_loader.php");
?>

<link rel="stylesheet" href="<?php echo $DIR . 'view/mvc/BackOfficeAK/TastyCloud/css/main.css'; ?>">

<div class="lmb-theme">
    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading">
                    <?php _e_html(180769, "Catalogue"); ?>
                </div>
                <h1>
                    <?php _e_html(182943,"Envoyer sur les parcours digitaux"); ?>
                </h1>
            </div>
            <div class="jumbotron-actions">
                <a href="#autres_parametres.php" class="btn btn-white light">
                    <i class="fa fa-arrow-left"></i>
                    <?php _e_html(340644, "Retour au menu principal"); ?>
                </a>
            </div>
        </div>
    </header>

    <div class="container" id="vue-app">
        <div class="portlet-tab style-1">
            <ul>
                <li class="selected">
                    <a data-lmb-tab="#push">
                        <?php _e(182944,"Push catalogue"); ?>
                    </a>
                </li>
                <!-- <li>
                    <a data-lmb-tab="#histo">
                        <?php _e_html(182945,"Historique"); ?>
                    </a>
                </li> -->
            </ul>
        </div>

        <!-- Formulaire de push -->
        <form class="portlet" id="push" @submit.prevent="pushCatalog">
            <div class="portlet-header">
                <h1>
                    <?php _e_html(182946,"Push du catalogue"); ?>
                </h1>
            </div>
            <div class="portlet-body">
                <table class="style-1">
                    <tr>
                        <td>
                            <?php _e_html(182947,"Catalogue"); ?>
                        </td>
                        <td>
                            <select v-model="push.catalog_id">
                                <option v-for="catalog in liste_catalogues"
                                    :value="catalog.id_catalogue" :key="catalog.id_catalogue">
                                    {{ catalog.lib }}
                                </option>
                            </select>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <?php _e_html(182949,"Destinataire"); ?>
                        </td>
                        <td>
                            <select v-model="push.channel">
                                <option v-for="(label, ref) in liste_channels"
                                    :value="ref" :key="ref">
                                    {{ label }}
                                </option>
                            </select>
                        </td>
                    </tr>
                    <tr v-if="['click_n_collect', 'commande_table'].indexOf(push.channel) != -1">
                        <td>
                            <?php _e_html(182960,"Restaurant"); ?>
                        </td>
                        <td>
                            <select v-model="push.id_magasin">
                                <option v-for="magasin in liste_magasins"
                                    :value="magasin.id_magasin" :key="magasin.id_magasin">
                                    {{ magasin.lib_magasin }}
                                </option>
                            </select>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="portlet-footer">
                <button class="btn btn-primary" type="submit">
                    <?php _e_html(182950,"Valider"); ?>
                </button>
            </div>
        </form>

        <!-- Historique !-->
        <!-- <div id="histo" class="portlet hidden">
            <div class="portlet-body">
                <p>Tab 2 content</p>
            </div>
        </div> -->



        
    </div>
</div>

<script type="module">
    /*
     * @TODO - Standardiser ce processus d'import
     */
    import { PushCatalogueController } from '<?= $files['PushCatalogue.js'] ?>';
    
    const vueApp = createApp(PushCatalogueController);
    vueApp.directive('init', window.VueDirectives.init)
        .directive('lmbNumber', window.VueDirectives.lmbNumber)
        .directive('lmbDate', window.VueDirectives.lmbDate)
        .directive('date-range', window.VueDirectives.dateRange);
    vueApp.use(VueComponents.ElementPlus).mount('#vue-app');
</script>
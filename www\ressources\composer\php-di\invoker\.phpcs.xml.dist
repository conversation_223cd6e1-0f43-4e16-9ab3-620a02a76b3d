<?xml version="1.0"?>
<ruleset>
    <arg name="basepath" value="."/>
    <arg name="extensions" value="php"/>
    <arg name="cache" value=".phpcs-cache"/>
    <!-- Show sniff names -->
    <arg value="s"/>

    <file>src</file>

    <rule ref="HardMode"/>

    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHint.MissingNativeTypeHint">
        <exclude-pattern type="relative">src/ParameterResolver/ParameterResolver.php</exclude-pattern>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming.SuperfluousSuffix">
        <severity>0</severity>
    </rule>
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousExceptionNaming.SuperfluousSuffix">
        <severity>0</severity>
    </rule>

</ruleset>

<?php
include($DIR . "view/mvc/javascript/_javascript_loader.php");
$files = include($DIR . "view/mvc/BackOfficeAK/RestaurantDigital/javascript/_vue_loader.php");
?>

<div class="lmb-theme">
    <header class="jumbotron">
        <div class="container">
            <div class="jumbotron-title">
                <div class="jumbotron-heading">
                    <?php _e_html(510495,"Paramètres"); ?>
                </div>
                <h1>
                    Deliveroo
                </h1>
            </div>
            <div class="jumbotron-actions">
                <a href="#autres_parametres.php" class="btn btn-white light">
                    <i class="fa fa-arrow-left"></i>
                    <?php _e_html(340644, "Retour au menu principal"); ?>
                </a>
            </div>
        </div>
    </header>

    <div class="container" id="vue-app">
        <form class="portlet" @submit.prevent="saveModuleConfig"
            :class="!module.params.actif ? 'no-inner-line' : ''">
            <div class="portlet-header">
                <h1>
                    <?php _e_html(182841,"Activer le module"); ?>
                    Deliveroo
                </h1>
                <div class="portlet-actions">
                    <input type="checkbox"
                        v-model="module.params.actif"
                        @change="toggleModule"
                        true-value="1"
                        false-value="0"
                        class="checkbox-switch"
                    />
                </div>
            </div>
            <div class="portlet-body" v-if="module.params.actif == 1" v-cloak>
                <h2>
                    <?php _e_html(182975,"ID des restaurants sur Deliveroo"); ?>
                </h2>
                <table class="style-1 auto-layout settings-table" v-cloak>
                    <tr v-for="magasin in liste_magasins" :key="magasin.id_magasin">
                        <td>
                            {{ magasin.lib_magasin }}
                        </td>
                        <td>
                            <input type="text" class="input-medium"
                                v-model="module.params.location_ids[magasin.id_magasin]"
                                @input="locations_edited = true"
                            />
                        </td>
                    </tr>
                </table>

                <?php
                    // Si on est en mode support, on affiche le sélecteur d'environnement
                    if(security::IS_SUPPORTLMB()) {
                ?>
                    <h2>Environnement</h2>
                    <select v-model="module.params.environment">
                        <option value="sandbox">Sandbox</option>
                        <option value="production">Production</option>
                    </select>
                <?php
                    }
                ?>
            </div>

            <div class="portlet-footer">
                <button class="btn btn-primary" type="submit">
                    <?php _e_html(180920,"Sauvegarder"); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script type="module">
    /*
     * @TODO - Standardiser ce processus d'import
     */
    import { DeliverooController } from '<?= $files['Deliveroo.js'] ?>';
    
    const vueApp = createApp(DeliverooController);
    vueApp.use(VueComponents.ElementPlus).mount('#vue-app');
</script>
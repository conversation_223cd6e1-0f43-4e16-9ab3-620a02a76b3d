'use strict';

(function () {
    angular
        .module('AkPadManager')
        .controller('IndexController', IndexController);

    IndexController.$inject = ['$scope', 'AkPadApi', 'LMBModalService'];

    LMBLangage.preloadTags([121325]);

    function IndexController($scope, AkPadApi, LMBModalService) {
        const DEFAULT_LICENCE = 'akpad_standard';

        $scope.ak_pads = [];
        $scope.loading = true;

        $scope.init = function () {
            AkPadApi.getList().then((res) => {
                if (res.data) {
                    $scope.ak_pads = res.data;
                    $scope.loading = false;
                } else {
                    LMBToast.error({
                        title: __(580957,"Une erreur est survenue"),
                        message: __(521850,"La liste des pads n'a pas pu être chargée")
                    });
                }
            });
        };

        let addNewShowCreationModal = function () {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(520474, "Création d'un Pad"),
                    drag: true,
                    resizable: false,
                    overlay: true,
                    style: {width: "750px", height: "250px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:AkPad/phtml/popup/pad.phtml",
                        controller: "popupAkPadController",
                        inputs: {
                            pad: null
                        }
                    },
                    then: function () {
                    },
                    onClose: function (result) {
                        $scope.ak_pads.push(result);
                    }
                }
            });
        };

        let addNewShowAlertMoreLicenceModal = function () {
            let lmb_modal = new LMBModal("popup_alerte_add_more_licence", {
                titre: __(121325, "Souscription au service payant"),
                overlay: true,
                style: {width: "645px", height: "260px"},
                callback: function (confirmed) {
                    if (confirmed === true) {
                        addNewShowCreationModal();
                    }
                }
            });
            lmb_modal.request(
                "page.php/Licence/Standard/LicenceRight/popupAlerteAddMore"
            );
            lmb_modal.open();
        }

        let addNewShowImpossibleModal = function () {
            let lmb_modal = new LMBModal("popup_bloquer_add_more_licence", {
                titre: __(121325, "Souscription au service payant"),
                overlay: true,
                style: {width: "645px", height: "260px"},
                onClose: function () {
                }
            });
            lmb_modal.request(
                "page.php/Licence/Standard/LicenceRight/popupBloquerAddMore",
                {'ref_licence': DEFAULT_LICENCE}
            );
            lmb_modal.open();
        }

        $scope.addNew = function () {
            LMBTools.post({
                url: 'page.php/Licence/Standard/LicenceRight/getLicenceRight',
                dataType: "json",
                data: {
                    'ref_licence': DEFAULT_LICENCE
                },
                success: function (response) {
                    let licence_right = response.licence_right;
                    if (licence_right.qte_used < licence_right.qte_dispo) {
                        addNewShowCreationModal();
                    } else if (licence_right.can_add_more && licence_right.qte_used < licence_right.qte_max) {
                        addNewShowAlertMoreLicenceModal();
                    } else {
                        addNewShowImpossibleModal();
                    }
                }
            });

        };

        $scope.desync = function(id_ak_pad, index, statut_sync) {
            if (["Synchronisé", "Synchronisation initiale en cours"].indexOf(statut_sync) == -1) {
                return;
            }

            LMBTools.confirm({
                content: __(521851,"Voulez-vous vraiment désynchroniser le Pad ?"),
                onValid: function(confirm) {
                    if(confirm) {
                        let data = {"id_pad" : id_ak_pad};
                        AkPadApi.desync(data).then(function(response){
                            if(response.pad){
                                $scope.ak_pads[index] = response.pad;
                                LMBToast.success({
                                    title: __(521852,"Pad désynchronisé")
                                });
                            }else if(response.error){
                                LMBToast.error({
                                    title: __(580957,"Une erreur est survenue"),
                                    message: __(521853,"La désynchronisation du Pad a échouée.")
                                });
                            }
                        });
                    }
                }
            });
        }

        $scope.delete = function(id_ak_pad, index){
            LMBTools.confirm({
                content: __(521854,"Voulez-vous vraiment supprimer ce Pad ?"),
                onValid: function(confirm) {
                    if(confirm) {
                        let data = {"id_pad" : id_ak_pad};
                        AkPadApi.delete(data).then(function(response){
                            if(response.success){
                                $scope.ak_pads.splice(index, 1);
                                LMBToast.success({
                                    title: __(521856,"Pad supprimé")
                                });
                            }else if(response.error){
                                LMBToast.error({
                                    title: __(580957,"Une erreur est survenue"),
                                    message: __(521855,"La suppression du Pad a échouée.")
                                });
                            }
                        });
                    }
                }
            });
        }

        $scope.updatePad = function(pad) {
            LMBModalService.showModal({
                LMBModalConfig: {
                    titre: __(182512,"Modification du pad"),
                    drag: true,
                    resizable: false,
                    overlay: true,
                    style: {width: "750px", height: "250px"}
                },
                angularModal: {
                    config: {
                        templateUrl: "page.php/Components/Standard/Template/View:AkPad/phtml/popup/pad.phtml",
                        controller: "popupAkPadController",
                        inputs: {
                            pad: angular.copy(pad)
                        }
                    },
                    then: function () {
                    },
                    onClose: function (result) {
                        angular.forEach(result, (value, key) => {
                            pad[key] = value;
                        });
                    }
                }
            });
        }
    }

})();
let PushCatalogueController = {
    components: {},
    data() {
        return {
            // Vraies variables
            API_BASE: 'page.php/BackOfficeAK/Standard/TastyCloud',
            push: {
                catalog_id: null,
                channel: null,
                id_magasin: null
            },
            push_histo: [],
            liste_catalogues: [],
            liste_channels: [],
            liste_magasins: [],
            edited: false // Passe à true si on a modifié des champs
        }
    },
    mounted() {
        this.init();
    },

    computed: {
    },

    methods: {
        // Fonctions appelées par les update des composants Angular
        init() {
            const url = `${this.API_BASE}/loadPushData`
            const that = this

            LMBTools.post({
                url,
                data: {},
                dataType: 'json',
                success: function (response) {
                    if(that._hasError(response)) {
                        return;
                    }
                    that.liste_catalogues = response.liste_catalogues || []
                    that.liste_channels = response.liste_channels || {}
                    that.liste_magasins = response.liste_magasins || []

                    if(that.liste_catalogues?.length) {
                        that.push.catalog_id = that.liste_catalogues[0].id_catalogue
                    }
                    if(Object.keys(that.liste_channels)?.length) {
                        that.push.channel = Object.keys(that.liste_channels)[0]
                    }
                }
            })
        },
        pushCatalog() {
            const data = { ...this.push };
            const url = `${this.API_BASE}/pushCatalog`
            const that = this

            LMBTools.post({
                url,
                data,
                dataType: 'json',
                success: function (response) {
                    if(!that._hasError(response)) {
                        LMBToast.success({
                            title: "Le catalogue a été envoyé au destinataire"
                        });
                    }
                }
            });
        },
        _hasError(response = {}) {
            if(typeof response.status == "undefined" || response.status == "ko") {
                if(response.error) {
                    LMBToast.error({
                        title: __(340842,"Problème technique"),
                        message: response.error
                    });
                } else {
                    LMBToast.errorTechnique();
                }
                return true;
            }
            return false;
        },
    },

    watch: {
        module: {
            handler() {
                this.edited = true
            },
            deep: true
        }
    }

};

export { PushCatalogueController }
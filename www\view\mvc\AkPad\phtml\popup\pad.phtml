<div id="ak-pad-popup" class="lmb-theme">
    <table class="style-1 row">
        <tr>
            <td><?php _e_html(520475,"Libellé : "); ?></td>
            <td>
                <input type="text" class="input-full" ng-model="pad.lib">
            </td>
        </tr>
        <tr>
            <td><?php _e_html(520476,"Référence interne : "); ?></td>
            <td>
                <input type="text" class="input-full" ng-model="pad.ref_interne">
            </td>
        </tr>
        <tr>
            <td>
                <input  type="checkbox" class="checkbox-switch" ng-model="pad.configuration"  ng-true-value="'1'" ng-false-value="'0'"/>
            </td>
            <td qa_id="480395">
                <span  >
                    <?php _e_html(710666,"Imprimer sur l’imprimante associée au Pad AirKitchen"); ?>
                </span>
            </td>
        </tr>
        <tr ng-if="pad.configuration == '1'">
            <td style="border-bottom: none; padding-left: 0px; width: 40%;">
                <span ><?php _e_html(670022,"Sélection des imprimantes"); ?></span>
            </td>
            <td >
                <div class="input-large">
                    <lmb-dropdown-checkbox
                        ng-if="config.imprimantes"
                        options="config.imprimantes"
                        ng-model="pad.imprimantes"
                        config="{useSearch: true}">
                    </lmb-dropdown-checkbox>
                </div>
            </td>
        </tr>
    </table>
    <div class="modal-footer">
        <div ng-show="loadingPage" class="left page-loading"><span class="spinner big"></span></div>
        <button class="btn btn-tertiary" ng-click="save()">
            <span ng-if="!pad.id_ak_pad">
                <?php _e_html(520477,"Création"); ?>
            </span>
            <span ng-if="pad.id_ak_pad">
                <?php _e_html(182515,"Modification"); ?>
            </span>
        </button>
    </div>
</div>
